"""
用户CRUD操作
"""
from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_
from sqlalchemy.orm import selectinload
from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import verify_password


class UserCRUD(CRUDBase[User, UserCreate, UserUpdate]):
    """用户CRUD操作类"""

    def __init__(self, db: AsyncSession):
        super().__init__(User)
        self.db = db

    async def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名查询用户（用于登录验证）"""
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.username == username)
        )
        return result.scalar_one_or_none()

    async def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱查询用户（用于邮箱唯一性校验）"""
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.email == email)
        )
        return result.scalar_one_or_none()

    async def list(
        self,
        keyword: Optional[str] = None,
        status: Optional[str] = None,
        is_active: Optional[bool] = None,
        offset: int = 0,
        limit: int = 10
    ) -> Tuple[List[User], int]:
        """查询用户列表（带筛选和分页）"""
        # 基础查询
        query = select(User).options(selectinload(User.roles))

        # 筛选条件
        if keyword:
            query = query.where(
                or_(
                    User.username.ilike(f"%{keyword}%"),
                    User.email.ilike(f"%{keyword}%"),
                    User.nickname.ilike(f"%{keyword}%")
                )
            )

        if status:
            query = query.where(User.status == status)

        if is_active is not None:
            query = query.where(User.is_active == is_active)

        # 计算总条数
        total_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(total_query)
        total = total_result.scalar()

        # 分页查询
        query = query.offset(offset).limit(limit).order_by(User.created_at.desc())
        result = await self.db.execute(query)
        items = result.scalars().all()

        return items, total

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        user = await self.get_by_username(username)
        if not user:
            return None

        if not verify_password(password, user.hashed_password):
            return None

        return user

    async def update_last_login(self, user_id: int) -> None:
        """更新用户最后登录时间"""
        from datetime import datetime
        
        user = await self.get(self.db, user_id)
        if user:
            user.last_login_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await self.db.commit()
            await self.db.refresh(user)

    async def create_user(self, user_data: UserCreate) -> User:
        """创建用户"""
        from app.core.security import get_password_hash
        from app.models.user import Role, user_roles
        from sqlalchemy import insert

        # 创建用户数据字典
        user_dict = user_data.model_dump(exclude={'password', 'role_ids'})
        user_dict['hashed_password'] = get_password_hash(user_data.password)

        # 创建用户实例
        db_user = User(**user_dict)
        self.db.add(db_user)
        await self.db.flush()  # 获取用户ID但不提交事务

        # 处理角色分配
        if user_data.role_ids:
            # 验证角色是否存在
            role_result = await self.db.execute(
                select(Role.id).where(Role.id.in_(user_data.role_ids))
            )
            valid_role_ids = [row[0] for row in role_result.fetchall()]

            # 直接插入关联表数据
            if valid_role_ids:
                user_role_data = [
                    {"user_id": db_user.id, "role_id": role_id}
                    for role_id in valid_role_ids
                ]
                await self.db.execute(insert(user_roles).values(user_role_data))

        await self.db.commit()

        # 重新查询用户以获取完整的关系数据
        result = await self.db.execute(
            select(User).options(selectinload(User.roles)).where(User.id == db_user.id)
        )
        return result.scalar_one()

    async def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """更新用户"""
        from app.core.security import get_password_hash
        from app.models.user import Role

        user = await self.get(self.db, user_id)
        if not user:
            return None

        # 更新数据
        update_dict = user_data.model_dump(exclude_unset=True, exclude={'role_ids'})

        # 如果包含密码，需要加密
        if 'password' in update_dict and update_dict['password']:
            update_dict['hashed_password'] = get_password_hash(update_dict.pop('password'))

        # 更新字段
        for field, value in update_dict.items():
            if hasattr(user, field):
                setattr(user, field, value)

        # 处理角色更新
        if user_data.role_ids is not None:
            from app.models.user import Role, user_roles
            from sqlalchemy import delete, insert

            # 先删除现有角色关系
            await self.db.execute(
                delete(user_roles).where(user_roles.c.user_id == user.id)
            )

            if user_data.role_ids:
                # 验证角色是否存在
                role_result = await self.db.execute(
                    select(Role.id).where(Role.id.in_(user_data.role_ids))
                )
                valid_role_ids = [row[0] for row in role_result.fetchall()]

                # 插入新的角色关系
                if valid_role_ids:
                    user_role_data = [
                        {"user_id": user.id, "role_id": role_id}
                        for role_id in valid_role_ids
                    ]
                    await self.db.execute(insert(user_roles).values(user_role_data))

        await self.db.commit()

        # 重新查询用户以获取完整的关系数据
        result = await self.db.execute(
            select(User).options(selectinload(User.roles)).where(User.id == user.id)
        )
        return result.scalar_one()
