"""添加混沌测试相关数据表

Revision ID: add_chaos_tables
Revises: 
Create Date: 2025-08-04 22:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'add_chaos_tables'
down_revision = None  # 这里应该是上一个迁移的revision
branch_labels = None
depends_on = None


def upgrade():
    """创建混沌测试相关数据表"""
    
    # 创建混沌测试任务表
    op.create_table('chaos_tasks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.String(length=50), nullable=True),
        sa.Column('updated_by', sa.String(length=50), nullable=True),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('env_ids', sa.JSON(), nullable=False),
        sa.Column('fault_type', sa.String(length=50), nullable=False),
        sa.Column('fault_params', sa.JSON(), nullable=False),
        sa.Column('execution_type', sa.String(length=20), nullable=True),
        sa.Column('scheduled_time', sa.DateTime(), nullable=True),
        sa.Column('periodic_config', sa.JSON(), nullable=True),
        sa.Column('cron_expression', sa.String(length=100), nullable=True),
        sa.Column('auto_destroy', sa.Boolean(), nullable=True),
        sa.Column('max_duration', sa.Integer(), nullable=True),
        sa.Column('task_status', sa.String(length=20), nullable=True),
        sa.Column('last_execution_time', sa.DateTime(), nullable=True),
        sa.Column('execution_count', sa.Integer(), nullable=True),
        sa.Column('monitor_config', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        mysql_charset='utf8mb4',
        mysql_collate='utf8mb4_unicode_ci',
        comment='混沌测试任务表'
    )
    op.create_index(op.f('ix_chaos_tasks_name'), 'chaos_tasks', ['name'], unique=False)
    
    # 创建混沌测试批次任务表
    op.create_table('chaos_batch_tasks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.String(length=50), nullable=True),
        sa.Column('updated_by', sa.String(length=50), nullable=True),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('env_ids', sa.JSON(), nullable=False),
        sa.Column('execution_type', sa.String(length=20), nullable=True),
        sa.Column('scheduled_time', sa.DateTime(), nullable=True),
        sa.Column('batch_execution_mode', sa.String(length=20), nullable=True),
        sa.Column('batch_interval', sa.Integer(), nullable=True),
        sa.Column('task_status', sa.String(length=20), nullable=True),
        sa.Column('last_execution_time', sa.DateTime(), nullable=True),
        sa.Column('execution_count', sa.Integer(), nullable=True),
        sa.Column('monitor_config', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        mysql_charset='utf8mb4',
        mysql_collate='utf8mb4_unicode_ci',
        comment='混沌测试批次任务表'
    )
    op.create_index(op.f('ix_chaos_batch_tasks_name'), 'chaos_batch_tasks', ['name'], unique=False)
    
    # 创建批次任务子项表
    op.create_table('chaos_batch_task_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.String(length=50), nullable=True),
        sa.Column('updated_by', sa.String(length=50), nullable=True),
        sa.Column('batch_task_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('execution_order', sa.Integer(), nullable=True),
        sa.Column('fault_type', sa.String(length=50), nullable=False),
        sa.Column('fault_params', sa.JSON(), nullable=False),
        sa.Column('auto_destroy', sa.Boolean(), nullable=True),
        sa.Column('max_duration', sa.Integer(), nullable=True),
        sa.Column('is_enabled', sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(['batch_task_id'], ['chaos_batch_tasks.id'], ),
        sa.PrimaryKeyConstraint('id'),
        mysql_charset='utf8mb4',
        mysql_collate='utf8mb4_unicode_ci',
        comment='批次任务子项表'
    )
    
    # 创建混沌测试执行记录表
    op.create_table('chaos_executions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.String(length=50), nullable=True),
        sa.Column('updated_by', sa.String(length=50), nullable=True),
        sa.Column('task_id', sa.Integer(), nullable=True),
        sa.Column('batch_task_id', sa.Integer(), nullable=True),
        sa.Column('batch_task_item_id', sa.Integer(), nullable=True),
        sa.Column('host_id', sa.Integer(), nullable=True),
        sa.Column('host_info', sa.JSON(), nullable=False),
        sa.Column('fault_config', sa.JSON(), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('start_time', sa.DateTime(), nullable=True),
        sa.Column('end_time', sa.DateTime(), nullable=True),
        sa.Column('destroy_time', sa.DateTime(), nullable=True),
        sa.Column('chaos_uid', sa.String(length=100), nullable=True),
        sa.Column('command', sa.Text(), nullable=True),
        sa.Column('output', sa.Text(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('is_auto_destroyed', sa.Boolean(), nullable=True),
        sa.Column('destroy_output', sa.Text(), nullable=True),
        sa.Column('celery_task_id', sa.String(length=100), nullable=True),
        sa.ForeignKeyConstraint(['batch_task_id'], ['chaos_batch_tasks.id'], ),
        sa.ForeignKeyConstraint(['batch_task_item_id'], ['chaos_batch_task_items.id'], ),
        sa.ForeignKeyConstraint(['task_id'], ['chaos_tasks.id'], ),
        sa.PrimaryKeyConstraint('id'),
        mysql_charset='utf8mb4',
        mysql_collate='utf8mb4_unicode_ci',
        comment='混沌测试执行记录表'
    )
    op.create_index(op.f('ix_chaos_executions_chaos_uid'), 'chaos_executions', ['chaos_uid'], unique=False)
    
    # 创建混沌测试场景模板表
    op.create_table('chaos_scenarios',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.String(length=50), nullable=True),
        sa.Column('updated_by', sa.String(length=50), nullable=True),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category', sa.String(length=50), nullable=True),
        sa.Column('fault_type', sa.String(length=50), nullable=False),
        sa.Column('fault_params_template', sa.JSON(), nullable=False),
        sa.Column('default_duration', sa.Integer(), nullable=True),
        sa.Column('default_auto_destroy', sa.Boolean(), nullable=True),
        sa.Column('applicable_platforms', sa.JSON(), nullable=True),
        sa.Column('required_tools', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_builtin', sa.Boolean(), nullable=True),
        sa.Column('usage_count', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        mysql_charset='utf8mb4',
        mysql_collate='utf8mb4_unicode_ci',
        comment='混沌测试场景模板表'
    )
    op.create_index(op.f('ix_chaos_scenarios_name'), 'chaos_scenarios', ['name'], unique=False)


def downgrade():
    """删除混沌测试相关数据表"""
    op.drop_index(op.f('ix_chaos_scenarios_name'), table_name='chaos_scenarios')
    op.drop_table('chaos_scenarios')
    op.drop_index(op.f('ix_chaos_executions_chaos_uid'), table_name='chaos_executions')
    op.drop_table('chaos_executions')
    op.drop_table('chaos_batch_task_items')
    op.drop_index(op.f('ix_chaos_batch_tasks_name'), table_name='chaos_batch_tasks')
    op.drop_table('chaos_batch_tasks')
    op.drop_index(op.f('ix_chaos_tasks_name'), table_name='chaos_tasks')
    op.drop_table('chaos_tasks')
