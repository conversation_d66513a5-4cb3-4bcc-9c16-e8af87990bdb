<template>
  <div class="task-form-new">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleCancel" :icon="ArrowLeft">返回</el-button>
        <div class="header-info">
          <h1>{{ pageTitle }}</h1>
          <!-- <p>{{ pageDescription }}</p> -->
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          :disabled="!canSubmit"
        >
          {{ submitButtonText }}
        </el-button>
      </div>
    </div>

    <!-- 分步表单 -->
    <div class="form-container">
      <el-steps :active="currentStep" align-center class="form-steps">
        <el-step title="基本信息" description="设置批次任务基本信息" />
        <el-step title="批次配置" description="配置批次任务子项" />
        <el-step title="执行计划" description="设置执行方式和时间" />
        <el-step title="确认提交" description="确认配置并提交任务" />
      </el-steps>

      <div class="step-content">
        <!-- 第一步：基本信息 -->
        <BasicInfoStep
          v-show="currentStep === 0"
          ref="basicStepRef"
          :form-data="formData"
          :environments="environments || []"
          :environments-loading="environmentsLoading"
          :is-batch="true"
        />

        <!-- 第二步：故障配置 -->
        <FaultConfigStep
          v-show="currentStep === 1"
          ref="faultStepRef"
          :form-data="formData"
          :scenarios="scenarios || []"
          v-model:selected-category="selectedCategory"
          @fault-type-change="handleFaultTypeChange"
          :is-batch="true"
        />

        <!-- 第三步：执行计划 -->
        <ScheduleStep
          v-show="currentStep === 2"
          ref="scheduleStepRef"
          :form-data="formData"
          :is-batch="true"
        />

        <!-- 第四步：确认提交 -->
        <ConfirmStep
          v-show="currentStep === 3"
          ref="confirmStepRef"
          :form-data="formData"
          :environments="environments || []"
          :scenarios="scenarios || []"
          :is-batch="true"
        />
      </div>

      <!-- 步骤导航 -->
      <div class="step-navigation">
        <el-button 
          v-if="currentStep > 0" 
          @click="handlePrevStep"
          :icon="ArrowLeft"
        >
          上一步
        </el-button>
        <el-button 
          v-if="currentStep < 3" 
          type="primary" 
          @click="handleNextStep"
          :icon="ArrowRight"
        >
          下一步
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

// 导入子组件
import BasicInfoStep from './components/BasicInfoStep.vue'
import FaultConfigStep from './components/FaultConfigStep.vue'
import ScheduleStep from './components/ScheduleStep.vue'
import ConfirmStep from './components/ConfirmStep.vue'

// 导入stores
import { useChaosScenariosStore } from '@/store/business/chaos/scenarios'
import { useEnvironmentStore } from '@/store/business/environment'

// 导入API
import ChaosService from '@/api/chaosApi'

// 导入类型
import type { ChaosBatchTaskCreate } from '@/types/api/chaos'

const router = useRouter()
const route = useRoute()
const scenariosStore = useChaosScenariosStore()
const environmentStore = useEnvironmentStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const environmentsLoading = ref(false)
const currentStep = ref(0)
const selectedCategory = ref('系统资源')

// 表单引用
const basicStepRef = ref()
const faultStepRef = ref()
const scheduleStepRef = ref()
const confirmStepRef = ref()

// 数据
const environments = ref<any[]>([])
const scenarios = ref<any[]>([])

// 表单数据
const formData = reactive<ChaosBatchTaskCreate & {
  scheduled_time: string
  cron_expression: string
}>({
  name: '',
  description: '',
  env_id: undefined as any,
  batch_execution_mode: 'sequential',
  wait_time: 30,
  task_items: [],
  execution_type: 'immediate',
  scheduled_time: '',
  cron_expression: '',
  auto_destroy: true,
  max_duration: 300,
  task_status: 'enabled'
})

// 计算属性
const taskId = computed(() => Number(route.params.id))
const copyTaskId = computed(() => Number(route.query.copy))
const isEditMode = computed(() => route.name === 'ChaosBatchTaskEdit' && taskId.value)
const isCopyMode = computed(() => !!copyTaskId.value)

const pageTitle = computed(() => {
  if (isEditMode.value) return '编辑批次任务'
  if (isCopyMode.value) return '复制批次任务'
  return '创建批次任务'
})

const pageDescription = computed(() => {
  if (isEditMode.value) return '修改现有的混沌测试任务配置'
  if (isCopyMode.value) return '基于现有任务创建新的混沌测试任务'
  return '创建新的混沌测试任务，配置故障注入参数'
})

const submitButtonText = computed(() => {
  return isEditMode.value ? '更新任务' : '创建任务'
})

const canSubmit = computed(() => {
  if (currentStep.value !== 3) return false
  return confirmStepRef.value?.canSubmit?.() || false
})

// 生命周期
onMounted(async () => {
  await loadInitialData()
  
  // 根据模式加载数据
  if (isEditMode.value && taskId.value) {
    await loadTaskForEdit(taskId.value)
  } else if (isCopyMode.value && copyTaskId.value) {
    await loadTaskForCopy(copyTaskId.value)
  }
})

// 方法
const loadInitialData = async () => {
  await Promise.all([
    loadEnvironments(),
    loadScenarios()
  ])
}

const loadEnvironments = async () => {
  environmentsLoading.value = true
  try {
    const result = await environmentStore.fetchEnvironments()
    if (result) {
      // 根据实际返回的数据结构设置环境数据
      environments.value = result.items || result.records || []
    }
  } catch (error) {
    ElMessage.error('加载环境列表失败')
  } finally {
    environmentsLoading.value = false
  }
}

const loadScenarios = async () => {
  try {
    const result = await scenariosStore.fetchScenariosForTask()
    if (result) {
      // fetchScenariosForTask返回的直接是场景数组
      scenarios.value = Array.isArray(result) ? result : []
    }
  } catch (error) {
    console.error('加载场景列表失败:', error)
    ElMessage.error('加载场景列表失败')
  }
}

const loadTaskForEdit = async (id: number) => {
  loading.value = true
  try {
    const task = await ChaosService.getBatchTaskDetail(id)

    // 处理子任务的故障参数：将对象转换为JSON字符串
    const processedTaskItems = (task.task_items || []).map(item => ({
      ...item,
      fault_params_json: item.fault_params ? JSON.stringify(item.fault_params, null, 2) : '{}'
    }))

    // 填充表单数据
    Object.assign(formData, {
      name: task.name,
      description: task.description || '',
      env_id: task.env_id || 0,
      batch_execution_mode: task.batch_execution_mode || 'sequential',
      wait_time: task.wait_time || 30,
      task_items: processedTaskItems,
      execution_type: task.execution_type || 'immediate',
      scheduled_time: task.scheduled_time || '',
      cron_expression: task.cron_expression || '',
      auto_destroy: task.auto_destroy ?? true,
      max_duration: task.max_duration || 300,
      task_status: task.task_status || 'enabled'
    })
  } catch (error) {
    ElMessage.error('加载批次任务数据失败')
    router.push('/chaos/tasks')
  } finally {
    loading.value = false
  }
}

const loadTaskForCopy = async (id: number) => {
  loading.value = true
  try {
    const task = await ChaosService.getBatchTaskDetail(id)

    // 处理子任务的故障参数：将对象转换为JSON字符串
    const processedTaskItems = (task.task_items || []).map(item => ({
      ...item,
      fault_params_json: item.fault_params ? JSON.stringify(item.fault_params, null, 2) : '{}'
    }))

    // 复制批次任务数据，但重置名称
    Object.assign(formData, {
      name: `${task.name} - 副本`,
      description: task.description || '',
      env_id: task.env_id || undefined,
      batch_execution_mode: task.batch_execution_mode || 'sequential',
      wait_time: task.wait_time || 30,
      task_items: processedTaskItems,
      execution_type: task.execution_type || 'immediate',
      scheduled_time: task.scheduled_time || '',
      cron_expression: task.cron_expression || '',
      auto_destroy: task.auto_destroy ?? true,
      max_duration: task.max_duration || 300,
      task_status: task.task_status || 'enabled'
    })
  } catch (error) {
    ElMessage.error('加载任务数据失败')
    router.push('/chaos/tasks')
  } finally {
    loading.value = false
  }
}

const setSelectedCategoryByFaultType = (faultType: string) => {
  const scenario = scenarios.value.find(s => s.fault_type === faultType)
  if (scenario?.category) {
    selectedCategory.value = scenario.category
  }
}

const handleFaultTypeChange = (faultType: string) => {
  // 子组件已处理参数重置
}

const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleNextStep = async () => {
  // 验证当前步骤
  let isValid = true
  
  switch (currentStep.value) {
    case 0:
      isValid = await basicStepRef.value?.validate?.() || false
      break
    case 1:
      isValid = await faultStepRef.value?.validate?.() || false
      break
    case 2:
      isValid = await scheduleStepRef.value?.validate?.() || false
      break
  }
  
  if (isValid && currentStep.value < 3) {
    currentStep.value++
  }
}

const handleSubmit = async () => {
  if (!canSubmit.value) {
    ElMessage.warning('请确认所有配置信息')
    return
  }

  submitting.value = true
  try {
    // 清理提交数据，移除空的可选字段
    const submitData = { ...formData }

    // 根据执行类型清理不需要的字段
    if (submitData.execution_type !== 'scheduled') {
      delete (submitData as any).scheduled_time
    }

    if (submitData.execution_type !== 'periodic') {
      delete (submitData as any).periodic_config
    } else {
      // 清理周期配置中的0值
      const config = submitData.periodic_config
      if (config) {
        if (!config.minutes) delete config.minutes
        if (!config.hours) delete config.hours
        if (!config.days) delete config.days
      }
    }

    if (submitData.execution_type !== 'cron') {
      delete (submitData as any).cron_expression
    }

    // 移除空的描述
    if (!submitData.description || submitData.description.trim() === '') {
      delete (submitData as any).description
    }

    if (isEditMode.value) {
      await ChaosService.updateBatchTask(taskId.value, submitData)
      ElMessage.success('批次任务更新成功')
    } else {
      await ChaosService.createBatchTask(submitData)
      ElMessage.success('批次任务创建成功')
    }

    router.push('/chaos/tasks')
  } catch (error) {
    ElMessage.error(isEditMode.value ? '任务更新失败' : '任务创建失败')
  } finally {
    submitting.value = false
  }
}

const handleCancel = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消吗？未保存的更改将丢失。',
      '确认取消',
      { type: 'warning' }
    )
    router.push('/chaos/tasks')
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.task-form-new {
  min-height: 100vh;
}

.page-header {
  padding-bottom:  15px;
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-info h1 {
  /* margin: 0 0 4px 0; */
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.header-info p {
  margin: 0;
  color: #606266;
  font-size: 10px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px;
}

.form-steps {
  margin-bottom: 40px;
}

.step-content {
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
  min-height: 500px;
}

.step-navigation {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}
</style>
