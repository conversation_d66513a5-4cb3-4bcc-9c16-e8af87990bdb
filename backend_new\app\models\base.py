"""
基础数据模型
定义所有模型共享的基础字段和方法
"""
from datetime import datetime
from typing import Any,Dict
from sqlalchemy import Column, Integer, DateTime, func,String, event
from sqlalchemy.ext.declarative import as_declarative, declared_attr
from app.core.context import get_current_user_id


@as_declarative()
class BaseModel:
    """基础模型类，所有数据模型都应继承此类"""
    
    # 自动生成表名（类名转换为小写下划线格式）
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建者")
    updated_by = Column(String(50), nullable=True, comment="更新者")


    def to_dict(self) -> Dict[str, Any]:
        """
        将模型实例转换为字典，自动将时间字段转换为中国时区（UTC+8）
        """
        # 获取所有列字段
        columns = [column.key for column in self.__table__.columns]
        result = {}
        
        for column in columns:
            value = getattr(self, column)
            
            # 处理时间字段，转换为中国时区（UTC+8）
            if isinstance(value, datetime):
                # 若时间带有时区信息，先转换为UTC再转东八区；否则直接假设为UTC时间处理
                if value.tzinfo:
                    utc_time = value.astimezone(datetime.timezone.utc)
                else:
                    utc_time = value.replace(tzinfo=datetime.timezone.utc)
                
                # 转换为东八区（UTC+8）
                china_tz = datetime.timezone(datetime.timedelta(hours=8))
                china_time = utc_time.astimezone(china_tz)
                
                # 格式化为字符串（保留毫秒）
                result[column] = china_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            else:
                result[column] = value
        
        return result
    
    def update_from_dict(self, data: dict) -> None:
        """
        从字典更新模型实例
        
        Args:
            data: 更新数据字典
        """
        for key, value in data.items():
            if hasattr(self, key) and key not in ['id', 'created_at']:
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """模型的字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id})>"


# SQLAlchemy事件监听器，自动设置created_by和updated_by字段
@event.listens_for(BaseModel, 'before_insert', propagate=True)
def set_created_by(mapper, connection, target):
    """在插入前自动设置created_by和updated_by"""
    current_user = get_current_user_id()
    if current_user and hasattr(target, 'created_by'):
        target.created_by = current_user
    if current_user and hasattr(target, 'updated_by'):
        target.updated_by = current_user


@event.listens_for(BaseModel, 'before_update', propagate=True)
def set_updated_by(mapper, connection, target):
    """在更新前自动设置updated_by"""
    current_user = get_current_user_id()
    if current_user and hasattr(target, 'updated_by'):
        target.updated_by = current_user
