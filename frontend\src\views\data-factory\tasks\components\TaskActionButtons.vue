<template>
  <div class="task-action-buttons">
    <!-- 查看详情 -->
    <el-button
      link
      type="primary"
      size="small"
      @click="handleView"
    >
      <el-icon><View /></el-icon>
      详情
    </el-button>

    <!-- 下载结果 -->
    <el-button
      v-if="canDownload"
      link
      type="success"
      size="small"
      @click="handleDownload"
    >
      <el-icon><Download /></el-icon>
      下载
    </el-button>

    <!-- 取消任务 -->
    <el-button
      v-if="canCancel"
      link
      type="warning"
      size="small"
      @click="handleCancel"
    >
      <el-icon><Close /></el-icon>
      取消
    </el-button>

    <!-- 重试任务 -->
    <el-button
      v-if="canRetry"
      link
      type="primary"
      size="small"
      @click="handleRetry"
    >
      <el-icon><RefreshRight /></el-icon>
      重试
    </el-button>

    <!-- 删除任务 -->
    <el-button
      v-if="canDelete"
      link
      type="danger"
      size="small"
      @click="handleDelete"
    >
      <el-icon><Delete /></el-icon>
      删除
    </el-button>

    <!-- 更多操作 -->
    <el-dropdown
      v-if="hasMoreActions"
      @command="handleCommand"
      trigger="click"
    >
      <el-button link size="small">
        <el-icon><MoreFilled /></el-icon>
        更多
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-if="task.status === 'completed'"
            command="copy"
          >
            <el-icon><CopyDocument /></el-icon>
            复制任务
          </el-dropdown-item>
          <el-dropdown-item
            v-if="task.status === 'completed'"
            command="share"
          >
            <el-icon><Share /></el-icon>
            分享结果
          </el-dropdown-item>
          <el-dropdown-item
            command="log"
            divided
          >
            <el-icon><Document /></el-icon>
            查看日志
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  View,
  Download,
  Close,
  RefreshRight,
  Delete,
  MoreFilled,
  CopyDocument,
  Share,
  Document
} from '@element-plus/icons-vue'

interface Props {
  task: Api.DataFactory.TaskInfo
}

interface Emits {
  (e: 'view', task: Api.DataFactory.TaskInfo): void
  (e: 'download', task: Api.DataFactory.TaskInfo): void
  (e: 'cancel', task: Api.DataFactory.TaskInfo): void
  (e: 'retry', task: Api.DataFactory.TaskInfo): void
  (e: 'delete', task: Api.DataFactory.TaskInfo): void
  (e: 'copy', task: Api.DataFactory.TaskInfo): void
  (e: 'share', task: Api.DataFactory.TaskInfo): void
  (e: 'log', task: Api.DataFactory.TaskInfo): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性 - 按钮显示条件
const canDownload = computed(() => {
  return props.task.status === 'completed' && props.task.result_file_path
})

const canCancel = computed(() => {
  return props.task.status === 'pending' || props.task.status === 'running'
})

const canRetry = computed(() => {
  return props.task.status === 'failed'
})

const canDelete = computed(() => {
  return ['completed', 'failed', 'cancelled'].includes(props.task.status)
})

const hasMoreActions = computed(() => {
  return props.task.status === 'completed' || true // 总是显示更多操作（查看日志）
})

// 事件处理
const handleView = () => {
  emit('view', props.task)
}

const handleDownload = () => {
  emit('download', props.task)
}

const handleCancel = () => {
  emit('cancel', props.task)
}

const handleRetry = () => {
  emit('retry', props.task)
}

const handleDelete = () => {
  emit('delete', props.task)
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'copy':
      emit('copy', props.task)
      break
    case 'share':
      emit('share', props.task)
      break
    case 'log':
      emit('log', props.task)
      break
  }
}
</script>

<style scoped lang="scss">
.task-action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;

  .el-button {
    padding: 4px 8px;
    font-size: 12px;
    
    .el-icon {
      font-size: 12px;
    }
  }
}
</style>
