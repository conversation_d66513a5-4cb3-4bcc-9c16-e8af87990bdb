"""
模型配置管理API路由
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, Query
from fastapi.responses import StreamingResponse

from app.api.deps import get_db, get_redis_client, get_current_active_user
from app.db.redis import RedisClient
from app.schemas.model_config import (
    ModelConfigCreate, ModelConfigUpdate, ModelConfigResponse, ModelConfigQuery,
    ModelConfigPageResponse, ModelHealthCheckRequest, ModelHealthCheckResponse,
    ModelCallRequest, ModelCallResponse, ModelStatsResponse, SupportedPlatformResponse
)
from app.services.model_config import ModelConfigService
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


@router.post("", response_model=ModelConfigResponse, summary="创建模型配置")
async def create_model_config(
    model_data: ModelConfigCreate,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user = Depends(get_current_active_user)
):
    """创建新的模型配置，需要登录权限"""
    model_service = ModelConfigService(db, redis_client)
    result = await model_service.create_model_config(model_data)
    return result


@router.get("", response_model=ModelConfigPageResponse, summary="查询模型配置列表")
async def list_model_configs(
    query: ModelConfigQuery = Depends(),
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user = Depends(get_current_active_user)
):
    """查询模型配置列表，支持关键词搜索、平台筛选和分页"""
    model_service = ModelConfigService(db, redis_client)
    result = await model_service.list_model_configs(query)
    return result


@router.get("/{model_id}", response_model=ModelConfigResponse, summary="获取模型配置详情")
async def get_model_config(
    model_id: int,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """获取指定ID的模型配置详情"""
    model_service = ModelConfigService(db)
    result = await model_service.get_model_config_by_id(model_id)
    return result


@router.put("/{model_id}", response_model=ModelConfigResponse, summary="更新模型配置")
async def update_model_config(
    model_id: int,
    model_data: ModelConfigUpdate,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """更新模型配置信息，支持修改基本信息和配置"""
    model_service = ModelConfigService(db)
    result = await model_service.update_model_config(model_id, model_data)
    return result


@router.delete("/{model_id}", status_code=204, summary="删除模型配置")
async def delete_model_config(
    model_id: int,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """删除模型配置"""
    model_service = ModelConfigService(db)
    await model_service.delete_model_config(model_id)
    return None


@router.post("/{model_id}/enable", response_model=ModelConfigResponse, summary="启用模型")
async def enable_model(
    model_id: int,
    db: AsyncSession = Depends(get_db),

    _current_user = Depends(get_current_active_user)
):
    """启用模型"""
    model_service = ModelConfigService(db)
    result = await model_service.enable_model(model_id)
    return result


@router.post("/{model_id}/disable", response_model=ModelConfigResponse, summary="停用模型")
async def disable_model(
    model_id: int,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """停用模型"""
    model_service = ModelConfigService(db)
    result = await model_service.disable_model(model_id)
    return result


@router.get("/available/list", response_model=List[ModelConfigResponse], summary="获取可用模型列表")
async def get_available_models(
    db: AsyncSession = Depends(get_db)
):
    """获取所有可用的模型配置（启用且健康）"""
    model_service = ModelConfigService(db)
    models = await model_service.get_available_models()
    return models


@router.post("/{model_id}/health-check", response_model=ModelHealthCheckResponse, summary="检查指定模型健康状态")
async def health_check_model(
    model_id: int,
    timeout_seconds: int = Query(None, ge=1, le=60, description="检查超时时间（秒）"),
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """检查指定模型的健康状态"""
    model_service = ModelConfigService(db)
    result = await model_service.health_check_model(model_id, timeout_seconds)
    return result


@router.post("/health-check/batch", response_model=List[ModelHealthCheckResponse], summary="批量健康检查")
async def batch_health_check(
    request: ModelHealthCheckRequest,
    db: AsyncSession = Depends(get_db)
):
    """批量检查模型健康状态"""
    model_service = ModelConfigService(db)
    results = await model_service.batch_health_check(request.model_ids, request.timeout_seconds)
    return results


@router.post("/test", response_model=Dict[str, Any], summary="测试模型配置连接")
async def test_model_config(
    model_data: ModelConfigCreate,
    db: AsyncSession = Depends(get_db)
):
    """测试模型配置连接"""
    model_service = ModelConfigService(db)
    result = await model_service.test_model_connection(model_data)
    return result


@router.get("/stats/overview", response_model=ModelStatsResponse, summary="获取模型统计信息")
async def get_model_stats(
    db: AsyncSession = Depends(get_db)
):
    """获取模型统计信息"""
    model_service = ModelConfigService(db)
    stats = await model_service.get_model_stats()
    return stats


@router.post("/call", response_model=ModelCallResponse, summary="调用模型")
async def call_model(
    request: ModelCallRequest,
    db: AsyncSession = Depends(get_db)
):
    """统一模型调用接口"""
    model_service = ModelConfigService(db)
    result = await model_service.call_model(request)
    return result


@router.post("/call/stream", summary="调用模型（流式）")
async def call_model_stream(
    request: ModelCallRequest,
    db: AsyncSession = Depends(get_db)
):
    """统一模型调用接口（流式）"""
    model_service = ModelConfigService(db)
    # 业务逻辑交给Service层处理，API层只负责返回StreamingResponse
    # 注意：这里不能用await，因为返回的是异步生成器
    stream_generator = model_service.call_model_stream_response(request)

    return StreamingResponse(
        stream_generator,
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8"
        }
    )


@router.get("/platforms/supported", response_model=List[SupportedPlatformResponse], summary="获取支持的平台类型")
async def get_supported_platforms(
    db: AsyncSession = Depends(get_db)
):
    """获取系统支持的平台类型"""
    model_service = ModelConfigService(db)
    platforms = await model_service.get_supported_platforms()
    return platforms
