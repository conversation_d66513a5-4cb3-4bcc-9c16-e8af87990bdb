"""
异常定义模块
定义应用中使用的自定义异常类
"""
from typing import Any, Dict, Optional
from fastapi import HTTPException, status


class BaseCustomException(HTTPException):
    """基础自定义异常类"""
    
    def __init__(
        self,
        status_code: int,
        detail: Any = None,
        headers: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(status_code, detail, headers)


class ValidationException(BaseCustomException):
    """数据验证异常"""
    
    def __init__(self, detail: str = "数据验证失败"):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail
        )


class AuthenticationException(BaseCustomException):
    """认证异常"""
    
    def __init__(self, detail: str = "认证失败"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


class AuthorizationException(BaseCustomException):
    """授权异常"""
    
    def __init__(self, detail: str = "权限不足"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )


class NotFoundException(BaseCustomException):
    """资源不存在异常"""
    
    def __init__(self, detail: str = "资源不存在"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail
        )


class ConflictException(BaseCustomException):
    """资源冲突异常"""
    
    def __init__(self, detail: str = "资源冲突"):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail
        )


class BusinessException(BaseCustomException):
    """业务逻辑异常"""
    
    def __init__(self, detail: str = "业务处理失败"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )


class ServerException(BaseCustomException):
    """服务器内部异常"""
    
    def __init__(self, detail: str = "服务器内部错误"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


# 便捷的异常抛出函数
def raise_validation_error(detail: str) -> None:
    """抛出验证异常"""
    raise ValidationException(detail)


def raise_authentication_error(detail: str = "认证失败") -> None:
    """抛出认证异常"""
    raise AuthenticationException(detail)


def raise_authorization_error(detail: str = "权限不足") -> None:
    """抛出授权异常"""
    raise AuthorizationException(detail)


def raise_not_found_error(detail: str = "资源不存在") -> None:
    """抛出资源不存在异常"""
    raise NotFoundException(detail)


def raise_conflict_error(detail: str = "资源冲突") -> None:
    """抛出资源冲突异常"""
    raise ConflictException(detail)


def raise_business_error(detail: str = "业务处理失败") -> None:
    """抛出业务异常"""
    raise BusinessException(detail)


def raise_server_error(detail: str = "服务器内部错误") -> None:
    """抛出服务器异常"""
    raise ServerException(detail)
