#!/usr/bin/env python3
"""
简单的连接测试 - 直接调用服务层
"""
import asyncio
import sys
import json

# 添加项目路径
sys.path.append('.')

from app.services.environment import EnvironmentService
from app.schemas.environment import ConnectionTestRequest
from app.db.session import get_async_db


async def test_connections():
    """测试各种连接类型"""
    print("🚀 开始连接测试...")
    print("=" * 60)
    
    # 测试配置
    test_cases = [
        {
            "name": "HTTP连接测试 - httpbin.org",
            "type": "http",
            "config": {
                "host": "httpbin.org",
                "port": 80,
                "protocol": "http",
                "path": "/get"
            }
        },
        {
            "name": "HTTPS连接测试 - httpbin.org",
            "type": "https",
            "config": {
                "host": "httpbin.org",
                "port": 443,
                "protocol": "https",
                "path": "/get"
            }
        },
        {
            "name": "TCP连接测试 - 百度",
            "type": "tcp",
            "config": {
                "host": "www.baidu.com",
                "port": 80
            }
        },
        {
            "name": "TCP连接测试 - Google DNS",
            "type": "tcp",
            "config": {
                "host": "*******",
                "port": 53
            }
        },
        {
            "name": "SSH连接测试 - 无效地址",
            "type": "ssh",
            "config": {
                "host": "192.168.1.999",  # 无效IP
                "port": 22,
                "username": "test",
                "password": "test"
            }
        },
        {
            "name": "数据库连接测试 - 本地MySQL",
            "type": "mysql",
            "config": {
                "host": "127.0.0.1",
                "port": 3306
            }
        },
        {
            "name": "Redis连接测试 - 本地Redis",
            "type": "redis",
            "config": {
                "host": "127.0.0.1",
                "port": 6379
            }
        }
    ]
    
    async for db in get_async_db():
        env_service = EnvironmentService(db)

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 测试 {i}/{len(test_cases)}: {test_case['name']}")
            print("-" * 50)
            
            try:
                # 创建测试请求
                test_request = ConnectionTestRequest(
                    type=test_case['type'],
                    config=test_case['config'],
                    timeout=10
                )
                
                # 执行测试
                result = await env_service.test_connection(test_request)
                
                # 显示结果
                success_icon = "✅" if result.success else "❌"
                print(f"{success_icon} 结果: {result.success}")
                print(f"📝 消息: {result.message}")
                print(f"⏱️  耗时: {result.duration:.2f}秒")
                
                if result.details:
                    print(f"📊 详情:")
                    for key, value in result.details.items():
                        print(f"   {key}: {value}")
                
            except Exception as e:
                print(f"❌ 测试异常: {str(e)}")
                import traceback
                traceback.print_exc()
        
        break  # 只需要一个数据库连接
    
    print("\n" + "=" * 60)
    print("🏁 所有测试完成！")


async def test_specific_connection():
    """测试特定连接"""
    print("🎯 特定连接测试")
    print("=" * 40)
    
    # 测试一个肯定能成功的连接
    test_request = ConnectionTestRequest(
        type="http",
        config={
            "host": "httpbin.org",
            "port": 80,
            "protocol": "http",
            "path": "/get"
        },
        timeout=15
    )
    
    async for db in get_async_db():
        env_service = EnvironmentService(db)

        print("🧪 测试HTTP连接到 httpbin.org...")
        try:
            result = await env_service.test_connection(test_request)

            print(f"✅ 成功: {result.success}")
            print(f"📝 消息: {result.message}")
            print(f"⏱️  耗时: {result.duration:.2f}秒")
            print(f"📊 详情: {json.dumps(result.details, indent=2, ensure_ascii=False)}")

        except Exception as e:
            print(f"❌ 异常: {str(e)}")
            import traceback
            traceback.print_exc()
        break


async def main():
    """主函数"""
    print("📋 环境连接测试工具 (直接调用服务层)")
    print("💡 这个测试直接调用服务层，不通过API")
    print()
    
    mode = input("请选择测试模式:\n1. 全面测试\n2. 特定测试\n请输入选择 (1-2): ").strip()
    
    if mode == "1":
        await test_connections()
    elif mode == "2":
        await test_specific_connection()
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    asyncio.run(main())
