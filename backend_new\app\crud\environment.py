"""
环境CRUD操作
"""
from typing import Optional, List, Tuple, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, update
from app.crud.base import CRUDBase
from app.models.environment import Environment
from app.schemas.environment import EnvironmentCreate, EnvironmentUpdate


class EnvironmentCRUD(CRUDBase[Environment, EnvironmentCreate, EnvironmentUpdate]):
    """环境CRUD操作类"""

    def __init__(self, db: AsyncSession):
        super().__init__(Environment)
        self.db = db

    async def get_by_name(self, name: str) -> Optional[Environment]:
        """根据名称查询环境"""
        result = await self.db.execute(
            select(Environment).where(Environment.name == name)
        )
        return result.scalar_one_or_none()

    async def list(
        self,
        keyword: Optional[str] = None,
        env_type: Optional[str] = None,
        status: Optional[str] = None,
        tags: Optional[str] = None,
        offset: int = 0,
        limit: int = 10
    ) -> <PERSON><PERSON>[List[Environment], int]:
        """查询环境列表（带筛选和分页）"""
        # 基础查询
        query = select(Environment)

        # 筛选条件
        if keyword:
            query = query.where(
                or_(
                    Environment.name.ilike(f"%{keyword}%"),
                    Environment.description.ilike(f"%{keyword}%"),
                    Environment.host.ilike(f"%{keyword}%")
                )
            )

        if env_type:
            query = query.where(Environment.type == env_type)

        if status:
            query = query.where(Environment.status == status)

        if tags:
            # 标签筛选，支持模糊匹配
            query = query.where(Environment.tags.ilike(f"%{tags}%"))

        # 计算总条数
        total_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(total_query)
        total = total_result.scalar()

        # 分页查询
        query = query.offset(offset).limit(limit).order_by(Environment.created_at.desc())
        result = await self.db.execute(query)
        items = result.scalars().all()

        return items, total

    async def get_environments_by_ids(self, env_ids: List[int]) -> List[Environment]:
        """根据ID列表获取环境"""
        result = await self.db.execute(
            select(Environment).where(Environment.id.in_(env_ids))
        )
        return result.scalars().all()

    async def update_status(self, env_id: int, status: str, last_test_time: Optional[str] = None) -> bool:
        """更新环境状态"""
        update_data = {"status": status}
        if last_test_time:
            update_data["last_test_time"] = last_test_time

        result = await self.db.execute(
            update(Environment)
            .where(Environment.id == env_id)
            .values(**update_data)
        )
        await self.db.commit()
        return result.rowcount > 0

    async def batch_update_status(self, status_updates: List[Dict[str, Any]]) -> int:
        """批量更新环境状态"""
        if not status_updates:
            return 0
        
        updated_count = 0
        for update_data in status_updates:
            env_id = update_data.pop("id")
            result = await self.db.execute(
                update(Environment).where(Environment.id == env_id).values(**update_data)
            )
            updated_count += result.rowcount
        
        await self.db.commit()
        return updated_count

    async def get_type_stats(self) -> Dict[str, int]:
        """获取环境类型统计"""
        result = await self.db.execute(
            select(Environment.type, func.count(Environment.id))
            .group_by(Environment.type)
        )
        return {row[0]: row[1] for row in result.fetchall()}

    async def get_status_stats(self) -> Dict[str, int]:
        """获取环境状态统计"""
        result = await self.db.execute(
            select(Environment.status, func.count(Environment.id))
            .group_by(Environment.status)
        )
        return {row[0]: row[1] for row in result.fetchall()}

    async def get_recent_environments(self, limit: int = 5) -> List[Environment]:
        """获取最近创建的环境"""
        result = await self.db.execute(
            select(Environment)
            .order_by(Environment.created_at.desc())
            .limit(limit)
        )
        return result.scalars().all()

    async def create_environment(self, env_data: EnvironmentCreate) -> Environment:
        """创建环境"""
        # 创建环境数据字典
        env_dict = env_data.model_dump()

        # 创建环境实例
        db_env = Environment(**env_dict)
        self.db.add(db_env)
        await self.db.commit()
        await self.db.refresh(db_env)

        return db_env

    async def update_environment(self, env_id: int, env_data: EnvironmentUpdate) -> Optional[Environment]:
        """更新环境"""
        env = await self.get(self.db, env_id)
        if not env:
            return None

        # 更新数据
        update_dict = env_data.model_dump(exclude_unset=True)

        # 更新字段
        for field, value in update_dict.items():
            if hasattr(env, field):
                setattr(env, field, value)

        await self.db.commit()
        await self.db.refresh(env)

        return env
