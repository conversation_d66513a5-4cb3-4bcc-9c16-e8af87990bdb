/**
 * 用户信息管理工具
 * 提供智能缓存、条件刷新、性能优化等功能
 */

import { UserService } from '@/api/usersApi'
import { useUserStore } from '@/store/modules/user'

interface UserInfoCache {
  data: any
  timestamp: number
  version: string
}

class UserInfoManager {
  private static instance: UserInfoManager
  private cacheKey = 'user_info_cache'
  private timestampKey = 'user_info_timestamp'
  private versionKey = 'user_info_version'
  private defaultCacheTime = 10 * 60 * 1000 // 10分钟默认缓存时间
  private maxCacheTime = 30 * 60 * 1000 // 30分钟最大缓存时间

  private constructor() {}

  static getInstance(): UserInfoManager {
    if (!UserInfoManager.instance) {
      UserInfoManager.instance = new UserInfoManager()
    }
    return UserInfoManager.instance
  }

  /**
   * 获取用户信息（智能缓存策略）
   */
  async getUserInfo(options: {
    forceRefresh?: boolean
    cacheTime?: number
    fallbackToCache?: boolean
  } = {}): Promise<any> {
    const {
      forceRefresh = false,
      cacheTime = this.defaultCacheTime,
      fallbackToCache = true
    } = options

    // 如果不强制刷新，检查缓存
    if (!forceRefresh) {
      const cachedData = this.getCachedUserInfo()
      if (cachedData && this.isCacheValid(cachedData.timestamp, cacheTime)) {
        return cachedData.data
      }
    }

    try {
      // 从服务器获取最新数据
      const userData = await UserService.getUserInfo()
      
      // 缓存数据
      this.setCachedUserInfo(userData)
      
      return userData
    } catch (error) {
      console.error('获取用户信息失败:', error)
      
      // 如果允许回退到缓存且有缓存数据
      if (fallbackToCache) {
        const cachedData = this.getCachedUserInfo()
        if (cachedData) {
          console.warn('使用缓存的用户信息数据')
          return cachedData.data
        }
      }
      
      throw error
    }
  }

  /**
   * 检查是否需要刷新用户信息
   */
  shouldRefresh(customCacheTime?: number): boolean {
    const cachedData = this.getCachedUserInfo()
    if (!cachedData) return true
    
    const cacheTime = customCacheTime || this.defaultCacheTime
    return !this.isCacheValid(cachedData.timestamp, cacheTime)
  }

  /**
   * 强制刷新用户信息
   */
  async forceRefresh(): Promise<any> {
    this.clearCache()
    return await this.getUserInfo({ forceRefresh: true })
  }

  /**
   * 预加载用户信息（后台静默更新）
   */
  async preloadUserInfo(): Promise<void> {
    try {
      const userData = await UserService.getUserInfo()
      this.setCachedUserInfo(userData)
    } catch (error) {
      console.warn('预加载用户信息失败:', error)
    }
  }

  /**
   * 获取缓存的用户信息
   */
  private getCachedUserInfo(): UserInfoCache | null {
    try {
      const cached = localStorage.getItem(this.cacheKey)
      if (!cached) return null
      
      return JSON.parse(cached)
    } catch (error) {
      console.warn('解析缓存用户信息失败:', error)
      return null
    }
  }

  /**
   * 设置缓存的用户信息
   */
  private setCachedUserInfo(data: any): void {
    try {
      const cacheData: UserInfoCache = {
        data,
        timestamp: Date.now(),
        version: '1.0'
      }
      
      localStorage.setItem(this.cacheKey, JSON.stringify(cacheData))
      localStorage.setItem(this.timestampKey, Date.now().toString())
    } catch (error) {
      console.warn('缓存用户信息失败:', error)
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(timestamp: number, cacheTime: number): boolean {
    return Date.now() - timestamp < cacheTime
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    localStorage.removeItem(this.cacheKey)
    localStorage.removeItem(this.timestampKey)
    localStorage.removeItem(this.versionKey)
  }

  /**
   * 获取缓存状态信息
   */
  getCacheStatus(): {
    hasCache: boolean
    isValid: boolean
    age: number
    remainingTime: number
  } {
    const cachedData = this.getCachedUserInfo()
    
    if (!cachedData) {
      return {
        hasCache: false,
        isValid: false,
        age: 0,
        remainingTime: 0
      }
    }

    const age = Date.now() - cachedData.timestamp
    const remainingTime = Math.max(0, this.defaultCacheTime - age)
    const isValid = this.isCacheValid(cachedData.timestamp, this.defaultCacheTime)

    return {
      hasCache: true,
      isValid,
      age,
      remainingTime
    }
  }
}

// 导出单例实例
export const userInfoManager = UserInfoManager.getInstance()

// 导出类型
export type { UserInfoCache }
