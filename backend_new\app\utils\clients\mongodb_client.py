"""
MongoDB连接客户端
支持MongoDB数据库的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional, List

from .base_client import BaseClient, ConnectionResult

try:
    from motor.motor_asyncio import AsyncIOMotorClient
    from pymongo.errors import ConnectionFailure, AuthenticationFailed, ServerSelectionTimeoutError
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False


class MongoDBClient(BaseClient):
    """
    MongoDB连接客户端
    支持MongoDB数据库的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化MongoDB客户端
        
        Args:
            config: MongoDB连接配置
                - host: MongoDB主机地址 (默认: localhost)
                - port: MongoDB端口 (默认: 27017)
                - username: 用户名 (可选)
                - password: 密码 (可选)
                - database: 数据库名 (可选)
                - auth_source: 认证数据库 (默认: admin)
                - replica_set: 副本集名称 (可选)
                - ssl: 是否使用SSL (默认: False)
                - connection_string: 完整连接字符串 (可选，优先级最高)
        """
        super().__init__(config)
        
        if not MONGODB_AVAILABLE:
            raise ImportError("motor库未安装，请运行: pip install motor")
        
        # 如果提供了完整的连接字符串，直接使用
        if config.get('connection_string'):
            self.connection_string = config['connection_string']
        else:
            # 构建连接字符串
            self.host = config.get('host', 'localhost')
            self.port = config.get('port', 27017)
            self.username = config.get('username')
            self.password = config.get('password')
            self.database = config.get('database')
            self.auth_source = config.get('auth_source', 'admin')
            self.replica_set = config.get('replica_set')
            self.ssl = config.get('ssl', False)
            
            self.connection_string = self._build_connection_string()
        
        self.mongo_client = None
        self.db = None

    def _build_connection_string(self) -> str:
        """构建MongoDB连接字符串"""
        # 基础连接字符串
        if self.username and self.password:
            conn_str = f"mongodb://{self.username}:{self.password}@{self.host}:{self.port}/"
        else:
            conn_str = f"mongodb://{self.host}:{self.port}/"
        
        # 添加参数
        params = []
        
        if self.auth_source and self.username:
            params.append(f"authSource={self.auth_source}")
        
        if self.replica_set:
            params.append(f"replicaSet={self.replica_set}")
        
        if self.ssl:
            params.append("ssl=true")
        
        if params:
            conn_str += "?" + "&".join(params)
        
        return conn_str

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立MongoDB连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 创建MongoDB客户端
            self.mongo_client = AsyncIOMotorClient(
                self.connection_string,
                serverSelectionTimeoutMS=timeout * 1000
            )
            
            # 测试连接
            await self.mongo_client.admin.command('ping')
            
            # 如果指定了数据库，获取数据库对象
            if hasattr(self, 'database') and self.database:
                self.db = self.mongo_client[self.database]
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到MongoDB服务器",
                duration=duration,
                details={
                    "host": getattr(self, 'host', '未知'),
                    "port": getattr(self, 'port', '未知'),
                    "database": getattr(self, 'database', '未指定'),
                    "auth": "是" if getattr(self, 'username', None) else "否",
                    "ssl": getattr(self, 'ssl', False)
                }
            )
            
        except ServerSelectionTimeoutError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MongoDB连接超时 ({timeout}秒) - 服务器不可达",
                duration=duration,
                details={"error_type": "ServerSelectionTimeoutError"}
            )
        except AuthenticationFailed:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message="MongoDB认证失败 - 用户名或密码错误",
                duration=duration,
                details={"error_type": "AuthenticationFailed"}
            )
        except ConnectionFailure as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MongoDB连接失败: {str(e)}",
                duration=duration,
                details={"error_type": "ConnectionFailure"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MongoDB连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开MongoDB连接"""
        try:
            if self.mongo_client:
                self.mongo_client.close()
                self.mongo_client = None
                self.db = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试MongoDB连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        # 如果已连接，先断开
        if self.is_connected:
            await self.disconnect()
        
        # 尝试连接
        result = await self.connect(timeout)
        
        # 如果连接成功，获取服务器信息
        if result.success:
            try:
                # 获取服务器状态
                server_status = await self.mongo_client.admin.command('serverStatus')
                
                if server_status:
                    result.details.update({
                        "mongodb_version": server_status.get('version', '未知'),
                        "uptime": server_status.get('uptime', 0),
                        "connections_current": server_status.get('connections', {}).get('current', 0),
                        "connections_available": server_status.get('connections', {}).get('available', 0)
                    })
                    
                    result.message += f" (版本: {server_status.get('version', '未知')})"
                
                # 获取数据库列表
                db_list = await self.mongo_client.list_database_names()
                result.details["databases"] = db_list
                result.details["database_count"] = len(db_list)
                
                # 如果指定了数据库，获取集合列表
                if hasattr(self, 'database') and self.database and self.db:
                    collections = await self.db.list_collection_names()
                    result.details["collections"] = collections[:10]  # 显示前10个集合
                    result.details["collection_count"] = len(collections)
                
            except Exception as e:
                result.details["info_test"] = f"获取服务器信息失败: {str(e)}"
        
        # 测试完成后断开连接
        if result.success:
            await self.disconnect()
        
        return result

    async def list_databases(self) -> List[str]:
        """
        获取数据库列表
        
        Returns:
            List[str]: 数据库名称列表
        """
        if not self.mongo_client:
            raise RuntimeError("MongoDB未连接")
        
        try:
            return await self.mongo_client.list_database_names()
        except Exception as e:
            raise RuntimeError(f"获取数据库列表失败: {str(e)}")

    async def list_collections(self, database_name: str = None) -> List[str]:
        """
        获取集合列表
        
        Args:
            database_name: 数据库名称，如果不指定则使用默认数据库
            
        Returns:
            List[str]: 集合名称列表
        """
        if not self.mongo_client:
            raise RuntimeError("MongoDB未连接")
        
        try:
            if database_name:
                db = self.mongo_client[database_name]
            elif self.db:
                db = self.db
            else:
                raise RuntimeError("未指定数据库")
            
            return await db.list_collection_names()
        except Exception as e:
            raise RuntimeError(f"获取集合列表失败: {str(e)}")

    async def insert_document(self, collection_name: str, document: Dict[str, Any], database_name: str = None) -> Dict[str, Any]:
        """
        插入文档
        
        Args:
            collection_name: 集合名称
            document: 要插入的文档
            database_name: 数据库名称
            
        Returns:
            Dict: 插入结果
        """
        if not self.mongo_client:
            raise RuntimeError("MongoDB未连接")
        
        try:
            if database_name:
                db = self.mongo_client[database_name]
            elif self.db:
                db = self.db
            else:
                raise RuntimeError("未指定数据库")
            
            collection = db[collection_name]
            result = await collection.insert_one(document)
            
            return {
                "success": True,
                "inserted_id": str(result.inserted_id),
                "acknowledged": result.acknowledged
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    async def find_documents(self, collection_name: str, filter_dict: Dict[str, Any] = None, limit: int = 10, database_name: str = None) -> Dict[str, Any]:
        """
        查找文档
        
        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            limit: 限制返回数量
            database_name: 数据库名称
            
        Returns:
            Dict: 查询结果
        """
        if not self.mongo_client:
            raise RuntimeError("MongoDB未连接")
        
        try:
            if database_name:
                db = self.mongo_client[database_name]
            elif self.db:
                db = self.db
            else:
                raise RuntimeError("未指定数据库")
            
            collection = db[collection_name]
            
            if filter_dict is None:
                filter_dict = {}
            
            cursor = collection.find(filter_dict).limit(limit)
            documents = []
            
            async for doc in cursor:
                # 转换ObjectId为字符串
                if '_id' in doc:
                    doc['_id'] = str(doc['_id'])
                documents.append(doc)
            
            return {
                "success": True,
                "documents": documents,
                "count": len(documents)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    async def get_collection_stats(self, collection_name: str, database_name: str = None) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Args:
            collection_name: 集合名称
            database_name: 数据库名称
            
        Returns:
            Dict: 集合统计信息
        """
        if not self.mongo_client:
            raise RuntimeError("MongoDB未连接")
        
        try:
            if database_name:
                db = self.mongo_client[database_name]
            elif self.db:
                db = self.db
            else:
                raise RuntimeError("未指定数据库")
            
            stats = await db.command("collStats", collection_name)
            
            return {
                "collection_name": collection_name,
                "document_count": stats.get('count', 0),
                "size": stats.get('size', 0),
                "storage_size": stats.get('storageSize', 0),
                "indexes": stats.get('nindexes', 0),
                "index_size": stats.get('totalIndexSize', 0)
            }
            
        except Exception as e:
            return {"error": str(e)}
