"""
数据导出器
提供各种格式的数据导出功能
"""
import json
import csv
import os
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import pandas as pd


class BaseExporter(ABC):
    """数据导出器基类"""
    
    @abstractmethod
    async def export(self, data: List[Dict[str, Any]], file_path: str, 
                    config: Optional[Dict[str, Any]] = None) -> str:
        """
        导出数据
        
        Args:
            data: 要导出的数据
            file_path: 文件路径
            config: 导出配置
            
        Returns:
            实际的文件路径
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证导出配置
        
        Args:
            config: 导出配置
            
        Returns:
            是否有效
        """
        pass


class JSONExporter(BaseExporter):
    """JSON格式导出器"""
    
    async def export(self, data: List[Dict[str, Any]], file_path: str, 
                    config: Optional[Dict[str, Any]] = None) -> str:
        """导出JSON格式"""
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 配置选项
        indent = config.get('indent', 2) if config else 2
        ensure_ascii = config.get('ensure_ascii', False) if config else False
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=ensure_ascii, indent=indent)
        
        return file_path
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证JSON导出配置"""
        if not config:
            return True
        
        indent = config.get('indent')
        if indent is not None and not isinstance(indent, int):
            return False
        
        ensure_ascii = config.get('ensure_ascii')
        if ensure_ascii is not None and not isinstance(ensure_ascii, bool):
            return False
        
        return True


class CSVExporter(BaseExporter):
    """CSV格式导出器"""
    
    async def export(self, data: List[Dict[str, Any]], file_path: str, 
                    config: Optional[Dict[str, Any]] = None) -> str:
        """导出CSV格式"""
        if not data:
            return file_path
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 配置选项
        delimiter = config.get('delimiter', ',') if config else ','
        encoding = config.get('encoding', 'utf-8-sig') if config else 'utf-8-sig'
        
        # 获取字段名
        fieldnames = data[0].keys()
        
        # 写入文件
        with open(file_path, 'w', newline='', encoding=encoding) as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, delimiter=delimiter)
            writer.writeheader()
            writer.writerows(data)
        
        return file_path
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证CSV导出配置"""
        if not config:
            return True
        
        delimiter = config.get('delimiter')
        if delimiter is not None and not isinstance(delimiter, str):
            return False
        
        encoding = config.get('encoding')
        if encoding is not None and not isinstance(encoding, str):
            return False
        
        return True


class ExcelExporter(BaseExporter):
    """Excel格式导出器"""
    
    async def export(self, data: List[Dict[str, Any]], file_path: str, 
                    config: Optional[Dict[str, Any]] = None) -> str:
        """导出Excel格式"""
        if not data:
            return file_path
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 配置选项
        sheet_name = config.get('sheet_name', 'Sheet1') if config else 'Sheet1'
        index = config.get('index', False) if config else False
        
        # 转换为DataFrame并导出
        df = pd.DataFrame(data)
        df.to_excel(file_path, sheet_name=sheet_name, index=index, engine='openpyxl')
        
        return file_path
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证Excel导出配置"""
        if not config:
            return True
        
        sheet_name = config.get('sheet_name')
        if sheet_name is not None and not isinstance(sheet_name, str):
            return False
        
        index = config.get('index')
        if index is not None and not isinstance(index, bool):
            return False
        
        return True


class DataExporterEngine:
    """数据导出引擎"""
    
    def __init__(self):
        self.exporters = {
            'json': JSONExporter(),
            'csv': CSVExporter(),
            'excel': ExcelExporter(),
        }
        
        # 确保导出目录存在
        self.export_dir = "exports"
        os.makedirs(self.export_dir, exist_ok=True)
    
    def is_format_supported(self, export_format: str) -> bool:
        """检查导出格式是否支持"""
        return export_format in self.exporters
    
    def validate_export_config(self, export_format: str, config: Optional[Dict[str, Any]]) -> bool:
        """验证导出配置"""
        if export_format not in self.exporters:
            return False
        
        if not config:
            return True
        
        exporter = self.exporters[export_format]
        return exporter.validate_config(config)
    
    async def export_data(self, data: List[Dict[str, Any]], export_format: str, 
                         filename: str, config: Optional[Dict[str, Any]] = None) -> str:
        """
        导出数据
        
        Args:
            data: 要导出的数据
            export_format: 导出格式
            filename: 文件名（不含扩展名）
            config: 导出配置
            
        Returns:
            导出文件的完整路径
        """
        if export_format not in self.exporters:
            raise ValueError(f"不支持的导出格式: {export_format}")
        
        # 生成文件路径
        extension = self._get_file_extension(export_format)
        file_path = os.path.join(self.export_dir, f"{filename}{extension}")
        
        # 执行导出
        exporter = self.exporters[export_format]
        return await exporter.export(data, file_path, config)
    
    def _get_file_extension(self, export_format: str) -> str:
        """获取文件扩展名"""
        extensions = {
            'json': '.json',
            'csv': '.csv',
            'excel': '.xlsx',
        }
        return extensions.get(export_format, '.txt')
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的导出格式列表"""
        return list(self.exporters.keys())
    
    def get_format_info(self, export_format: str) -> Optional[Dict[str, Any]]:
        """获取格式信息"""
        format_info = {
            'json': {
                'name': 'JSON',
                'description': '标准JSON格式',
                'extension': '.json',
                'mime_type': 'application/json'
            },
            'csv': {
                'name': 'CSV',
                'description': '逗号分隔值格式',
                'extension': '.csv',
                'mime_type': 'text/csv'
            },
            'excel': {
                'name': 'Excel',
                'description': 'Excel工作簿格式',
                'extension': '.xlsx',
                'mime_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
        }
        return format_info.get(export_format)
