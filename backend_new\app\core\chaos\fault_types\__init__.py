"""
故障类型处理器模块
提供各种故障类型的统一接口
"""
from typing import Dict, Optional
from .base_fault import BaseFaultHandler
from .cpu_fault import CPUFaultHandler
from .memory_fault import MemoryFaultHandler
from .network_fault import NetworkFaultHandler
from .disk_fault import DiskFaultHandler
from .process_fault import ProcessFaultHandler

# 故障处理器注册表
FAULT_HANDLERS: Dict[str, BaseFaultHandler] = {
    "cpu": CPUFaultHandler(),
    "memory": MemoryFaultHandler(),
    "network": NetworkFaultHandler(),
    "disk": DiskFaultHandler(),
    "process": ProcessFaultHandler(),
}

def get_fault_handler(fault_type: str) -> Optional[BaseFaultHandler]:
    """
    获取故障处理器
    
    Args:
        fault_type: 故障类型
        
    Returns:
        BaseFaultHandler: 故障处理器实例，如果不支持则返回None
    """
    return FAULT_HANDLERS.get(fault_type.lower())

def get_supported_fault_types() -> list:
    """获取支持的故障类型列表"""
    return list(FAULT_HANDLERS.keys())

def register_fault_handler(fault_type: str, handler: BaseFaultHandler):
    """
    注册新的故障处理器
    
    Args:
        fault_type: 故障类型
        handler: 故障处理器实例
    """
    FAULT_HANDLERS[fault_type.lower()] = handler

__all__ = [
    "get_fault_handler",
    "get_supported_fault_types", 
    "register_fault_handler",
    "BaseFaultHandler",
    "CPUFaultHandler",
    "MemoryFaultHandler",
    "NetworkFaultHandler",
    "DiskFaultHandler",
    "ProcessFaultHandler",
]
