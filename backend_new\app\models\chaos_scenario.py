"""
混沌测试场景模板数据模型
"""
from sqlalchemy import Column, String, Integer, Text, JSON, Boolean
from app.models.base import BaseModel


class ChaosScenario(BaseModel):
    """
    混沌测试场景模板模型
    预定义的故障注入场景，可复用于创建任务
    """
    __tablename__ = "chaos_scenarios"

    # 基本信息
    name = Column(String(100), nullable=False, index=True, comment="场景名称")
    description = Column(Text, nullable=True, comment="场景描述")
    category = Column(String(50), nullable=True, comment="场景分类")
    
    # 场景配置
    fault_type = Column(String(50), nullable=False, comment="故障类型：cpu/memory/network/disk/process/k8s")
    fault_params_template = Column(JSON, nullable=False, comment="故障参数模板")
    
    # 默认配置
    default_duration = Column(Integer, nullable=True, comment="默认持续时间(秒)")
    default_auto_destroy = Column(Boolean, default=True, comment="默认是否自动销毁")
    
    # 适用性
    applicable_platforms = Column(JSON, nullable=True, comment="适用平台列表")
    required_tools = Column(JSON, nullable=True, comment="所需工具列表")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_builtin = Column(Boolean, default=False, comment="是否内置场景")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    
    def __repr__(self):
        return f"<ChaosScenario(id={self.id}, name='{self.name}', fault_type='{self.fault_type}')>"
    
    def to_task_config(self) -> dict:
        """转换为任务配置"""
        return {
            "fault_type": self.fault_type,
            "fault_params": self.fault_params_template,
            "max_duration": self.default_duration,
            "auto_destroy": self.default_auto_destroy
        }
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count = (self.usage_count or 0) + 1
