"""
定时任务装饰器
提供便捷的任务注册方式
"""
import inspect
import logging
from datetime import datetime
from typing import Optional, Callable, Any, Union
from functools import wraps

from .schedule_manager import schedule_manager, create_task

logger = logging.getLogger(__name__)

# 存储待注册的任务
_pending_tasks = []


def scheduled_task(
    task_id: str,
    name: str,
    task_type: str = "general",
    description: Optional[str] = None,
    max_instances: int = 1,
    misfire_grace_time: int = 300
):
    """
    通用定时任务装饰器
    
    Args:
        task_id: 任务ID
        name: 任务名称
        task_type: 任务类型
        description: 任务描述
        max_instances: 最大实例数
        misfire_grace_time: 错过执行宽限时间
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if inspect.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        # 创建任务对象
        task = create_task(
            task_id=task_id,
            name=name,
            func=func,
            task_type=task_type,
            description=description,
            max_instances=max_instances,
            misfire_grace_time=misfire_grace_time
        )
        
        # 将任务信息存储到函数属性中
        wrapper._schedule_task = task
        wrapper._schedule_type = "manual"  # 需要手动添加到调度器
        
        return wrapper
    
    return decorator


def interval_task(
    task_id: str,
    name: str,
    seconds: int = 0,
    minutes: int = 0,
    hours: int = 0,
    days: int = 0,
    task_type: str = "general",
    description: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    max_instances: int = 1,
    misfire_grace_time: int = 300,
    auto_register: bool = True
):
    """
    间隔执行任务装饰器
    
    Args:
        task_id: 任务ID
        name: 任务名称
        seconds: 间隔秒数
        minutes: 间隔分钟数
        hours: 间隔小时数
        days: 间隔天数
        task_type: 任务类型
        description: 任务描述
        start_date: 开始时间
        end_date: 结束时间
        max_instances: 最大实例数
        misfire_grace_time: 错过执行宽限时间
        auto_register: 是否自动注册到调度器
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if inspect.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        # 创建任务对象
        task = create_task(
            task_id=task_id,
            name=name,
            func=func,
            task_type=task_type,
            description=description,
            max_instances=max_instances,
            misfire_grace_time=misfire_grace_time
        )
        
        # 存储调度信息
        wrapper._schedule_task = task
        wrapper._schedule_type = "interval"
        wrapper._schedule_config = {
            'seconds': seconds,
            'minutes': minutes,
            'hours': hours,
            'days': days,
            'start_date': start_date,
            'end_date': end_date
        }
        
        # 如果启用自动注册，添加到待注册列表
        if auto_register:
            _pending_tasks.append({
                'type': 'interval',
                'task': task,
                'config': wrapper._schedule_config
            })
        
        return wrapper
    
    return decorator


def cron_task(
    task_id: str,
    name: str,
    year: Union[int, str] = None,
    month: Union[int, str] = None,
    day: Union[int, str] = None,
    week: Union[int, str] = None,
    day_of_week: Union[int, str] = None,
    hour: Union[int, str] = None,
    minute: Union[int, str] = None,
    second: Union[int, str] = None,
    task_type: str = "general",
    description: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    max_instances: int = 1,
    misfire_grace_time: int = 300,
    auto_register: bool = True
):
    """
    Cron表达式任务装饰器
    
    Args:
        task_id: 任务ID
        name: 任务名称
        year: 年
        month: 月
        day: 日
        week: 周
        day_of_week: 星期几
        hour: 小时
        minute: 分钟
        second: 秒
        task_type: 任务类型
        description: 任务描述
        start_date: 开始时间
        end_date: 结束时间
        max_instances: 最大实例数
        misfire_grace_time: 错过执行宽限时间
        auto_register: 是否自动注册到调度器
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if inspect.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        # 创建任务对象
        task = create_task(
            task_id=task_id,
            name=name,
            func=func,
            task_type=task_type,
            description=description,
            max_instances=max_instances,
            misfire_grace_time=misfire_grace_time
        )
        
        # 存储调度信息
        wrapper._schedule_task = task
        wrapper._schedule_type = "cron"
        wrapper._schedule_config = {
            'year': year,
            'month': month,
            'day': day,
            'week': week,
            'day_of_week': day_of_week,
            'hour': hour,
            'minute': minute,
            'second': second,
            'start_date': start_date,
            'end_date': end_date
        }
        
        # 如果启用自动注册，添加到待注册列表
        if auto_register:
            _pending_tasks.append({
                'type': 'cron',
                'task': task,
                'config': wrapper._schedule_config
            })
        
        return wrapper
    
    return decorator


def date_task(
    task_id: str,
    name: str,
    run_date: datetime,
    task_type: str = "general",
    description: Optional[str] = None,
    max_instances: int = 1,
    misfire_grace_time: int = 300,
    auto_register: bool = True
):
    """
    单次执行任务装饰器
    
    Args:
        task_id: 任务ID
        name: 任务名称
        run_date: 执行时间
        task_type: 任务类型
        description: 任务描述
        max_instances: 最大实例数
        misfire_grace_time: 错过执行宽限时间
        auto_register: 是否自动注册到调度器
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if inspect.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        # 创建任务对象
        task = create_task(
            task_id=task_id,
            name=name,
            func=func,
            task_type=task_type,
            description=description,
            max_instances=max_instances,
            misfire_grace_time=misfire_grace_time
        )
        
        # 存储调度信息
        wrapper._schedule_task = task
        wrapper._schedule_type = "date"
        wrapper._schedule_config = {
            'run_date': run_date
        }
        
        # 如果启用自动注册，添加到待注册列表
        if auto_register:
            _pending_tasks.append({
                'type': 'date',
                'task': task,
                'config': wrapper._schedule_config
            })
        
        return wrapper
    
    return decorator


async def register_pending_tasks():
    """注册所有待注册的任务"""
    global _pending_tasks
    
    if not _pending_tasks:
        logger.info("没有待注册的定时任务")
        return
    
    logger.info(f"开始注册 {len(_pending_tasks)} 个定时任务")
    
    for task_info in _pending_tasks:
        try:
            task_type = task_info['type']
            task = task_info['task']
            config = task_info['config']
            
            if task_type == 'interval':
                schedule_manager.add_interval_task(task, **config)
            elif task_type == 'cron':
                schedule_manager.add_cron_task(task, **config)
            elif task_type == 'date':
                schedule_manager.add_date_task(task, **config)
            
            logger.info(f"注册定时任务成功: {task.name} ({task.task_id})")
            
        except Exception as e:
            logger.error(f"注册定时任务失败: {task.name} ({task.task_id}), 错误: {str(e)}")
    
    # 清空待注册列表
    _pending_tasks.clear()
    logger.info("定时任务注册完成")


def get_pending_tasks():
    """获取待注册的任务列表"""
    return _pending_tasks.copy()


def clear_pending_tasks():
    """清空待注册的任务列表"""
    global _pending_tasks
    _pending_tasks.clear()
