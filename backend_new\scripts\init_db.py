"""
数据库初始化脚本
创建默认的超级管理员用户
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import AsyncSessionLocal, init_db
from app.models.user import User, Role
from app.core.security import get_password_hash
from app.core.config import settings


async def create_default_superuser():
    """创建默认超级管理员用户"""
    async with AsyncSessionLocal() as session:
        try:
            # 检查是否已存在超级管理员
            from sqlalchemy import select
            result = await session.execute(
                select(User).where(User.username == "admin")
            )
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                print("超级管理员用户已存在")
                return
            
            # 创建超级管理员用户
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                nickname="系统管理员",
                hashed_password=get_password_hash("admin123"),
                is_active=True,
                is_superuser=True,
                status="1",
                description="系统默认超级管理员"
            )
            
            session.add(admin_user)
            await session.commit()
            await session.refresh(admin_user)
            
            print(f"创建超级管理员成功:")
            print(f"  用户名: admin")
            print(f"  密码: admin123")
            print(f"  邮箱: <EMAIL>")
            print(f"  用户ID: {admin_user.id}")
            
        except Exception as e:
            print(f"创建超级管理员失败: {e}")
            await session.rollback()
            raise


async def create_default_roles():
    """创建默认角色"""
    async with AsyncSessionLocal() as session:
        try:
            from sqlalchemy import select
            
            # 默认角色列表
            default_roles = [
                {
                    "name": "超级管理员",
                    "code": "super_admin",
                    "description": "系统超级管理员，拥有所有权限"
                },
                {
                    "name": "管理员",
                    "code": "admin",
                    "description": "系统管理员，拥有大部分管理权限"
                },
                {
                    "name": "普通用户",
                    "code": "user",
                    "description": "普通用户，拥有基本功能权限"
                }
            ]
            
            for role_data in default_roles:
                # 检查角色是否已存在
                result = await session.execute(
                    select(Role).where(Role.code == role_data["code"])
                )
                existing_role = result.scalar_one_or_none()
                
                if not existing_role:
                    role = Role(**role_data)
                    session.add(role)
                    print(f"创建角色: {role_data['name']} ({role_data['code']})")
            
            await session.commit()
            print("默认角色创建完成")
            
        except Exception as e:
            print(f"创建默认角色失败: {e}")
            await session.rollback()
            raise


async def main():
    """主函数"""
    print("开始初始化数据库...")
    
    try:
        # 初始化数据库表
        await init_db()
        print("数据库表创建完成")
        
        # 创建默认角色
        await create_default_roles()
        
        # 创建默认超级管理员
        await create_default_superuser()
        
        print("数据库初始化完成!")
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
