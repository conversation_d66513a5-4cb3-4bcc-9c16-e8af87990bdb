"""
Redis连接客户端
支持Redis服务器的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional

from .base_client import BaseClient, ConnectionResult

try:
    import aioredis
    REDIS_AVAILABLE = True
except (ImportError, TypeError):
    # TypeError可能由于Python 3.11的兼容性问题导致
    REDIS_AVAILABLE = False


class RedisClient(BaseClient):
    """
    Redis连接客户端
    支持Redis服务器的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Redis客户端
        
        Args:
            config: Redis连接配置
                - host: Redis主机地址 (默认: localhost)
                - port: Redis端口 (默认: 6379)
                - password: Redis密码 (可选)
                - db: 数据库编号 (默认: 0)
                - username: Redis用户名 (可选，Redis 6.0+)
                - ssl: 是否使用SSL (默认: False)
                - decode_responses: 是否解码响应 (默认: True)
        """
        super().__init__(config)
        
        if not REDIS_AVAILABLE:
            raise ImportError("aioredis库未安装，请运行: pip install aioredis")
        
        # 设置默认值
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 6379)
        self.password = config.get('password')
        self.db = config.get('db', 0)
        self.username = config.get('username')
        self.ssl = config.get('ssl', False)
        self.decode_responses = config.get('decode_responses', True)
        
        self.redis_client = None

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立Redis连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 构建Redis URL
            if self.ssl:
                url = f"rediss://"
            else:
                url = f"redis://"
            
            if self.username and self.password:
                url += f"{self.username}:{self.password}@"
            elif self.password:
                url += f":{self.password}@"
            
            url += f"{self.host}:{self.port}/{self.db}"
            
            # 建立Redis连接
            self.redis_client = aioredis.from_url(
                url,
                decode_responses=self.decode_responses,
                socket_timeout=timeout,
                socket_connect_timeout=timeout
            )
            
            # 测试连接
            await asyncio.wait_for(self.redis_client.ping(), timeout=timeout)
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到Redis服务器 {self.host}:{self.port}",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "db": self.db,
                    "ssl": self.ssl,
                    "auth": "是" if self.password else "否"
                }
            )
            
        except (asyncio.TimeoutError, TimeoutError):
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis连接超时 ({timeout}秒)",
                duration=duration,
                details={"error_type": "TimeoutError"}
            )
        except ConnectionRefusedError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis连接被拒绝 - 请检查Redis服务是否运行",
                duration=duration,
                details={"error_type": "ConnectionRefusedError"}
            )
        except aioredis.AuthenticationError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message="Redis认证失败 - 请检查用户名和密码",
                duration=duration,
                details={"error_type": "AuthenticationError"}
            )
        except aioredis.ResponseError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis响应错误: {str(e)}",
                duration=duration,
                details={"error_type": "ResponseError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开Redis连接"""
        try:
            if self.redis_client:
                await self.redis_client.close()
                self.redis_client = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试Redis连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        # 如果已连接，先断开
        if self.is_connected:
            await self.disconnect()
        
        # 尝试连接
        result = await self.connect(timeout)
        
        # 如果连接成功，获取服务器信息
        if result.success:
            try:
                # 获取Redis服务器信息
                info = await self.redis_client.info()
                
                if info:
                    result.details.update({
                        "redis_version": info.get("redis_version", "未知"),
                        "redis_mode": info.get("redis_mode", "未知"),
                        "used_memory_human": info.get("used_memory_human", "未知"),
                        "connected_clients": info.get("connected_clients", 0),
                        "uptime_in_seconds": info.get("uptime_in_seconds", 0)
                    })
                    
                    result.message += f" (版本: {info.get('redis_version', '未知')})"
                
                # 测试基本操作
                test_key = "connection_test"
                await self.redis_client.set(test_key, "test_value", ex=10)  # 10秒过期
                test_value = await self.redis_client.get(test_key)
                await self.redis_client.delete(test_key)
                
                if test_value == "test_value":
                    result.details["operation_test"] = "读写操作正常"
                else:
                    result.details["operation_test"] = "读写操作异常"
                
            except Exception as e:
                result.details["info_test"] = f"获取服务器信息失败: {str(e)}"
        
        # 测试完成后断开连接
        if result.success:
            await self.disconnect()
        
        return result

    async def execute_command(self, command: str, *args) -> Dict[str, Any]:
        """
        执行Redis命令
        
        Args:
            command: Redis命令
            *args: 命令参数
            
        Returns:
            Dict: 命令执行结果
        """
        if not self.redis_client:
            raise RuntimeError("Redis未连接")
        
        try:
            result = await self.redis_client.execute_command(command, *args)
            
            return {
                "success": True,
                "result": result,
                "command": f"{command} {' '.join(map(str, args))}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": f"{command} {' '.join(map(str, args))}"
            }

    async def get_server_info(self) -> Dict[str, Any]:
        """
        获取Redis服务器信息
        
        Returns:
            Dict: 服务器信息
        """
        if not self.redis_client:
            raise RuntimeError("Redis未连接")
        
        try:
            info = await self.redis_client.info()
            
            # 提取关键信息
            key_info = {
                "redis_version": info.get("redis_version"),
                "redis_mode": info.get("redis_mode"),
                "os": info.get("os"),
                "arch_bits": info.get("arch_bits"),
                "multiplexing_api": info.get("multiplexing_api"),
                "process_id": info.get("process_id"),
                "tcp_port": info.get("tcp_port"),
                "uptime_in_seconds": info.get("uptime_in_seconds"),
                "uptime_in_days": info.get("uptime_in_days"),
                "connected_clients": info.get("connected_clients"),
                "used_memory": info.get("used_memory"),
                "used_memory_human": info.get("used_memory_human"),
                "used_memory_peak_human": info.get("used_memory_peak_human"),
                "total_connections_received": info.get("total_connections_received"),
                "total_commands_processed": info.get("total_commands_processed"),
                "keyspace_hits": info.get("keyspace_hits"),
                "keyspace_misses": info.get("keyspace_misses"),
            }
            
            # 过滤掉None值
            return {k: v for k, v in key_info.items() if v is not None}
            
        except Exception as e:
            return {"error": str(e)}

    async def test_operations(self) -> Dict[str, Any]:
        """
        测试基本Redis操作
        
        Returns:
            Dict: 操作测试结果
        """
        if not self.redis_client:
            raise RuntimeError("Redis未连接")
        
        results = {}
        test_key = "redis_client_test"
        
        try:
            # 测试SET/GET
            await self.redis_client.set(test_key, "test_value")
            value = await self.redis_client.get(test_key)
            results["string_operations"] = value == "test_value"
            
            # 测试LIST操作
            list_key = f"{test_key}_list"
            await self.redis_client.lpush(list_key, "item1", "item2")
            list_length = await self.redis_client.llen(list_key)
            results["list_operations"] = list_length == 2
            
            # 测试HASH操作
            hash_key = f"{test_key}_hash"
            await self.redis_client.hset(hash_key, "field1", "value1")
            hash_value = await self.redis_client.hget(hash_key, "field1")
            results["hash_operations"] = hash_value == "value1"
            
            # 清理测试数据
            await self.redis_client.delete(test_key, list_key, hash_key)
            
            results["cleanup"] = True
            
        except Exception as e:
            results["error"] = str(e)
        
        return results
