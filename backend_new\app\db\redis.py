"""
Redis缓存管理模块
配置Redis连接和缓存操作
"""
import json
import pickle
from typing import Any, Optional, Union
import redis.asyncio as redis
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class RedisClient:
    """Redis客户端封装类"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
    
    async def init_redis(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.from_url(
                settings.redis_url,
                encoding="utf-8",
                decode_responses=False,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
            raise
    
    async def close_redis(self):
        """关闭Redis连接"""
        if self.redis_client:
            try:
                await self.redis_client.close()
                logger.info("Redis connection closed")
            except Exception as e:
                logger.error(f"Failed to close Redis connection: {e}")
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[int] = None,
        serialize: bool = True
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒）
            serialize: 是否序列化值
            
        Returns:
            操作是否成功
        """
        if not self.redis_client:
            return False
        
        try:
            if serialize:
                if isinstance(value, (dict, list)):
                    value = json.dumps(value, ensure_ascii=False)
                else:
                    value = pickle.dumps(value)
            
            result = await self.redis_client.set(key, value, ex=expire)
            return bool(result)
            
        except Exception as e:
            logger.error(f"Failed to set cache {key}: {e}")
            return False
    
    async def get(
        self, 
        key: str, 
        deserialize: bool = True
    ) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            deserialize: 是否反序列化值
            
        Returns:
            缓存值或None
        """
        if not self.redis_client:
            return None
        
        try:
            value = await self.redis_client.get(key)
            if value is None:
                return None
            
            if deserialize:
                try:
                    # 尝试JSON反序列化
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    try:
                        # 尝试pickle反序列化
                        return pickle.loads(value)
                    except (pickle.PickleError, TypeError):
                        # 返回原始值
                        return value.decode('utf-8') if isinstance(value, bytes) else value
            
            return value.decode('utf-8') if isinstance(value, bytes) else value
            
        except Exception as e:
            logger.error(f"Failed to get cache {key}: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            操作是否成功
        """
        if not self.redis_client:
            return False
        
        try:
            result = await self.redis_client.delete(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to delete cache {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            缓存是否存在
        """
        if not self.redis_client:
            return False
        
        try:
            result = await self.redis_client.exists(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to check cache existence {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """
        设置缓存过期时间

        Args:
            key: 缓存键
            seconds: 过期时间（秒）

        Returns:
            操作是否成功
        """
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.expire(key, seconds)
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to set expiration for cache {key}: {e}")
            return False

    async def mget(self, keys: list) -> list:
        """
        批量获取缓存值

        Args:
            keys: 缓存键列表

        Returns:
            缓存值列表，不存在的键返回None
        """
        if not self.redis_client or not keys:
            return [None] * len(keys)

        try:
            results = await self.redis_client.mget(keys)
            # 处理反序列化
            processed_results = []
            for result in results:
                if result is None:
                    processed_results.append(None)
                else:
                    try:
                        # 尝试JSON反序列化
                        if isinstance(result, bytes):
                            result = result.decode('utf-8')
                        processed_results.append(result)
                    except Exception:
                        # 如果JSON反序列化失败，尝试pickle
                        try:
                            processed_results.append(pickle.loads(result))
                        except Exception:
                            processed_results.append(result)

            return processed_results

        except Exception as e:
            logger.error(f"Failed to mget cache {keys}: {e}")
            return [None] * len(keys)

    async def mset(self, mapping: dict, expire: Optional[int] = None) -> bool:
        """
        批量设置缓存值

        Args:
            mapping: 键值对字典
            expire: 过期时间（秒）

        Returns:
            操作是否成功
        """
        if not self.redis_client or not mapping:
            return False

        try:
            # 序列化值
            processed_mapping = {}
            for key, value in mapping.items():
                if isinstance(value, (dict, list)):
                    processed_mapping[key] = json.dumps(value, ensure_ascii=False)
                elif not isinstance(value, (str, bytes)):
                    processed_mapping[key] = pickle.dumps(value)
                else:
                    processed_mapping[key] = value

            # 批量设置
            result = await self.redis_client.mset(processed_mapping)

            # 如果设置了过期时间，需要为每个键单独设置
            if expire and result:
                for key in processed_mapping.keys():
                    await self.redis_client.expire(key, expire)

            return bool(result)

        except Exception as e:
            logger.error(f"Failed to mset cache {list(mapping.keys())}: {e}")
            return False


# 创建全局Redis客户端实例
redis_client = RedisClient()


async def get_redis() -> RedisClient:
    """
    获取Redis客户端
    用于依赖注入
    """
    return redis_client
