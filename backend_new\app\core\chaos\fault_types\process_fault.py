"""
进程故障处理器
实现进程相关的故障注入和销毁
"""
import logging
from typing import Dict, Any
from .base_fault import BaseFaultHandler, FaultResult

logger = logging.getLogger(__name__)


class ProcessFaultHandler(BaseFaultHandler):
    """进程故障处理器"""
    
    async def inject(
        self, 
        ssh_client, 
        fault_params: Dict[str, Any],
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """
        注入进程故障
        
        支持的参数:
        - fault_action: 故障动作 (kill/stop)
        - process: 进程名称
        - process-cmd: 进程命令
        - pid: 进程ID
        - signal: 信号类型 (用于kill动作)
        - timeout: 持续时间(秒)
        """
        # 验证参数
        is_valid, error_msg = self.validate_params(fault_params)
        if not is_valid:
            return FaultResult(
                success=False,
                fault_id="",
                message=f"参数验证失败: {error_msg}"
            )
        
        # 构建命令参数
        cmd_params = {}
        fault_action = fault_params.get("fault_action", "kill")
        
        # 进程标识参数
        if "process" in fault_params:
            cmd_params["process"] = fault_params["process"]
        
        if "process_cmd" in fault_params:
            cmd_params["process-cmd"] = fault_params["process_cmd"]
        
        if "pid" in fault_params:
            cmd_params["pid"] = fault_params["pid"]
        
        # 信号类型 (用于kill动作)
        if fault_action == "kill" and "signal" in fault_params:
            cmd_params["signal"] = fault_params["signal"]
        
        # 持续时间
        if "timeout" in fault_params:
            cmd_params["timeout"] = fault_params["timeout"]
        
        # 构建ChaosBlade命令
        command = self._build_chaosblade_command(
            action="create",
            target=f"process {fault_action}",
            params=cmd_params,
            chaosblade_path=chaosblade_path
        )
        
        # 执行命令
        return await self._execute_chaosblade_command(ssh_client, command)
    
    async def destroy(
        self, 
        ssh_client, 
        fault_id: str,
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """销毁进程故障"""
        command = self._build_destroy_command(fault_id, chaosblade_path)
        return await self._execute_chaosblade_command(ssh_client, command)
    
    def validate_params(self, fault_params: Dict[str, Any]) -> tuple[bool, str]:
        """验证进程故障参数"""
        # 验证故障动作
        fault_action = fault_params.get("fault_action", "kill")
        valid_actions = ["kill", "stop"]
        if fault_action not in valid_actions:
            return False, f"fault_action必须是{valid_actions}中的一个"
        
        # 必须指定进程标识参数中的至少一个
        process_identifiers = ["process", "process_cmd", "pid"]
        if not any(param in fault_params for param in process_identifiers):
            return False, f"必须指定{process_identifiers}中的至少一个参数"
        
        # 验证进程名称
        process = fault_params.get("process")
        if process and not isinstance(process, str):
            return False, "process必须是字符串"
        
        # 验证进程命令
        process_cmd = fault_params.get("process_cmd")
        if process_cmd and not isinstance(process_cmd, str):
            return False, "process_cmd必须是字符串"
        
        # 验证进程ID
        pid = fault_params.get("pid")
        if pid is not None:
            if not isinstance(pid, int) or pid <= 0:
                return False, "pid必须是正整数"
        
        # 验证信号类型
        signal = fault_params.get("signal")
        if signal is not None:
            valid_signals = ["TERM", "KILL", "INT", "HUP", "QUIT", "USR1", "USR2"]
            if signal not in valid_signals:
                return False, f"signal必须是{valid_signals}中的一个"
        
        # 验证超时时间
        timeout = fault_params.get("timeout")
        if timeout is not None:
            if not isinstance(timeout, int) or timeout <= 0:
                return False, "timeout必须是正整数"
        
        return True, ""
    
    def get_param_schema(self) -> Dict[str, Any]:
        """获取进程故障参数结构定义"""
        return {
            "type": "object",
            "properties": {
                "fault_action": {
                    "type": "string",
                    "enum": ["kill", "stop"],
                    "description": "故障动作类型",
                    "default": "kill"
                },
                "process": {
                    "type": "string",
                    "description": "进程名称",
                    "required": False
                },
                "process_cmd": {
                    "type": "string",
                    "description": "进程命令",
                    "required": False
                },
                "pid": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "进程ID",
                    "required": False
                },
                "signal": {
                    "type": "string",
                    "enum": ["TERM", "KILL", "INT", "HUP", "QUIT", "USR1", "USR2"],
                    "description": "信号类型，用于kill动作",
                    "default": "TERM"
                },
                "timeout": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "持续时间(秒)，不指定则持续到手动销毁",
                    "required": False
                }
            },
            "anyOf": [
                {"required": ["process"]},
                {"required": ["process_cmd"]},
                {"required": ["pid"]}
            ],
            "additionalProperties": False
        }
