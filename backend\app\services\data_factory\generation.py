"""
数据生成服务
提供数据生成任务的业务逻辑处理
"""
import asyncio
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.data_factory.generation_task import GenerationTaskRepository
from app.repositories.data_factory.data_model import DataModelRepository
from app.models.data_factory.generation_task import GenerationTask
from app.schemas.data_factory.generation_task import (
    GenerationTaskCreate, 
    GenerationTaskUpdate, 
    GenerationTaskResponse,
    TaskStatusUpdate
)
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.data_factory.generators import DataGeneratorEngine
from app.utils.data_factory.exporters import DataExporterEngine


class DataGenerationService(BaseService[GenerationTask, GenerationTaskCreate, GenerationTaskUpdate, GenerationTaskResponse]):
    """
    数据生成服务类
    继承BaseService，提供数据生成任务的业务逻辑处理
    """

    def __init__(self, db: AsyncSession):
        repository = GenerationTaskRepository(db)
        super().__init__(db, repository)
        self.model_repository = DataModelRepository(db)
        self.generator_engine = DataGeneratorEngine()
        self.exporter_engine = DataExporterEngine()

    @property
    def model_class(self):
        """返回模型类"""
        return GenerationTask

    @property
    def response_schema_class(self):
        """返回响应Schema类"""
        return GenerationTaskResponse

    async def _validate_before_create(self, create_data: GenerationTaskCreate, **kwargs) -> None:
        """
        创建前验证
        检查数据模型是否存在和有效
        """
        # 检查数据模型是否存在
        model = await self.model_repository.get(create_data.model_id)
        if not model:
            raise_not_found(f"数据模型ID {create_data.model_id} 不存在")
        
        # 检查模型是否启用
        if model.status != "1":
            raise_validation_error(f"数据模型 '{model.name}' 已禁用，无法创建生成任务")
        
        # 验证导出格式
        if not self.exporter_engine.is_format_supported(create_data.export_format):
            raise_validation_error(f"不支持的导出格式: {create_data.export_format}")

    async def create_generation_task(self, task_data: GenerationTaskCreate, current_user: str) -> GenerationTaskResponse:
        """
        创建数据生成任务
        
        Args:
            task_data: 任务创建数据
            current_user: 当前用户
            
        Returns:
            任务响应数据
        """
        # 创建任务
        task = await self.create(task_data, current_user)
        
        # 异步执行数据生成
        asyncio.create_task(self._execute_generation_task(task.id))
        
        return task

    async def _execute_generation_task(self, task_id: int) -> None:
        """
        执行数据生成任务
        
        Args:
            task_id: 任务ID
        """
        try:
            # 更新任务状态为运行中
            await self.repository.update_task_status(task_id, "running", 0)
            
            # 获取任务和模型信息
            task = await self.repository.get_with_model(task_id)
            if not task or not task.model:
                await self.repository.update_task_status(
                    task_id, "failed", 0, "任务或模型信息不存在"
                )
                return
            
            # 根据数据量选择生成策略
            if task.record_count <= 1000:
                await self._instant_generate(task)
            elif task.record_count <= 50000:
                await self._batch_generate(task)
            else:
                await self.repository.update_task_status(
                    task_id, "failed", 0, "数据量超出MVP版本限制（最大50000条）"
                )
                return
            
            # 更新任务状态为完成
            await self.repository.update_task_status(task_id, "completed", 100)
            
        except Exception as e:
            # 更新任务状态为失败
            await self.repository.update_task_status(
                task_id, "failed", 0, str(e)
            )

    async def _instant_generate(self, task: GenerationTask) -> None:
        """
        即时生成数据（≤1000条）
        
        Args:
            task: 生成任务
        """
        # 生成数据
        data = await self.generator_engine.generate_data(
            task.model.fields_config, task.record_count
        )
        
        # 更新进度
        await self.repository.update_task_status(task.id, "running", 50)
        
        # 导出数据
        from app.utils.timezone import now
        filename = f"task_{task.id}_{now().strftime('%Y%m%d_%H%M%S')}"
        file_path = await self.exporter_engine.export_data(
            data, task.export_format, filename, task.export_config
        )
        
        # 更新任务结果
        task.result_file_path = file_path
        task.result_file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        await self.repository.db.commit()

    async def _batch_generate(self, task: GenerationTask) -> None:
        """
        批量生成数据（1000-50000条）
        
        Args:
            task: 生成任务
        """
        batch_size = 5000
        all_data = []
        
        for i in range(0, task.record_count, batch_size):
            batch_count = min(batch_size, task.record_count - i)
            
            # 生成批次数据
            batch_data = await self.generator_engine.generate_data(
                task.model.fields_config, batch_count
            )
            all_data.extend(batch_data)
            
            # 更新进度
            progress = int((i + batch_count) / task.record_count * 80)  # 80%用于生成
            await self.repository.update_task_status(task.id, "running", progress)
        
        # 导出数据
        filename = f"task_{task.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        file_path = await self.exporter_engine.export_data(
            all_data, task.export_format, filename, task.export_config
        )
        
        # 更新任务结果
        task.result_file_path = file_path
        task.result_file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        await self.repository.db.commit()

    async def get_task_with_model(self, task_id: int) -> GenerationTaskResponse:
        """
        获取任务及其关联模型信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务响应数据
        """
        task = await self.repository.get_with_model(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")
        
        return self._convert_to_response(task)

    async def get_tasks_by_status(self, status: str, skip: int = 0, limit: int = 100) -> List[GenerationTaskResponse]:
        """
        根据状态获取任务列表
        
        Args:
            status: 任务状态
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            任务响应列表
        """
        tasks = await self.repository.get_by_status(status, skip, limit)
        return [self._convert_to_response(task) for task in tasks]

    async def get_tasks_by_model(self, model_id: int, skip: int = 0, limit: int = 100) -> List[GenerationTaskResponse]:
        """
        根据模型ID获取任务列表
        
        Args:
            model_id: 数据模型ID
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            任务响应列表
        """
        tasks = await self.repository.get_by_model_id(model_id, skip, limit)
        return [self._convert_to_response(task) for task in tasks]

    async def cancel_task(self, task_id: int) -> GenerationTaskResponse:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            更新后的任务响应
        """
        task = await self.repository.update_task_status(task_id, "cancelled")
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")
        
        return self._convert_to_response(task)

    async def retry_task(self, task_id: int) -> GenerationTaskResponse:
        """
        重试失败的任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            更新后的任务响应
        """
        task = await self.get_by_id(task_id)
        
        if task.status != "failed":
            raise_validation_error("只能重试失败的任务")
        
        # 重置任务状态
        updated_task = await self.repository.update_task_status(
            task_id, "pending", 0, None
        )
        
        # 异步执行数据生成
        asyncio.create_task(self._execute_generation_task(task_id))
        
        return self._convert_to_response(updated_task)

    async def get_task_statistics(self) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Returns:
            统计信息字典
        """
        return await self.repository.get_statistics()

    async def get_running_tasks(self) -> List[GenerationTaskResponse]:
        """
        获取正在运行的任务列表
        
        Returns:
            正在运行的任务列表
        """
        tasks = await self.repository.get_running_tasks()
        return [self._convert_to_response(task) for task in tasks]
