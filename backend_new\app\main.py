"""
FastAPI应用主入口
配置和启动FastAPI应用
"""
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.exceptions import RequestValidationError
from fastapi.staticfiles import StaticFiles
from starlette.exceptions import HTTPException as StarletteHTTPException
import time
import uuid
import os

from app.core.config import settings
from app.core.exceptions import BaseCustomException
from app.api.router import api_router
from app.api.v1.health import router as health_router
from app.db.session import init_db, close_db
from app.db.redis import redis_client
from app.utils.logger import setup_logging, app_logger
from app.schemas.base import response_builder
from app.middleware.context import ContextMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    app_logger.info("正在启动应用...")
    
    # 设置日志
    setup_logging()
    
    # 初始化数据库
    try:
        await init_db()
        app_logger.info("数据库初始化完成")
    except Exception as e:
        app_logger.error(f"数据库初始化失败: {e}")
        raise
    
    # 初始化Redis
    try:
        await redis_client.init_redis()
        app_logger.info("Redis初始化完成")
    except Exception as e:
        app_logger.warning(f"Redis初始化失败: {e}")
        # Redis失败不阻止应用启动
    
    app_logger.info("应用启动完成")
    
    yield
    
    # 关闭时执行
    app_logger.info("正在关闭应用...")
    
    # 关闭数据库连接
    try:
        await close_db()
        app_logger.info("数据库连接已关闭")
    except Exception as e:
        app_logger.error(f"关闭数据库连接失败: {e}")
    
    # 关闭Redis连接
    try:
        await redis_client.close_redis()
        app_logger.info("Redis连接已关闭")
    except Exception as e:
        app_logger.error(f"关闭Redis连接失败: {e}")
    
    app_logger.info("应用已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="DpTestPlatform 后端API服务",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 添加上下文中间件（自动设置当前用户）
app.add_middleware(ContextMiddleware)

# 添加可信主机中间件（生产环境建议启用）
# app.add_middleware(
#     TrustedHostMiddleware,
#     allowed_hosts=["localhost", "127.0.0.1", "*.example.com"]
# )


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间和请求ID"""
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    # 添加请求ID到请求状态
    request.state.request_id = request_id
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    response.headers["X-Request-ID"] = request_id
    
    # 记录访问日志
    app_logger.bind(
        request_id=request_id,
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time,
        extra={"access": True}
    ).info("API访问")
    
    return response


# 全局异常处理器
@app.exception_handler(BaseCustomException)
async def custom_exception_handler(request: Request, exc: BaseCustomException):
    """自定义异常处理器"""
    app_logger.error("自定义异常: {}", exc.detail)
    return JSONResponse(
        status_code=exc.status_code,
        content=response_builder.error(
            code=exc.status_code,
            message=exc.detail
        )
    )


@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP异常处理器"""
    app_logger.error("HTTP异常: {}", exc.detail)
    return JSONResponse(
        status_code=exc.status_code,
        content=response_builder.error(
            code=exc.status_code,
            message=exc.detail
        )
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    app_logger.error("请求验证异常: {}", exc.errors())
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=response_builder.validation_error(
            detail=f"请求数据验证失败: {exc.errors()}"
        )
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    app_logger.error("未处理的异常: {}", str(exc), exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=response_builder.error(
            code=500,
            message="服务器内部错误"
        )
    )


# 挂载静态文件服务
uploads_dir = "uploads"
if not os.path.exists(uploads_dir):
    os.makedirs(uploads_dir)

app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")

# 包含路由
app.include_router(health_router, prefix="/health", tags=["健康检查"])
app.include_router(api_router, prefix="/api")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
