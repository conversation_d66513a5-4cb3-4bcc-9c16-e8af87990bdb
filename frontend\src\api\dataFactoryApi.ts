import request from '@/utils/http'

/**
 * 数据工厂API服务
 */
export class DataFactoryService {
  
  // ==================== 数据模型管理 ====================
  
  /**
   * 获取数据模型列表
   */
  static async getDataModels(params: Api.DataFactory.ModelListParams): Promise<Api.Common.PaginationData<Api.DataFactory.ModelInfo>> {
    const response = await request.get<Api.Common.PaginationData<Api.DataFactory.ModelInfo>>({
      url: '/api/data-factory/models/',
      params
    })
    return response
  }

  /**
   * 创建数据模型
   */
  static async createDataModel(data: Api.DataFactory.ModelCreateData): Promise<Api.DataFactory.ModelResponse> {
    const response = await request.post<Api.DataFactory.ModelResponse>({
      url: '/api/data-factory/models/',
      params: data
    })
    return response
  }

  /**
   * 获取数据模型详情
   */
  static async getDataModel(id: number): Promise<Api.DataFactory.ModelResponse> {
    const response = await request.get<Api.DataFactory.ModelResponse>({
      url: `/api/data-factory/models/${id}`
    })
    return response
  }

  /**
   * 更新数据模型
   */
  static async updateDataModel(id: number, data: Api.DataFactory.ModelUpdateData): Promise<Api.DataFactory.ModelResponse> {
    const response = await request.put<Api.DataFactory.ModelResponse>({
      url: `/api/data-factory/models/${id}`,
      params: data
    })
    return response
  }

  /**
   * 删除数据模型
   */
  static async deleteDataModel(id: number): Promise<void> {
    await request.del<void>({
      url: `/api/data-factory/models/${id}`
    })
  }

  /**
   * 预览数据模型
   */
  static async previewDataModel(id: number, count: number = 5): Promise<{
    model_id: number
    model_name: string
    preview_data: Record<string, any>[]
    field_count: number
    record_count: number
  }> {
    const response = await request.post<Api.DataFactory.ModelPreviewResponse>({
      url: `/api/data-factory/models/${id}/preview`,
      params: { count }
    })
    return response
  }

  /**
   * 复制数据模型
   */
  static async duplicateDataModel(id: number, newName: string): Promise<Api.DataFactory.ModelResponse> {
    const response = await request.post<Api.DataFactory.ModelResponse>({
      url: `/api/data-factory/models/${id}/duplicate`,
      params: { new_name: newName }
    })
    return response
  }

  /**
   * 获取模型分类列表
   */
  static async getModelCategories(): Promise<Api.DataFactory.CategoryListResponse> {
    const response = await request.get<Api.DataFactory.CategoryListResponse>({
      url: '/api/data-factory/models/categories/list'
    })
    return response
  }

  /**
   * 获取模型统计信息
   */
  static async getModelStatistics(): Promise<Api.DataFactory.ModelStatisticsResponse> {
    const response = await request.get<Api.DataFactory.ModelStatisticsResponse>({
      url: '/api/data-factory/models/statistics/overview'
    })
    return response
  }

  /**
   * 解析样本数据
   */
  static async parseSampleData(jsonData: string): Promise<any> {
    const response = await request.post<any>({
      url: '/api/data-factory/models/parse-sample',
      data: {
        json_data: jsonData
      }
    })
    return response
  }

  /**
   * 测试自定义生成器
   */
  static async testCustomGenerator(code: string, samples: any[], testCount: number = 5): Promise<any> {
    const response = await request.post<any>({
      url: '/api/data-factory/models/test-custom-generator',
      data: {
        code,
        samples,
        test_count: testCount
      }
    })
    return response
  }

  // ==================== 数据生成任务管理 ====================

  /**
   * 获取生成任务列表
   */
  static async getGenerationTasks(params: Api.DataFactory.TaskListParams): Promise<Api.DataFactory.TaskListResponse> {
    const response = await request.get<Api.DataFactory.TaskListResponse>({
      url: '/api/data-factory/tasks/',
      params
    })
    return response
  }

  /**
   * 创建生成任务
   */
  static async createGenerationTask(data: Api.DataFactory.TaskCreateData): Promise<Api.DataFactory.TaskResponse> {
    const response = await request.post<Api.DataFactory.TaskResponse>({
      url: '/api/data-factory/tasks/',
      params: data
    })
    return response
  }

  /**
   * 获取生成任务详情
   */
  static async getGenerationTask(id: number): Promise<Api.DataFactory.TaskResponse> {
    const response = await request.get<Api.DataFactory.TaskResponse>({
      url: `/api/data-factory/tasks/${id}`
    })
    return response
  }

  /**
   * 更新生成任务
   */
  static async updateGenerationTask(id: number, data: Api.DataFactory.TaskUpdateData): Promise<Api.DataFactory.TaskResponse> {
    const response = await request.put<Api.DataFactory.TaskResponse>({
      url: `/api/data-factory/tasks/${id}`,
      params: data
    })
    return response
  }

  /**
   * 删除生成任务
   */
  static async deleteGenerationTask(id: number): Promise<void> {
    await request.del<void>({
      url: `/api/data-factory/tasks/${id}`
    })
  }

  /**
   * 取消生成任务
   */
  static async cancelGenerationTask(id: number): Promise<Api.DataFactory.TaskResponse> {
    const response = await request.post<Api.DataFactory.TaskResponse>({
      url: `/api/data-factory/tasks/${id}/cancel`
    })
    return response
  }

  /**
   * 重试生成任务
   */
  static async retryGenerationTask(id: number): Promise<Api.DataFactory.TaskResponse> {
    const response = await request.post<Api.DataFactory.TaskResponse>({
      url: `/api/data-factory/tasks/${id}/retry`
    })
    return response
  }

  /**
   * 下载任务结果
   */
  static async downloadTaskResult(id: number): Promise<Blob> {
    const response = await request.get<Blob>({
      url: `/api/data-factory/tasks/${id}/download`,
      responseType: 'blob'
    })
    return response
  }

  /**
   * 获取任务统计信息
   */
  static async getTaskStatistics(): Promise<Api.DataFactory.TaskStatisticsResponse> {
    const response = await request.get<Api.DataFactory.TaskStatisticsResponse>({
      url: '/api/data-factory/tasks/statistics/overview'
    })
    return response
  }

  /**
   * 获取正在运行的任务
   */
  static async getRunningTasks(): Promise<Api.DataFactory.RunningTasksResponse> {
    const response = await request.get<Api.DataFactory.RunningTasksResponse>({
      url: '/api/data-factory/tasks/running/list'
    })
    return response
  }

  /**
   * 获取支持的导出格式
   */
  static async getExportFormats(): Promise<Api.DataFactory.ExportFormatsResponse> {
    const response = await request.get<Api.DataFactory.ExportFormatsResponse>({
      url: '/api/data-factory/tasks/export/formats'
    })
    return response
  }
}
