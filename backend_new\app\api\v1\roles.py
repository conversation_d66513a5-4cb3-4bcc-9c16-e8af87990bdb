"""
角色管理API路由
"""
from typing import List
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_current_active_user
from app.schemas.user import RoleResponse
from app.services.role import RoleService

router = APIRouter()


@router.get("/list", response_model=List[RoleResponse], summary="获取角色列表")
async def get_role_list(
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """
    获取角色列表
    
    Args:
        db: 数据库会话
        _current_user: 当前用户
        
    Returns:
        角色列表数据
    """
    role_service = RoleService(db)
    result = await role_service.get_role_list()
    return result
