"""
混沌测试批次任务服务
"""
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload

from app.models.chaos_batch_task import ChaosBatchTask, ChaosBatchTaskItem
from app.schemas.chaos.batch_task_schemas import (
    ChaosBatchTaskCreate, ChaosBatchTaskUpdate, ChaosBatchTaskResponse,
    ChaosBatchTaskQuery, ChaosBatchTaskPageResponse,
    ChaosBatchTaskItemCreate, ChaosBatchTaskItemResponse
)
from app.services.base import BaseService
from app.core.exceptions import raise_not_found_error, raise_conflict_error
from app.tasks.chaos.schedule_tasks import schedule_batch_chaos_task

logger = logging.getLogger(__name__)


class ChaosBatchTaskService(BaseService):
    """混沌测试批次任务服务"""

    def __init__(self, db: AsyncSession, redis_client=None):
        super().__init__(db, redis_client)

    async def create_batch_task(self, batch_task_data: ChaosBatchTaskCreate) -> ChaosBatchTaskResponse:
        """创建批次任务"""
        # 检查任务名称是否重复
        existing_task = await self._get_batch_task_by_name(batch_task_data.name)
        if existing_task:
            raise_conflict_error(f"批次任务名称 '{batch_task_data.name}' 已存在")

        # 创建批次任务
        batch_task_dict = batch_task_data.model_dump(exclude={'batch_items'})
        batch_task = ChaosBatchTask(**batch_task_dict)
        
        self.db.add(batch_task)
        await self.db.flush()  # 获取ID但不提交

        # 创建子任务
        if batch_task_data.batch_items:
            for order, item_data in enumerate(batch_task_data.batch_items):
                item_dict = item_data.model_dump()
                item_dict['batch_task_id'] = batch_task.id
                item_dict['execution_order'] = order
                
                batch_item = ChaosBatchTaskItem(**item_dict)
                self.db.add(batch_item)

        await self.db.commit()
        await self.db.refresh(batch_task)

        response = await self._convert_to_response(batch_task)
        await self._resolve_single_user_names(response)
        return response

    async def get_batch_task_by_id(self, batch_task_id: int) -> Optional[ChaosBatchTaskResponse]:
        """根据ID获取批次任务"""
        batch_task = await self._get_batch_task_by_id(batch_task_id)
        if not batch_task:
            return None

        response = await self._convert_to_response(batch_task)
        await self._resolve_single_user_names(response)
        return response

    async def update_batch_task(self, batch_task_id: int, batch_task_data: ChaosBatchTaskUpdate) -> ChaosBatchTaskResponse:
        """更新批次任务"""
        batch_task = await self._get_batch_task_by_id(batch_task_id)
        if not batch_task:
            raise_not_found_error("批次任务不存在")

        # 检查名称冲突
        if batch_task_data.name and batch_task_data.name != batch_task.name:
            existing_task = await self._get_batch_task_by_name(batch_task_data.name)
            if existing_task:
                raise_conflict_error(f"批次任务名称 '{batch_task_data.name}' 已存在")

        # 更新字段
        update_dict = batch_task_data.model_dump(exclude_unset=True, exclude={'batch_items'})
        for field, value in update_dict.items():
            if hasattr(batch_task, field):
                setattr(batch_task, field, value)

        await self.db.commit()
        await self.db.refresh(batch_task)

        response = await self._convert_to_response(batch_task)
        await self._resolve_single_user_names(response)
        return response

    async def delete_batch_task(self, batch_task_id: int) -> bool:
        """删除批次任务"""
        batch_task = await self._get_batch_task_by_id(batch_task_id)
        if not batch_task:
            raise_not_found_error("批次任务不存在")

        # 检查是否有运行中的执行记录
        from app.models.chaos_execution import ChaosExecution
        running_executions_result = await self.db.execute(
            select(func.count(ChaosExecution.id)).where(
                and_(
                    ChaosExecution.batch_task_id == batch_task_id,
                    ChaosExecution.status.in_(["pending", "running"])
                )
            )
        )
        running_count = running_executions_result.scalar()
        
        if running_count > 0:
            raise_conflict_error("批次任务有运行中的执行记录，无法删除")

        await self.db.delete(batch_task)
        await self.db.commit()
        return True

    async def list_batch_tasks(self, query: ChaosBatchTaskQuery) -> ChaosBatchTaskPageResponse:
        """查询批次任务列表"""
        # 构建查询
        stmt = select(ChaosBatchTask).options(selectinload(ChaosBatchTask.batch_items))

        # 添加筛选条件
        if query.keyword:
            stmt = stmt.where(
                or_(
                    ChaosBatchTask.name.ilike(f"%{query.keyword}%"),
                    ChaosBatchTask.description.ilike(f"%{query.keyword}%")
                )
            )

        if query.execution_type:
            stmt = stmt.where(ChaosBatchTask.execution_type == query.execution_type)

        if query.batch_execution_mode:
            stmt = stmt.where(ChaosBatchTask.batch_execution_mode == query.batch_execution_mode)

        if query.task_status:
            stmt = stmt.where(ChaosBatchTask.task_status == query.task_status)

        # 计算总数
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()

        # 分页查询
        stmt = stmt.offset(query.offset).limit(query.size).order_by(ChaosBatchTask.created_at.desc())
        result = await self.db.execute(stmt)
        batch_tasks = result.scalars().all()

        # 转换响应
        batch_task_responses = []
        for batch_task in batch_tasks:
            response = await self._convert_to_response(batch_task)
            batch_task_responses.append(response)
        
        await self._resolve_user_names(batch_task_responses)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return ChaosBatchTaskPageResponse(
            items=batch_task_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def execute_batch_task(self, batch_task_id: int, execution_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行批次任务"""
        batch_task = await self._get_batch_task_by_id(batch_task_id)
        if not batch_task:
            raise_not_found_error("批次任务不存在")

        if not batch_task.can_execute():
            raise_conflict_error("批次任务当前无法执行")

        # 检查是否有子任务
        if not batch_task.batch_items:
            raise_conflict_error("批次任务没有子任务")

        # 构建执行配置
        config = execution_config or {}
        config.update({
            "batch_task_id": batch_task_id,
            "env_ids": batch_task.env_ids,
            "execution_time": datetime.now().isoformat()
        })

        # 提交到Celery队列
        celery_result = schedule_batch_chaos_task.delay(batch_task_id, config)

        # 更新任务执行统计
        batch_task.last_execution_time = datetime.now()
        batch_task.execution_count = (batch_task.execution_count or 0) + 1
        await self.db.commit()

        return {
            "batch_task_id": batch_task_id,
            "celery_task_id": celery_result.id,
            "status": "submitted",
            "message": "批次任务已提交到执行队列"
        }

    async def get_batch_task_items(self, batch_task_id: int) -> List[ChaosBatchTaskItem]:
        """获取批次任务的子任务列表"""
        stmt = select(ChaosBatchTaskItem).where(
            ChaosBatchTaskItem.batch_task_id == batch_task_id
        ).order_by(ChaosBatchTaskItem.execution_order)
        
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_scheduled_batch_tasks(self, current_time: datetime) -> List[ChaosBatchTask]:
        """获取到期的调度批次任务"""
        stmt = select(ChaosBatchTask).where(
            and_(
                ChaosBatchTask.task_status == "enabled",
                ChaosBatchTask.execution_type == "scheduled",
                ChaosBatchTask.scheduled_time <= current_time
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    # 私有方法
    async def _get_batch_task_by_id(self, batch_task_id: int) -> Optional[ChaosBatchTask]:
        """根据ID获取批次任务"""
        stmt = select(ChaosBatchTask).options(
            selectinload(ChaosBatchTask.batch_items)
        ).where(ChaosBatchTask.id == batch_task_id)
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_batch_task_by_name(self, name: str) -> Optional[ChaosBatchTask]:
        """根据名称获取批次任务"""
        result = await self.db.execute(select(ChaosBatchTask).where(ChaosBatchTask.name == name))
        return result.scalar_one_or_none()

    async def _convert_to_response(self, batch_task: ChaosBatchTask) -> ChaosBatchTaskResponse:
        """转换为响应对象"""
        # 转换子任务
        batch_items = []
        if batch_task.batch_items:
            for item in batch_task.batch_items:
                item_response = ChaosBatchTaskItemResponse(
                    id=item.id,
                    name=item.name,
                    description=item.description,
                    execution_order=item.execution_order,
                    fault_type=item.fault_type,
                    fault_params=item.fault_params,
                    auto_destroy=item.auto_destroy,
                    max_duration=item.max_duration,
                    is_enabled=item.is_enabled,
                    created_at=item.created_at,
                    updated_at=item.updated_at,
                    created_by=item.created_by,
                    updated_by=item.updated_by,
                    created_by_name=None,
                    updated_by_name=None
                )
                batch_items.append(item_response)

        return ChaosBatchTaskResponse(
            id=batch_task.id,
            name=batch_task.name,
            description=batch_task.description,
            env_ids=batch_task.env_ids,
            execution_type=batch_task.execution_type,
            scheduled_time=batch_task.scheduled_time,
            batch_execution_mode=batch_task.batch_execution_mode,
            batch_interval=batch_task.batch_interval,
            task_status=batch_task.task_status,
            last_execution_time=batch_task.last_execution_time,
            execution_count=batch_task.execution_count,
            monitor_config=batch_task.monitor_config,
            batch_items=batch_items,
            created_at=batch_task.created_at,
            updated_at=batch_task.updated_at,
            created_by=batch_task.created_by,
            updated_by=batch_task.updated_by,
            created_by_name=None,
            updated_by_name=None
        )
