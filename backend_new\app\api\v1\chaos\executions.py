"""
混沌测试执行记录API
"""
import logging
from typing import List
from fastapi import APIRouter, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_redis_client, get_current_active_user
from app.db.redis import RedisClient
from app.models.user import User
from app.services.chaos.execution_service import ChaosExecutionService
from app.schemas.chaos.execution_schemas import (
    ChaosExecutionResponse, ChaosExecutionQuery, ChaosExecutionPageResponse,
    ChaosExecutionStatsResponse, ChaosExecutionDestroyRequest, ChaosExecutionDestroyResponse
)
from app.schemas.base import response_builder
from app.tasks.chaos.execution_tasks import destroy_single_chaos_fault

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("", response_model=ChaosExecutionPageResponse, summary="查询混沌测试执行记录列表")
async def list_chaos_executions(
    query: ChaosExecutionQuery = Depends(),
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """查询混沌测试执行记录列表，支持多维度筛选和分页"""
    execution_service = ChaosExecutionService(db, redis_client)
    result = await execution_service.list_executions(query)
    return response_builder.success(result)


@router.get("/{execution_id}", response_model=ChaosExecutionResponse, summary="获取混沌测试执行记录详情")
async def get_chaos_execution(
    execution_id: int,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """获取指定ID的混沌测试执行记录详情"""
    execution_service = ChaosExecutionService(db, redis_client)
    result = await execution_service.get_execution_by_id(execution_id)
    return response_builder.success(result)


@router.delete("/{execution_id}", status_code=status.HTTP_204_NO_CONTENT, summary="删除混沌测试执行记录")
async def delete_chaos_execution(
    execution_id: int,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """删除混沌测试执行记录"""
    execution_service = ChaosExecutionService(db, redis_client)
    await execution_service.delete_execution(execution_id)
    return None


@router.post("/destroy", response_model=ChaosExecutionDestroyResponse, summary="批量销毁混沌故障")
async def destroy_chaos_executions(
    destroy_request: ChaosExecutionDestroyRequest,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    current_user: User = Depends(get_current_active_user)
):
    """批量销毁混沌故障"""
    execution_service = ChaosExecutionService(db, redis_client)
    
    results = []
    success_count = 0
    failed_count = 0
    
    for execution_id in destroy_request.execution_ids:
        try:
            # 获取执行记录
            execution = await execution_service.get_execution_by_id(execution_id)
            if not execution:
                results.append({
                    "execution_id": execution_id,
                    "success": False,
                    "message": "执行记录不存在"
                })
                failed_count += 1
                continue
            
            # 检查是否可以销毁
            if not execution.chaos_uid:
                results.append({
                    "execution_id": execution_id,
                    "success": False,
                    "message": "没有有效的故障UID"
                })
                failed_count += 1
                continue
            
            if execution.status not in ["running", "success"] and not destroy_request.force_destroy:
                results.append({
                    "execution_id": execution_id,
                    "success": False,
                    "message": f"当前状态({execution.status})不允许销毁"
                })
                failed_count += 1
                continue
            
            # 提交销毁任务
            celery_result = destroy_single_chaos_fault.delay(execution.chaos_uid, execution_id)
            
            results.append({
                "execution_id": execution_id,
                "success": True,
                "message": "销毁任务已提交",
                "celery_task_id": celery_result.id
            })
            success_count += 1
            
        except Exception as e:
            logger.error(f"销毁执行记录异常: {execution_id}, {str(e)}")
            results.append({
                "execution_id": execution_id,
                "success": False,
                "message": f"销毁异常: {str(e)}"
            })
            failed_count += 1
    
    response = ChaosExecutionDestroyResponse(
        total_count=len(destroy_request.execution_ids),
        success_count=success_count,
        failed_count=failed_count,
        results=results,
        message=f"批量销毁完成: 成功 {success_count}, 失败 {failed_count}"
    )
    
    return response_builder.success(response)


@router.get("/stats/overview", response_model=ChaosExecutionStatsResponse, summary="获取混沌测试执行统计")
async def get_chaos_execution_stats(
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """获取混沌测试执行统计信息"""
    from datetime import datetime, timedelta
    from sqlalchemy import select, func, case
    from app.models.chaos_execution import ChaosExecution
    
    # 基础统计
    total_executions_result = await db.execute(select(func.count(ChaosExecution.id)))
    total_executions = total_executions_result.scalar()

    running_executions_result = await db.execute(
        select(func.count(ChaosExecution.id)).where(
            ChaosExecution.status.in_(["pending", "running"])
        )
    )
    running_executions = running_executions_result.scalar()

    success_executions_result = await db.execute(
        select(func.count(ChaosExecution.id)).where(ChaosExecution.status == "success")
    )
    success_executions = success_executions_result.scalar()

    failed_executions_result = await db.execute(
        select(func.count(ChaosExecution.id)).where(
            ChaosExecution.status.in_(["failed", "timeout"])
        )
    )
    failed_executions = failed_executions_result.scalar()

    # 今日执行统计
    today = datetime.now().date()
    today_executions_result = await db.execute(
        select(func.count(ChaosExecution.id)).where(
            func.date(ChaosExecution.start_time) == today
        )
    )
    today_executions = today_executions_result.scalar()

    # 平均执行时长
    avg_duration_result = await db.execute(
        select(func.avg(
            func.timestampdiff('SECOND', ChaosExecution.start_time, ChaosExecution.end_time)
        )).where(
            ChaosExecution.start_time.isnot(None),
            ChaosExecution.end_time.isnot(None)
        )
    )
    avg_duration = avg_duration_result.scalar()

    # 成功率
    success_rate = (success_executions / total_executions * 100) if total_executions > 0 else 0

    # 状态统计
    status_stats_result = await db.execute(
        select(ChaosExecution.status, func.count(ChaosExecution.id))
        .group_by(ChaosExecution.status)
    )
    status_stats = dict(status_stats_result.all())

    # 故障类型统计
    fault_type_stats_result = await db.execute(
        select(
            func.json_unquote(func.json_extract(ChaosExecution.fault_config, '$.fault_type')),
            func.count(ChaosExecution.id)
        ).group_by(
            func.json_unquote(func.json_extract(ChaosExecution.fault_config, '$.fault_type'))
        )
    )
    fault_type_stats = dict(fault_type_stats_result.all())

    response = ChaosExecutionStatsResponse(
        total_executions=total_executions,
        running_executions=running_executions,
        success_executions=success_executions,
        failed_executions=failed_executions,
        today_executions=today_executions,
        avg_duration=float(avg_duration) if avg_duration else None,
        success_rate=round(success_rate, 2),
        status_stats=status_stats,
        fault_type_stats=fault_type_stats
    )

    return response_builder.success(response)


@router.get("/running/list", response_model=List[ChaosExecutionResponse], summary="获取运行中的执行记录")
async def get_running_executions(
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """获取所有运行中的混沌测试执行记录"""
    execution_service = ChaosExecutionService(db, redis_client)
    running_executions = await execution_service.get_running_executions()
    
    responses = [execution_service._convert_to_response(execution) for execution in running_executions]
    await execution_service._resolve_user_names(responses)
    
    return response_builder.success(responses)
