"""
文件上传API路由
"""
import os
import uuid
from typing import List
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException
from fastapi.responses import J<PERSON>NResponse

from app.api.deps import get_current_user
from app.core.config import settings
from app.core.exceptions import raise_validation_error

router = APIRouter()


@router.post("/avatar", summary="上传用户头像")
async def upload_avatar(
    file: UploadFile = File(...),
    current_user = Depends(get_current_user)
):
    """
    上传用户头像
    
    Args:
        file: 上传的文件
        current_user: 当前用户
        
    Returns:
        上传结果和文件URL
    """
    # 检查文件类型
    if not file.content_type or not file.content_type.startswith('image/'):
        raise_validation_error("只能上传图片文件")
    
    # 检查文件大小
    file_content = await file.read()
    if len(file_content) > settings.MAX_FILE_SIZE:
        raise_validation_error(f"文件大小不能超过 {settings.MAX_FILE_SIZE // (1024*1024)}MB")
    
    # 检查文件扩展名
    file_extension = os.path.splitext(file.filename)[1].lower()
    allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif']
    if file_extension not in allowed_extensions:
        raise_validation_error(f"不支持的文件类型，只支持: {', '.join(allowed_extensions)}")
    
    # 生成唯一文件名
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    
    # 确保上传目录存在
    avatar_dir = os.path.join(settings.UPLOAD_DIR, "avatars")
    os.makedirs(avatar_dir, exist_ok=True)
    
    # 保存文件
    file_path = os.path.join(avatar_dir, unique_filename)
    with open(file_path, "wb") as buffer:
        buffer.write(file_content)
    
    # 构建文件URL
    file_url = f"/uploads/avatars/{unique_filename}"
    
    return {
        "message": "头像上传成功",
        "url": file_url,
        "filename": unique_filename
    }


@router.post("/file", summary="上传通用文件")
async def upload_file(
    file: UploadFile = File(...),
    current_user = Depends(get_current_user)
):
    """
    上传通用文件
    
    Args:
        file: 上传的文件
        current_user: 当前用户
        
    Returns:
        上传结果和文件URL
    """
    # 检查文件大小
    file_content = await file.read()
    if len(file_content) > settings.MAX_FILE_SIZE:
        raise_validation_error(f"文件大小不能超过 {settings.MAX_FILE_SIZE // (1024*1024)}MB")
    
    # 检查文件扩展名
    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension not in settings.ALLOWED_FILE_TYPES:
        raise_validation_error(f"不支持的文件类型，只支持: {', '.join(settings.ALLOWED_FILE_TYPES)}")
    
    # 生成唯一文件名
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    
    # 确保上传目录存在
    files_dir = os.path.join(settings.UPLOAD_DIR, "files")
    os.makedirs(files_dir, exist_ok=True)
    
    # 保存文件
    file_path = os.path.join(files_dir, unique_filename)
    with open(file_path, "wb") as buffer:
        buffer.write(file_content)
    
    # 构建文件URL
    file_url = f"/uploads/files/{unique_filename}"
    
    return {
        "message": "文件上传成功",
        "url": file_url,
        "filename": unique_filename,
        "original_name": file.filename,
        "size": len(file_content)
    }


@router.delete("/file/{filename}", summary="删除文件")
async def delete_file(
    filename: str,
    current_user = Depends(get_current_user)
):
    """
    删除上传的文件
    
    Args:
        filename: 文件名
        current_user: 当前用户
        
    Returns:
        删除结果
    """
    # 检查文件是否存在
    file_path = None
    for subdir in ["avatars", "files"]:
        potential_path = os.path.join(settings.UPLOAD_DIR, subdir, filename)
        if os.path.exists(potential_path):
            file_path = potential_path
            break
    
    if not file_path:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    try:
        os.remove(file_path)
        return {"message": "文件删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")
