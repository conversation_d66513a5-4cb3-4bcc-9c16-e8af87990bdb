"""
混沌测试任务API
"""
import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_redis_client, get_current_active_user
from app.db.redis import RedisClient
from app.models.user import User
from app.services.chaos.task_service import ChaosTaskService
from app.schemas.chaos.task_schemas import (
    ChaosTaskCreate, ChaosTaskUpdate, ChaosTaskResponse,
    ChaosTaskQuery, ChaosTaskPageResponse, ChaosTaskExecuteRequest,
    ChaosTaskExecuteResponse, ChaosTaskStatsResponse
)
from app.schemas.base import response_builder

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("", response_model=ChaosTaskResponse, summary="创建混沌测试任务")
async def create_chaos_task(
    task_data: ChaosTaskCreate,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """创建新的混沌测试任务"""
    task_service = ChaosTaskService(db, redis_client)
    result = await task_service.create_task(task_data)
    return response_builder.success(result)


@router.get("", response_model=ChaosTaskPageResponse, summary="查询混沌测试任务列表")
async def list_chaos_tasks(
    query: ChaosTaskQuery = Depends(),
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """查询混沌测试任务列表，支持关键词搜索、类型筛选和分页"""
    task_service = ChaosTaskService(db, redis_client)
    result = await task_service.list_tasks(query)
    return response_builder.success(result)


@router.get("/{task_id}", response_model=ChaosTaskResponse, summary="获取混沌测试任务详情")
async def get_chaos_task(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """获取指定ID的混沌测试任务详情"""
    task_service = ChaosTaskService(db, redis_client)
    result = await task_service.get_task_by_id(task_id)
    return response_builder.success(result)


@router.put("/{task_id}", response_model=ChaosTaskResponse, summary="更新混沌测试任务")
async def update_chaos_task(
    task_id: int,
    task_data: ChaosTaskUpdate,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """更新混沌测试任务信息"""
    task_service = ChaosTaskService(db, redis_client)
    result = await task_service.update_task(task_id, task_data)
    return response_builder.success(result)


@router.delete("/{task_id}", status_code=status.HTTP_204_NO_CONTENT, summary="删除混沌测试任务")
async def delete_chaos_task(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """删除混沌测试任务"""
    task_service = ChaosTaskService(db, redis_client)
    await task_service.delete_task(task_id)
    return None


@router.post("/{task_id}/execute", response_model=ChaosTaskExecuteResponse, summary="执行混沌测试任务")
async def execute_chaos_task(
    task_id: int,
    execute_request: ChaosTaskExecuteRequest = ChaosTaskExecuteRequest(),
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    current_user: User = Depends(get_current_active_user)
):
    """执行混沌测试任务"""
    task_service = ChaosTaskService(db, redis_client)
    
    # 构建执行配置
    execution_config = {
        "user_id": current_user.id,
        "user_name": current_user.username,
        "force_execute": execute_request.force_execute
    }
    
    # 如果指定了环境ID，覆盖任务配置
    if execute_request.env_ids:
        execution_config["env_ids"] = execute_request.env_ids
    
    # 合并执行配置覆盖
    if execute_request.execution_config:
        execution_config.update(execute_request.execution_config)
    
    result = await task_service.execute_task(task_id, execution_config)
    
    response = ChaosTaskExecuteResponse(
        task_id=task_id,
        celery_task_id=result["celery_task_id"],
        execution_type="manual",
        status=result["status"],
        message=result["message"]
    )
    
    return response_builder.success(response)


@router.post("/{task_id}/enable", response_model=ChaosTaskResponse, summary="启用混沌测试任务")
async def enable_chaos_task(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """启用混沌测试任务"""
    task_service = ChaosTaskService(db, redis_client)
    task_update = ChaosTaskUpdate(task_status="enabled")
    result = await task_service.update_task(task_id, task_update)
    return response_builder.success(result)


@router.post("/{task_id}/disable", response_model=ChaosTaskResponse, summary="禁用混沌测试任务")
async def disable_chaos_task(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """禁用混沌测试任务"""
    task_service = ChaosTaskService(db, redis_client)
    task_update = ChaosTaskUpdate(task_status="disabled")
    result = await task_service.update_task(task_id, task_update)
    return response_builder.success(result)


@router.get("/stats/overview", response_model=ChaosTaskStatsResponse, summary="获取混沌测试任务统计")
async def get_chaos_task_stats(
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user: User = Depends(get_current_active_user)
):
    """获取混沌测试任务统计信息"""
    task_service = ChaosTaskService(db, redis_client)
    result = await task_service.get_task_stats()
    return response_builder.success(result)


@router.get("/fault-types/supported", summary="获取支持的故障类型")
async def get_supported_fault_types(
    _current_user: User = Depends(get_current_active_user)
):
    """获取系统支持的故障类型列表"""
    from app.core.chaos.fault_types import get_supported_fault_types, get_fault_handler
    
    fault_types = get_supported_fault_types()
    
    # 获取每种故障类型的参数结构
    fault_type_info = []
    for fault_type in fault_types:
        handler = get_fault_handler(fault_type)
        if handler:
            fault_type_info.append({
                "type": fault_type,
                "name": fault_type.upper(),
                "description": f"{fault_type.upper()}故障注入",
                "param_schema": handler.get_param_schema()
            })
    
    return response_builder.success({
        "fault_types": fault_type_info,
        "total_count": len(fault_type_info)
    })


@router.get("/execution-types/supported", summary="获取支持的执行类型")
async def get_supported_execution_types(
    _current_user: User = Depends(get_current_active_user)
):
    """获取系统支持的执行类型列表"""
    execution_types = [
        {
            "type": "immediate",
            "name": "立即执行",
            "description": "创建后立即执行任务"
        },
        {
            "type": "scheduled",
            "name": "定时执行",
            "description": "在指定时间执行任务"
        },

        {
            "type": "cron",
            "name": "Cron执行",
            "description": "使用Cron表达式定义执行时间"
        }
    ]
    
    return response_builder.success({
        "execution_types": execution_types,
        "total_count": len(execution_types)
    })
