#!/bin/bash
# Celery服务启动脚本 (Linux/macOS)

echo "启动Celery服务..."

# 创建日志目录
mkdir -p logs

# 启动Celery Worker (后台运行)
echo "启动Celery Worker..."
nohup python celery_worker.py > logs/celery_worker_startup.log 2>&1 &
WORKER_PID=$!
echo "Celery Worker PID: $WORKER_PID"

# 等待2秒
sleep 2

# 启动Celery Beat (后台运行)
echo "启动Celery Beat..."
nohup python celery_beat.py > logs/celery_beat_startup.log 2>&1 &
BEAT_PID=$!
echo "Celery Beat PID: $BEAT_PID"

# 等待2秒
sleep 2

# 启动Celery Flower监控 (可选)
echo "启动Celery Flower监控..."
nohup python celery_monitor.py > logs/celery_flower_startup.log 2>&1 &
FLOWER_PID=$!
echo "Celery Flower PID: $FLOWER_PID"

echo ""
echo "Celery服务启动完成！"
echo ""
echo "服务状态:"
echo "- Worker PID: $WORKER_PID (处理任务队列)"
echo "- Beat PID: $BEAT_PID (定时任务调度)"
echo "- Flower PID: $FLOWER_PID (监控界面: http://localhost:5555)"
echo ""
echo "查看日志:"
echo "- Worker: tail -f logs/celery_worker.log"
echo "- Beat: tail -f logs/celery_beat.log"
echo ""
echo "停止服务:"
echo "- kill $WORKER_PID $BEAT_PID $FLOWER_PID"
echo "- 或运行: ./stop_celery.sh"
