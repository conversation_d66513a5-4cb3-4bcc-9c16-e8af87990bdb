<template>
  <div class="data-factory-tasks art-full-height">
    <!-- 搜索栏 -->
    <TaskSearch v-model:filter="defaultFilter" @reset="resetSearch" @search="handleSearch" />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton @click="handleCreate" type="primary">创建任务</ElButton>
          <ElButton
            v-if="selectedRows.length > 0"
            @click="handleBatchCancel"
            :loading="isLoading"
          >
            批量取消
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        @selection-change="handleSelectionChange"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
      </ArtTable>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { useWindowSize } from '@vueuse/core'
import { ElMessage, ElMessageBox, ElTag, ElCard, ElButton } from 'element-plus'
import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
import ArtTable from '@/components/core/tables/art-table/index.vue'
import ArtTableHeader from '@/components/core/tables/art-table-header/index.vue'
import { useTable } from '@/composables/useTable'
import { DataFactoryService } from '@/api/dataFactoryApi'
import { formatDateTime } from '@/utils'
import TaskSearch from './components/TaskSearch.vue'

defineOptions({ name: 'DataFactoryTasks' })

const router = useRouter()
const { width } = useWindowSize()
const selectedRows = ref<Api.DataFactory.TaskInfo[]>([])

// 默认搜索条件
const defaultFilter = ref({
  status: '',
  model_id: '',
  dateRange: []
})

/**
 * 任务状态配置
 */
const TASK_STATUS_CONFIG = {
  'pending': { type: 'info' as const, text: '等待执行' },
  'running': { type: 'warning' as const, text: '正在执行' },
  'completed': { type: 'success' as const, text: '执行完成' },
  'failed': { type: 'danger' as const, text: '执行失败' },
  'cancelled': { type: 'info' as const, text: '已取消' }
} as const

/**
 * 获取任务状态配置
 */
const getTaskStatusConfig = (status: string) => {
  return (
    TASK_STATUS_CONFIG[status as keyof typeof TASK_STATUS_CONFIG] || {
      type: 'info' as const,
      text: '未知'
    }
  )
}

/**
 * API适配函数 - 将任务API适配为useTable需要的格式
 */
const getTasksForTable = async (params: any) => {
  const response = await DataFactoryService.getGenerationTasks(params)
  return {
    records: response.records,
    total: response.total,
    current: response.current,
    size: response.size
  }
}

/**
 * 使用 useTable 管理表格数据
 */
const {
  columns,
  columnChecks,
  tableData,
  isLoading,
  paginationState,
  searchData,
  resetSearch,
  onPageSizeChange,
  onCurrentPageChange,
  refreshAll
} = useTable<Api.DataFactory.TaskInfo>({
  // 核心配置
  core: {
    apiFn: getTasksForTable,
    apiParams: {
      page: 1,
      size: 20,
      ...defaultFilter.value
    },
    columnsFactory: () => [
      { type: 'selection' }, // 勾选列
      { type: 'index', width: 60, label: '序号' }, // 序号
      {
        prop: 'name',
        label: '任务名称',
        minWidth: width.value < 500 ? 180 : 150,
        formatter: (row) => {
          return h('div', { class: 'task-name' }, [
            h('div', { style: 'font-weight: 500; margin-bottom: 4px;' }, row.name),
            h('div', {
              style: 'font-size: 12px; color: #999; line-height: 1.2;'
            }, row.description || '-')
          ])
        }
      },
      {
        prop: 'model_name',
        label: '数据模型',
        width: 150,
        formatter: (row) => {
          return row.model_name || '-'
        }
      },
      {
        prop: 'record_count',
        label: '生成数量',
        width: 100,
        formatter: (row) => {
          return h(ElTag, { type: 'primary', size: 'small' }, () => row.record_count)
        }
      },
      {
        prop: 'export_format',
        label: '导出格式',
        width: 100,
        formatter: (row) => {
          return h(ElTag, { size: 'small' }, () => row.export_format?.toUpperCase() || '-')
        }
      },
      {
        prop: 'status',
        label: '状态',
        width: 100,
        formatter: (row) => {
          const statusConfig = getTaskStatusConfig(row.status)
          return h(ElTag, { type: statusConfig.type }, () => statusConfig.text)
        }
      },
      {
        prop: 'progress',
        label: '进度',
        width: 100,
        formatter: (row) => {
          return `${row.progress || 0}%`
        }
      },
      {
        prop: 'created_at',
        label: '创建时间',
        width: 160,
        formatter: (row) => {
          return formatDateTime(row.created_at)
        }
      },
      {
        prop: 'operation',
        label: '操作',
        width: 200,
        fixed: 'right',
        formatter: (row) =>
          h('div', { style: 'display: flex; gap: 8px;' }, [
            h(ArtButtonTable, {
              type: 'view',
              onClick: () => handleView(row)
            }),
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => handleEdit(row)
            }),
            h(ArtButtonTable, {
              type: 'stop',
              onClick: () => handleCancel(row)
            }),
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => handleDelete(row)
            })
          ])
      }
    ]
  }
})

// 搜索处理
const handleSearch = () => {
  searchData({ ...defaultFilter.value })
  refreshAll()
}

// 选择变化处理
const handleSelectionChange = (selection: Api.DataFactory.TaskInfo[]) => {
  selectedRows.value = selection
}

// 事件处理
const handleCreate = () => {
  router.push('/data-factory/tasks/create')
}

const handleView = (row: Api.DataFactory.TaskInfo) => {
  router.push(`/data-factory/tasks/${row.id}`)
}

const handleEdit = (row: Api.DataFactory.TaskInfo) => {
  router.push(`/data-factory/tasks/${row.id}/edit`)
}

const handleCancel = async (row: Api.DataFactory.TaskInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 "${row.name}" 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DataFactoryService.cancelGenerationTask(row.id)
    ElMessage.success('任务取消成功')
    refreshAll()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('任务取消失败')
    }
  }
}

const handleDelete = async (row: Api.DataFactory.TaskInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DataFactoryService.deleteGenerationTask(row.id)
    ElMessage.success('删除成功')
    refreshAll()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchCancel = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要取消的任务')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要取消选中的 ${selectedRows.value.length} 个任务吗？`,
      '确认批量取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const promises = selectedRows.value.map(task =>
      DataFactoryService.cancelGenerationTask(task.id)
    )
    await Promise.all(promises)

    ElMessage.success('批量取消成功')
    refreshAll()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量取消失败')
    }
  }
}
</script>

<style scoped lang="scss">
.data-factory-tasks {
  .task-name {
    .name {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }
}
</style>
