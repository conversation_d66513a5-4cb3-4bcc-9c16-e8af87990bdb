"""
磁盘故障处理器
实现磁盘相关的故障注入和销毁
"""
import logging
from typing import Dict, Any
from .base_fault import BaseFaultHandler, FaultResult

logger = logging.getLogger(__name__)


class DiskFaultHandler(BaseFaultHandler):
    """磁盘故障处理器"""
    
    async def inject(
        self, 
        ssh_client, 
        fault_params: Dict[str, Any],
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """
        注入磁盘故障
        
        支持的参数:
        - fault_action: 故障动作 (fill/burn)
        - path: 目标路径
        - size: 填充大小 (如: 1G, 500M) - 用于fill
        - read: 是否读取 - 用于burn
        - write: 是否写入 - 用于burn
        - timeout: 持续时间(秒)
        """
        # 验证参数
        is_valid, error_msg = self.validate_params(fault_params)
        if not is_valid:
            return FaultResult(
                success=False,
                fault_id="",
                message=f"参数验证失败: {error_msg}"
            )
        
        # 构建命令参数
        cmd_params = {}
        fault_action = fault_params.get("fault_action", "fill")
        
        # 目标路径
        if "path" in fault_params:
            cmd_params["path"] = fault_params["path"]
        
        # 根据故障动作添加特定参数
        if fault_action == "fill":
            if "size" in fault_params:
                cmd_params["size"] = fault_params["size"]
        elif fault_action == "burn":
            if fault_params.get("read", False):
                cmd_params["read"] = "true"
            if fault_params.get("write", False):
                cmd_params["write"] = "true"
        
        # 持续时间
        if "timeout" in fault_params:
            cmd_params["timeout"] = fault_params["timeout"]
        
        # 构建ChaosBlade命令
        command = self._build_chaosblade_command(
            action="create",
            target=f"disk {fault_action}",
            params=cmd_params,
            chaosblade_path=chaosblade_path
        )
        
        # 执行命令
        return await self._execute_chaosblade_command(ssh_client, command)
    
    async def destroy(
        self, 
        ssh_client, 
        fault_id: str,
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """销毁磁盘故障"""
        command = self._build_destroy_command(fault_id, chaosblade_path)
        return await self._execute_chaosblade_command(ssh_client, command)
    
    def validate_params(self, fault_params: Dict[str, Any]) -> tuple[bool, str]:
        """验证磁盘故障参数"""
        # 验证故障动作
        fault_action = fault_params.get("fault_action", "fill")
        valid_actions = ["fill", "burn"]
        if fault_action not in valid_actions:
            return False, f"fault_action必须是{valid_actions}中的一个"
        
        # 验证路径
        path = fault_params.get("path")
        if not path or not isinstance(path, str):
            return False, "path参数是必需的，且必须是字符串"
        
        # 验证fill动作的参数
        if fault_action == "fill":
            size = fault_params.get("size")
            if size and not self._validate_size_format(size):
                return False, "size格式错误，应为如'1G'、'500M'的格式"
        
        # 验证burn动作的参数
        elif fault_action == "burn":
            read = fault_params.get("read", False)
            write = fault_params.get("write", False)
            if not read and not write:
                return False, "burn动作必须指定read或write参数"
        
        # 验证超时时间
        timeout = fault_params.get("timeout")
        if timeout is not None:
            if not isinstance(timeout, int) or timeout <= 0:
                return False, "timeout必须是正整数"
        
        return True, ""
    
    def _validate_size_format(self, size: str) -> bool:
        """验证大小格式"""
        import re
        # 支持格式: 数字+单位(B/K/M/G/T)
        pattern = r'^\d+[BKMGT]?$'
        return bool(re.match(pattern, size.upper()))
    
    def get_param_schema(self) -> Dict[str, Any]:
        """获取磁盘故障参数结构定义"""
        return {
            "type": "object",
            "properties": {
                "fault_action": {
                    "type": "string",
                    "enum": ["fill", "burn"],
                    "description": "故障动作类型",
                    "default": "fill"
                },
                "path": {
                    "type": "string",
                    "description": "目标路径",
                    "required": True
                },
                "size": {
                    "type": "string",
                    "pattern": r"^\d+[BKMGT]?$",
                    "description": "填充大小，如1G、500M，用于fill动作",
                    "required": False
                },
                "read": {
                    "type": "boolean",
                    "description": "是否执行读取操作，用于burn动作",
                    "default": False
                },
                "write": {
                    "type": "boolean",
                    "description": "是否执行写入操作，用于burn动作",
                    "default": False
                },
                "timeout": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "持续时间(秒)，不指定则持续到手动销毁",
                    "required": False
                }
            },
            "required": ["path"],
            "additionalProperties": False
        }
