#!/usr/bin/env python3
"""
环境连接测试脚本
用于验证不同类型环境的连接配置是否正确
"""
import asyncio
import json
import sys
from typing import Dict, Any

# 添加项目路径
sys.path.append('.')

from app.services.environment import EnvironmentService
from app.schemas.environment import ConnectionTestRequest
from app.db.session import get_db


async def test_ssh_connection():
    """测试SSH连接"""
    print("🔧 测试SSH连接...")
    
    # SSH连接配置示例
    ssh_config = {
        "host": "*************",  # 替换为您的SSH服务器IP
        "port": 22,
        "username": "root",       # 替换为您的用户名
        "password": "your_password",  # 替换为您的密码
        # 或者使用私钥认证：
        # "private_key": "/path/to/your/private/key",
        # "passphrase": "key_passphrase"  # 如果私钥有密码
    }
    
    test_request = ConnectionTestRequest(
        type="ssh",
        config=ssh_config,
        timeout=10
    )
    
    async for db in get_db():
        env_service = EnvironmentService(db)
        result = await env_service.test_connection(test_request)
        
        print(f"✅ SSH测试结果: {result.success}")
        print(f"📝 消息: {result.message}")
        print(f"⏱️  耗时: {result.duration:.2f}秒")
        print(f"📊 详情: {json.dumps(result.details, indent=2, ensure_ascii=False)}")
        break


async def test_http_connection():
    """测试HTTP连接"""
    print("\n🌐 测试HTTP连接...")
    
    # HTTP连接配置示例
    http_config = {
        "host": "httpbin.org",  # 公共测试API
        "port": 80,
        "protocol": "http",
        "path": "/get"
    }
    
    test_request = ConnectionTestRequest(
        type="http",
        config=http_config,
        timeout=10
    )
    
    async for db in get_db():
        env_service = EnvironmentService(db)
        result = await env_service.test_connection(test_request)
        
        print(f"✅ HTTP测试结果: {result.success}")
        print(f"📝 消息: {result.message}")
        print(f"⏱️  耗时: {result.duration:.2f}秒")
        print(f"📊 详情: {json.dumps(result.details, indent=2, ensure_ascii=False)}")
        break


async def test_database_connection():
    """测试数据库连接"""
    print("\n🗄️  测试数据库连接...")
    
    # 数据库连接配置示例
    db_config = {
        "host": "localhost",
        "port": 3306,
        "username": "root",
        "password": "password",
        "database": "test"
    }
    
    test_request = ConnectionTestRequest(
        type="mysql",
        config=db_config,
        timeout=10
    )
    
    async for db in get_db():
        env_service = EnvironmentService(db)
        result = await env_service.test_connection(test_request)
        
        print(f"✅ 数据库测试结果: {result.success}")
        print(f"📝 消息: {result.message}")
        print(f"⏱️  耗时: {result.duration:.2f}秒")
        print(f"📊 详情: {json.dumps(result.details, indent=2, ensure_ascii=False)}")
        break


async def test_redis_connection():
    """测试Redis连接"""
    print("\n🔴 测试Redis连接...")
    
    # Redis连接配置示例
    redis_config = {
        "host": "localhost",
        "port": 6379,
        "password": "",  # 如果有密码
        "db": 0
    }
    
    test_request = ConnectionTestRequest(
        type="redis",
        config=redis_config,
        timeout=10
    )
    
    async for db in get_db():
        env_service = EnvironmentService(db)
        result = await env_service.test_connection(test_request)
        
        print(f"✅ Redis测试结果: {result.success}")
        print(f"📝 消息: {result.message}")
        print(f"⏱️  耗时: {result.duration:.2f}秒")
        print(f"📊 详情: {json.dumps(result.details, indent=2, ensure_ascii=False)}")
        break


async def test_custom_tcp_connection():
    """测试自定义TCP连接"""
    print("\n🔌 测试自定义TCP连接...")
    
    # 自定义TCP连接配置示例
    tcp_config = {
        "host": "www.baidu.com",
        "port": 80
    }
    
    test_request = ConnectionTestRequest(
        type="custom",
        config=tcp_config,
        timeout=10
    )
    
    async for db in get_db():
        env_service = EnvironmentService(db)
        result = await env_service.test_connection(test_request)
        
        print(f"✅ TCP测试结果: {result.success}")
        print(f"📝 消息: {result.message}")
        print(f"⏱️  耗时: {result.duration:.2f}秒")
        print(f"📊 详情: {json.dumps(result.details, indent=2, ensure_ascii=False)}")
        break


async def main():
    """主函数"""
    print("🚀 开始环境连接测试...")
    print("=" * 60)
    
    try:
        # 测试HTTP连接（最容易成功的测试）
        await test_http_connection()
        
        # 测试自定义TCP连接
        await test_custom_tcp_connection()
        
        # 测试SSH连接（需要配置实际的SSH服务器）
        # await test_ssh_connection()
        
        # 测试数据库连接（需要配置实际的数据库）
        # await test_database_connection()
        
        # 测试Redis连接（需要配置实际的Redis服务器）
        # await test_redis_connection()
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成！")


if __name__ == "__main__":
    print("📋 环境连接测试工具")
    print("💡 提示：请根据您的实际环境修改配置信息")
    print()
    
    asyncio.run(main())
