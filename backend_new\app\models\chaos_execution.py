"""
混沌测试执行记录数据模型
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, Integer, Text, JSON, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class ChaosExecution(BaseModel):
    """
    混沌测试执行记录模型
    记录每次故障注入的详细执行信息
    """
    __tablename__ = "chaos_executions"

    # 关联信息
    task_id = Column(Integer, ForeignKey("chaos_tasks.id"), nullable=True, comment="单次任务ID")
    batch_task_id = Column(Integer, ForeignKey("chaos_batch_tasks.id"), nullable=True, comment="批次任务ID")
    batch_task_item_id = Column(Integer, ForeignKey("chaos_batch_task_items.id"), nullable=True, comment="批次任务子项ID")
    
    # 执行环境
    host_id = Column(Integer, nullable=True, comment="目标主机ID")
    host_info = Column(JSON, nullable=False, comment="主机连接信息")
    
    # 故障配置
    fault_config = Column(JSON, nullable=False, comment="故障配置信息")
    
    # 执行状态
    status = Column(String(20), default="pending", comment="执行状态：pending/running/success/failed/timeout/destroyed")
    
    # 时间记录
    start_time = Column(DateTime, nullable=True, comment="开始时间")
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    destroy_time = Column(DateTime, nullable=True, comment="销毁时间")
    
    # ChaosBlade相关
    chaos_uid = Column(String(100), nullable=True, index=True, comment="ChaosBlade故障UID")
    command = Column(Text, nullable=True, comment="执行的命令")
    output = Column(Text, nullable=True, comment="命令输出")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 销毁相关
    is_auto_destroyed = Column(Boolean, default=False, comment="是否自动销毁")
    destroy_output = Column(Text, nullable=True, comment="销毁命令输出")
    
    # Celery任务相关
    celery_task_id = Column(String(100), nullable=True, comment="Celery任务ID")
    
    # 关联关系
    task = relationship("ChaosTask", foreign_keys=[task_id])
    batch_task = relationship("ChaosBatchTask", foreign_keys=[batch_task_id])
    batch_task_item = relationship("ChaosBatchTaskItem", foreign_keys=[batch_task_item_id])
    
    def __repr__(self):
        return f"<ChaosExecution(id={self.id}, status='{self.status}', chaos_uid='{self.chaos_uid}')>"
    
    @property
    def is_running(self) -> bool:
        """是否正在运行"""
        return self.status in ["pending", "running"]
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status in ["success", "failed", "timeout", "destroyed"]
    
    @property
    def duration(self) -> Optional[int]:
        """执行持续时间(秒)"""
        if self.start_time and self.end_time:
            return int((self.end_time - self.start_time).total_seconds())
        return None
    
    @property
    def is_expired(self) -> bool:
        """是否已过期（需要清理）"""
        if not self.start_time:
            return False
        
        # 运行超过24小时的任务视为过期
        max_duration = 24 * 3600  # 24小时
        current_duration = (datetime.now() - self.start_time).total_seconds()
        
        return current_duration > max_duration
    
    def can_destroy(self) -> bool:
        """是否可以销毁"""
        return self.status in ["running", "success"] and self.chaos_uid and not self.is_auto_destroyed
