#!/usr/bin/env python3
"""
Celery Worker启动脚本
用于启动Celery工作进程
"""
import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.celery_app import celery_app
from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/celery_worker.log')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """启动Celery Worker"""
    logger.info("启动Celery Worker...")
    logger.info(f"Redis连接: {settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}")
    
    # Celery Worker参数
    worker_args = [
        'worker',
        '--app=app.core.celery_app:celery_app',
        '--loglevel=info',
        '--concurrency=4',  # 并发数
        '--max-tasks-per-child=1000',  # 每个子进程最大任务数
        '--time-limit=300',  # 任务超时时间(秒)
        '--soft-time-limit=240',  # 软超时时间(秒)
        '--queues=default,chaos_execution,chaos_schedule,chaos_cleanup,data_export,data_import,data_analysis,system_maintenance,notifications',
        '--hostname=worker@%h',
        '--pidfile=celery_worker.pid',
        '--logfile=logs/celery_worker.log'
    ]
    
    # 如果是开发环境，启用自动重载
    if settings.DEBUG:
        worker_args.extend([
            '--reload',
            '--pool=solo'  # 开发环境使用solo池
        ])
    
    # 启动Worker
    celery_app.worker_main(worker_args)

if __name__ == '__main__':
    main()
