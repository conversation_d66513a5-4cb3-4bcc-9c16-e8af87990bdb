"""
混沌测试任务Schema定义
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from app.schemas.base import BaseResponseSchema, BaseSchema


class ChaosTaskBase(BaseSchema):
    """混沌测试任务基础Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    env_ids: List[int] = Field(..., min_items=1, description="目标环境ID列表")
    fault_type: str = Field(..., description="故障类型")
    fault_params: Dict[str, Any] = Field(..., description="故障参数配置")
    execution_type: str = Field(default="immediate", description="执行类型")
    scheduled_time: Optional[datetime] = Field(None, description="定时执行时间")
    periodic_config: Optional[Dict[str, Any]] = Field(None, description="周期性执行配置")
    cron_expression: Optional[str] = Field(None, description="Cron表达式")
    auto_destroy: bool = Field(default=True, description="是否自动销毁故障")
    max_duration: Optional[int] = Field(None, ge=1, description="最大执行时长(秒)")
    monitor_config: Optional[Dict[str, Any]] = Field(None, description="监控配置")

    @validator('fault_type')
    def validate_fault_type(cls, v):
        valid_types = ['cpu', 'memory', 'network', 'disk', 'process', 'k8s']
        if v not in valid_types:
            raise ValueError(f'故障类型必须是 {valid_types} 中的一个')
        return v

    @validator('execution_type')
    def validate_execution_type(cls, v):
        valid_types = ['immediate', 'scheduled', 'periodic', 'cron']
        if v not in valid_types:
            raise ValueError(f'执行类型必须是 {valid_types} 中的一个')
        return v

    @validator('scheduled_time')
    def validate_scheduled_time(cls, v, values):
        if values.get('execution_type') == 'scheduled' and not v:
            raise ValueError('定时执行类型必须指定执行时间')
        return v

    @validator('periodic_config')
    def validate_periodic_config(cls, v, values):
        if values.get('execution_type') == 'periodic':
            if not v:
                raise ValueError('周期性执行类型必须指定周期配置')
            required_keys = ['interval_type', 'interval_value']
            if not all(key in v for key in required_keys):
                raise ValueError(f'周期配置必须包含 {required_keys}')
        return v

    @validator('cron_expression')
    def validate_cron_expression(cls, v, values):
        if values.get('execution_type') == 'cron' and not v:
            raise ValueError('Cron执行类型必须指定Cron表达式')
        return v


class ChaosTaskCreate(ChaosTaskBase):
    """创建混沌测试任务Schema"""
    pass


class ChaosTaskUpdate(BaseSchema):
    """更新混沌测试任务Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    env_ids: Optional[List[int]] = Field(None, min_items=1, description="目标环境ID列表")
    fault_type: Optional[str] = Field(None, description="故障类型")
    fault_params: Optional[Dict[str, Any]] = Field(None, description="故障参数配置")
    execution_type: Optional[str] = Field(None, description="执行类型")
    scheduled_time: Optional[datetime] = Field(None, description="定时执行时间")
    periodic_config: Optional[Dict[str, Any]] = Field(None, description="周期性执行配置")
    cron_expression: Optional[str] = Field(None, description="Cron表达式")
    auto_destroy: Optional[bool] = Field(None, description="是否自动销毁故障")
    max_duration: Optional[int] = Field(None, ge=1, description="最大执行时长(秒)")
    task_status: Optional[str] = Field(None, description="任务状态")
    monitor_config: Optional[Dict[str, Any]] = Field(None, description="监控配置")


class ChaosTaskResponse(ChaosTaskBase, BaseResponseSchema):
    """混沌测试任务响应Schema"""
    task_status: str = Field(..., description="任务状态")
    last_execution_time: Optional[datetime] = Field(None, description="最后执行时间")
    execution_count: int = Field(default=0, description="执行次数")

    class Config:
        from_attributes = True


class ChaosTaskQuery(BaseSchema):
    """混沌测试任务查询Schema"""
    keyword: Optional[str] = Field(None, description="关键词搜索")
    fault_type: Optional[str] = Field(None, description="故障类型筛选")
    execution_type: Optional[str] = Field(None, description="执行类型筛选")
    task_status: Optional[str] = Field(None, description="任务状态筛选")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")

    @property
    def offset(self) -> int:
        return (self.page - 1) * self.size


class ChaosTaskPageResponse(BaseSchema):
    """混沌测试任务分页响应Schema"""
    items: List[ChaosTaskResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    pages: int = Field(..., description="总页数")


class ChaosTaskExecuteRequest(BaseSchema):
    """混沌测试任务执行请求Schema"""
    env_ids: Optional[List[int]] = Field(None, description="指定执行的环境ID列表，不指定则使用任务配置")
    force_execute: bool = Field(default=False, description="是否强制执行（忽略状态检查）")
    execution_config: Optional[Dict[str, Any]] = Field(None, description="执行配置覆盖")


class ChaosTaskExecuteResponse(BaseSchema):
    """混沌测试任务执行响应Schema"""
    task_id: int = Field(..., description="任务ID")
    celery_task_id: str = Field(..., description="Celery任务ID")
    execution_type: str = Field(..., description="执行类型")
    status: str = Field(..., description="提交状态")
    message: str = Field(..., description="提交结果消息")
    scheduled_time: Optional[datetime] = Field(None, description="调度时间")


class ChaosTaskStatsResponse(BaseSchema):
    """混沌测试任务统计响应Schema"""
    total_tasks: int = Field(..., description="总任务数")
    enabled_tasks: int = Field(..., description="启用任务数")
    disabled_tasks: int = Field(..., description="禁用任务数")
    running_executions: int = Field(..., description="运行中执行数")
    today_executions: int = Field(..., description="今日执行数")
    fault_type_stats: Dict[str, int] = Field(..., description="故障类型统计")
    execution_type_stats: Dict[str, int] = Field(..., description="执行类型统计")
