"""
用户缓存服务
提供高效的用户信息查询和缓存功能
"""
from typing import Dict, List, Optional, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.user import User
from app.db.redis import RedisClient
import json
import logging

logger = logging.getLogger(__name__)


class UserCacheService:
    """用户缓存服务"""
    
    def __init__(self, db: AsyncSession, redis_client: RedisClient = None):
        self.db = db
        self.redis_client = redis_client
        self._cache_prefix = "user_info:"
        self._cache_expire = 3600  # 1小时过期
    
    async def get_user_info(self, user_id: str) -> Optional[Dict[str, str]]:
        """
        获取单个用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户信息字典 {"id": "1", "nickname": "张三", "username": "zhangsan"}
        """
        if not user_id:
            return None
            
        # 先尝试从Redis缓存获取
        if self.redis_client:
            cache_key = f"{self._cache_prefix}{user_id}"
            cached_data = await self.redis_client.get(cache_key)
            if cached_data:
                try:
                    # 如果cached_data是字符串，直接解析JSON
                    if isinstance(cached_data, str):
                        return json.loads(cached_data)
                    else:
                        # 如果是其他类型，尝试转换为字符串再解析
                        return json.loads(str(cached_data))
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"用户缓存数据格式错误: {user_id}, 错误: {e}")
        
        # 从数据库查询
        try:
            user_id_int = int(user_id)
            result = await self.db.execute(
                select(User.id, User.nickname, User.username)
                .where(User.id == user_id_int)
            )
            user = result.first()
            
            if user:
                user_info = {
                    "id": str(user.id),
                    "nickname": user.nickname or user.username,
                    "username": user.username
                }
                
                # 缓存到Redis
                if self.redis_client:
                    cache_key = f"{self._cache_prefix}{user_id}"
                    await self.redis_client.set(
                        cache_key, 
                        json.dumps(user_info), 
                        expire=self._cache_expire
                    )
                
                return user_info
                
        except (ValueError, TypeError) as e:
            logger.warning(f"无效的用户ID: {user_id}, 错误: {e}")
        except Exception as e:
            logger.error(f"查询用户信息失败: {user_id}, 错误: {e}")
        
        return None
    
    async def get_users_info_batch(self, user_ids: List[str]) -> Dict[str, Dict[str, str]]:
        """
        批量获取用户信息
        
        Args:
            user_ids: 用户ID列表
            
        Returns:
            用户信息字典 {user_id: {"id": "1", "nickname": "张三", "username": "zhangsan"}}
        """
        if not user_ids:
            return {}
        
        # 去重并过滤空值
        unique_user_ids = list(set(filter(None, user_ids)))
        if not unique_user_ids:
            return {}
        
        result = {}
        missing_ids = []
        
        # 先尝试从Redis批量获取
        if self.redis_client:
            cache_keys = [f"{self._cache_prefix}{uid}" for uid in unique_user_ids]
            cached_values = await self.redis_client.mget(cache_keys)

            for i, cached_value in enumerate(cached_values):
                user_id = unique_user_ids[i]
                if cached_value:
                    try:
                        # 如果cached_value是字符串，直接解析JSON
                        if isinstance(cached_value, str):
                            result[user_id] = json.loads(cached_value)
                        else:
                            # 如果是其他类型，尝试转换为字符串再解析
                            result[user_id] = json.loads(str(cached_value))
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"用户缓存数据格式错误: {user_id}, 错误: {e}")
                        missing_ids.append(user_id)
                else:
                    missing_ids.append(user_id)
        else:
            missing_ids = unique_user_ids
        
        # 从数据库批量查询缺失的用户信息
        if missing_ids:
            try:
                # 转换为整数ID列表
                int_ids = []
                for uid in missing_ids:
                    try:
                        int_ids.append(int(uid))
                    except (ValueError, TypeError):
                        logger.warning(f"无效的用户ID: {uid}")
                
                if int_ids:
                    db_result = await self.db.execute(
                        select(User.id, User.nickname, User.username)
                        .where(User.id.in_(int_ids))
                    )
                    users = db_result.all()
                    
                    # 处理查询结果
                    cache_data = {}
                    for user in users:
                        user_id = str(user.id)
                        user_info = {
                            "id": user_id,
                            "nickname": user.nickname or user.username,
                            "username": user.username
                        }
                        result[user_id] = user_info
                        cache_data[f"{self._cache_prefix}{user_id}"] = json.dumps(user_info)
                    
                    # 批量缓存到Redis
                    if self.redis_client and cache_data:
                        await self.redis_client.mset(cache_data, expire=self._cache_expire)
                        
            except Exception as e:
                logger.error(f"批量查询用户信息失败: {missing_ids}, 错误: {e}")
        
        return result
    
    async def get_user_nickname(self, user_id: str) -> Optional[str]:
        """
        获取用户昵称
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户昵称，如果不存在返回None
        """
        user_info = await self.get_user_info(user_id)
        return user_info.get("nickname") if user_info else None
    
    async def invalidate_user_cache(self, user_id: str):
        """
        清除用户缓存
        
        Args:
            user_id: 用户ID
        """
        if self.redis_client:
            cache_key = f"{self._cache_prefix}{user_id}"
            await self.redis_client.delete(cache_key)
