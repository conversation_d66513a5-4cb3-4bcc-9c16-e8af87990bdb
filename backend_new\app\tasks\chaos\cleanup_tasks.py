"""
混沌测试清理任务
处理过期故障清理、任务状态检查等维护工作
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

from app.core.celery_app import celery_app
from app.core.chaos.fault_injector import fault_injector
from app.db.session import get_db
from app.services.chaos.execution_service import ChaosExecutionService

logger = logging.getLogger(__name__)


@celery_app.task
def cleanup_expired_faults():
    """清理过期的故障"""
    return asyncio.run(_cleanup_expired_faults_async())


async def _cleanup_expired_faults_async():
    """异步清理过期故障"""
    try:
        logger.info("开始清理过期故障")
        
        cleanup_count = 0
        failed_count = 0
        
        async for db in get_db():
            execution_service = ChaosExecutionService(db)
            
            # 获取运行中且已过期的执行记录
            current_time = datetime.now()
            expired_executions = await execution_service.get_expired_running_executions(current_time)
            
            logger.info(f"发现 {len(expired_executions)} 个过期的运行中故障")
            
            for execution in expired_executions:
                try:
                    if execution.chaos_uid:
                        # 尝试销毁故障
                        destroy_result = await fault_injector.destroy_fault(execution.chaos_uid)
                        
                        # 更新执行记录
                        update_data = {
                            "status": "destroyed" if destroy_result.success else "failed",
                            "is_auto_destroyed": True,
                            "destroy_time": current_time,
                            "destroy_output": destroy_result.output,
                            "error_message": destroy_result.error if not destroy_result.success else None
                        }
                        
                        await execution_service.update_execution(execution.id, update_data)
                        
                        if destroy_result.success:
                            cleanup_count += 1
                            logger.info(f"成功清理过期故障: {execution.chaos_uid}")
                        else:
                            failed_count += 1
                            logger.error(f"清理过期故障失败: {execution.chaos_uid}, {destroy_result.message}")
                    else:
                        # 没有chaos_uid，直接标记为已销毁
                        await execution_service.update_execution(execution.id, {
                            "status": "destroyed",
                            "is_auto_destroyed": True,
                            "destroy_time": current_time
                        })
                        cleanup_count += 1
                        
                except Exception as e:
                    failed_count += 1
                    logger.error(f"清理执行记录异常: {execution.id}, {str(e)}")
                    
                    # 更新为失败状态
                    try:
                        await execution_service.update_execution(execution.id, {
                            "status": "failed",
                            "error_message": f"清理异常: {str(e)}",
                            "destroy_time": current_time
                        })
                    except Exception as update_error:
                        logger.error(f"更新执行记录失败: {update_error}")
            
            break  # 只处理一个数据库会话
        
        result = {
            "cleanup_time": current_time.isoformat(),
            "total_expired": len(expired_executions) if 'expired_executions' in locals() else 0,
            "cleanup_success": cleanup_count,
            "cleanup_failed": failed_count
        }
        
        logger.info(f"过期故障清理完成: 成功 {cleanup_count}, 失败 {failed_count}")
        return result
        
    except Exception as e:
        logger.error(f"清理过期故障异常: {str(e)}")
        raise e


@celery_app.task
def check_running_tasks_status():
    """检查运行中任务的状态"""
    return asyncio.run(_check_running_tasks_status_async())


async def _check_running_tasks_status_async():
    """异步检查运行中任务状态"""
    try:
        logger.info("开始检查运行中任务状态")
        
        status_updated = 0
        status_failed = 0
        
        async for db in get_db():
            execution_service = ChaosExecutionService(db)
            
            # 获取运行中的执行记录
            running_executions = await execution_service.get_running_executions()
            
            logger.info(f"发现 {len(running_executions)} 个运行中的任务")
            
            for execution in running_executions:
                try:
                    # 检查故障是否仍然活跃
                    if execution.chaos_uid:
                        is_active = fault_injector.is_fault_active(execution.chaos_uid)
                        
                        if not is_active:
                            # 故障已不活跃，更新状态
                            await execution_service.update_execution(execution.id, {
                                "status": "completed",
                                "end_time": datetime.now()
                            })
                            status_updated += 1
                            logger.info(f"更新非活跃故障状态: {execution.chaos_uid}")
                    
                    # 检查是否超时
                    if execution.start_time:
                        running_duration = datetime.now() - execution.start_time
                        max_duration_minutes = 60  # 最大运行时间1小时
                        
                        if running_duration.total_seconds() > max_duration_minutes * 60:
                            # 任务运行时间过长，标记为超时
                            await execution_service.update_execution(execution.id, {
                                "status": "timeout",
                                "error_message": f"任务运行超时: {running_duration}",
                                "end_time": datetime.now()
                            })
                            status_updated += 1
                            logger.warning(f"任务运行超时: {execution.id}, 运行时间: {running_duration}")
                    
                except Exception as e:
                    status_failed += 1
                    logger.error(f"检查任务状态异常: {execution.id}, {str(e)}")
            
            break  # 只处理一个数据库会话
        
        result = {
            "check_time": datetime.now().isoformat(),
            "total_running": len(running_executions) if 'running_executions' in locals() else 0,
            "status_updated": status_updated,
            "status_failed": status_failed
        }
        
        logger.info(f"运行中任务状态检查完成: 更新 {status_updated}, 失败 {status_failed}")
        return result
        
    except Exception as e:
        logger.error(f"检查运行中任务状态异常: {str(e)}")
        raise e


@celery_app.task
def cleanup_completed_executions():
    """清理已完成的执行记录"""
    return asyncio.run(_cleanup_completed_executions_async())


async def _cleanup_completed_executions_async():
    """异步清理已完成的执行记录"""
    try:
        logger.info("开始清理已完成的执行记录")
        
        # 清理7天前的已完成记录
        cutoff_time = datetime.now() - timedelta(days=7)
        deleted_count = 0
        
        async for db in get_db():
            execution_service = ChaosExecutionService(db)
            
            # 获取需要清理的执行记录
            old_executions = await execution_service.get_old_completed_executions(cutoff_time)
            
            logger.info(f"发现 {len(old_executions)} 个需要清理的已完成记录")
            
            for execution in old_executions:
                try:
                    await execution_service.delete_execution(execution.id)
                    deleted_count += 1
                    
                except Exception as e:
                    logger.error(f"删除执行记录异常: {execution.id}, {str(e)}")
            
            break  # 只处理一个数据库会话
        
        result = {
            "cleanup_time": datetime.now().isoformat(),
            "cutoff_time": cutoff_time.isoformat(),
            "total_found": len(old_executions) if 'old_executions' in locals() else 0,
            "deleted_count": deleted_count
        }
        
        logger.info(f"已完成执行记录清理完成: 删除 {deleted_count} 条记录")
        return result
        
    except Exception as e:
        logger.error(f"清理已完成执行记录异常: {str(e)}")
        raise e


@celery_app.task
def cleanup_orphaned_faults():
    """清理孤儿故障（数据库中没有记录但仍在运行的故障）"""
    return asyncio.run(_cleanup_orphaned_faults_async())


async def _cleanup_orphaned_faults_async():
    """异步清理孤儿故障"""
    try:
        logger.info("开始清理孤儿故障")
        
        # 获取所有活跃故障
        active_faults = fault_injector.get_active_faults()
        
        if not active_faults:
            logger.info("没有发现活跃故障")
            return {
                "cleanup_time": datetime.now().isoformat(),
                "active_faults_count": 0,
                "orphaned_count": 0,
                "cleaned_count": 0
            }
        
        logger.info(f"发现 {len(active_faults)} 个活跃故障")
        
        orphaned_count = 0
        cleaned_count = 0
        
        async for db in get_db():
            execution_service = ChaosExecutionService(db)
            
            for fault_id, fault_config in active_faults.items():
                try:
                    # 检查数据库中是否有对应的执行记录
                    execution = await execution_service.get_execution_by_chaos_uid(fault_id)
                    
                    if not execution:
                        # 孤儿故障，尝试清理
                        orphaned_count += 1
                        logger.warning(f"发现孤儿故障: {fault_id}")
                        
                        destroy_result = await fault_injector.destroy_fault(fault_id)
                        if destroy_result.success:
                            cleaned_count += 1
                            logger.info(f"成功清理孤儿故障: {fault_id}")
                        else:
                            logger.error(f"清理孤儿故障失败: {fault_id}, {destroy_result.message}")
                    
                except Exception as e:
                    logger.error(f"检查故障记录异常: {fault_id}, {str(e)}")
            
            break  # 只处理一个数据库会话
        
        result = {
            "cleanup_time": datetime.now().isoformat(),
            "active_faults_count": len(active_faults),
            "orphaned_count": orphaned_count,
            "cleaned_count": cleaned_count
        }
        
        logger.info(f"孤儿故障清理完成: 发现 {orphaned_count} 个, 清理 {cleaned_count} 个")
        return result
        
    except Exception as e:
        logger.error(f"清理孤儿故障异常: {str(e)}")
        raise e
