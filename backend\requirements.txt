# DpTestPlatform Backend Dependencies

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
asyncpg==0.29.0        
aiomysql==0.2.0      
aiosqlite==0.19.0      
psycopg2-binary==2.9.9 
PyMySQL==1.1.0        

# Data Validation
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Configuration & Environment
python-dotenv==1.0.0

# Logging
loguru==0.7.2

# Testing
pytest==7.4.3
httpx==0.25.2

# CORS
fastapi-cors==0.0.6 

# External Services
openai==1.97.1
asyncssh==2.21.0
redis==6.2.0
aiohttp==3.12.15

# Data Processing
faker==37.4.3
pandas==2.3.1

# Task Scheduling
apscheduler==3.11.0

# Code Execution
RestrictedPython==7.4
