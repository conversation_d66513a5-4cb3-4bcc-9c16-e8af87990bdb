import request from '@/utils/http'

export class UserService {
  // 登录
  static async login(params: Api.Auth.LoginParams): Promise<Api.Auth.LoginResponse> {
    const response = await request.post<Api.Auth.LoginResponse>({
      url: '/api/auth/login',
      params
    })
    return response
  }

  // 获取用户信息
  static async getUserInfo(): Promise<Api.User.UserInfo> {
    const response = await request.get<Api.User.UserInfo>({
      url: '/api/user/info'
    })
    return response
  }

  // 获取用户列表
  static async getUserList(params: Api.Common.PaginatingSearchParams): Promise<Api.User.UserListData> {
    // 转换参数格式：current -> page
    const queryParams = {
      page: (params as any).current || params.page || 1,
      size: params.size || 10,
      keyword: params.keyword,
      status: params.status,
      is_active: params.is_active
    }

    const response = await request.get<Api.User.UserListData>({
      url: '/api/users',
      params: queryParams
    })
    return response
  }

  // 刷新令牌
  static async refreshToken(refreshToken: string): Promise<Api.Auth.LoginResponse> {
    const response = await request.post<Api.Auth.LoginResponse>({
      url: '/api/auth/refresh',
      params: {
        refresh_token: refreshToken
      }
    })
    return response
  }

  // 用户登出
  static async logout(): Promise<void> {
    await request.post<void>({
      url: '/api/auth/logout'
    })
  }

  // 更新用户资料
  static async updateProfile(data: Partial<Api.User.UserInfo>): Promise<void> {
    await request.put<void>({
      url: '/api/user/profile',
      params: data
    })
  }

  // 更新用户头像
  static async updateAvatar(avatarUrl: string): Promise<Api.Common.BaseResponse> {
    const response = await request.put<Api.Common.BaseResponse>({
      url: '/api/user/avatar',
      params: { avatar_url: avatarUrl },
      data: {} // 明确指定data为空对象，避免params被移动到data
    })
    return response
  }

  // 新增用户
  static async addUser(data: Api.User.UserCreateParams): Promise<Api.Common.BaseResponse> {
    const response = await request.post<Api.Common.BaseResponse>({
      url: '/api/users',  // 修复路径：从 /add 改为空，符合RESTful规范
      params: data
    })
    return response
  }

  // 更新用户信息
  static async updateUser(userId: number, data: Api.User.UserUpdateParams): Promise<Api.Common.BaseResponse> {
    const response = await request.put<Api.Common.BaseResponse>({
      url: `/api/users/${userId}`,
      params: data
    })
    return response
  }

  // 获取角色列表
  static async getRoleList(): Promise<Api.Common.BaseResponse> {
    const response = await request.get<Api.Common.BaseResponse>({
      url: '/api/roles/list'
    })
    return response
  }

  // 删除用户
  static async deleteUser(userId: number): Promise<Api.Common.BaseResponse> {
    const response = await request.del<Api.Common.BaseResponse>({
      url: `/api/users/${userId}`
    })
    return response
  }

  // 更新用户状态
  static async updateUserStatus(userId: number, status: string): Promise<Api.Common.BaseResponse> {
    const response = await request.put<Api.Common.BaseResponse>({
      url: `/api/users/${userId}/status`,
      params: { status }
    })
    return response
  }
}
