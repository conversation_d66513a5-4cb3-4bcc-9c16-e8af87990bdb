"""
环境管理业务服务
"""
import time
import socket
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.environment import EnvironmentCRUD
from app.schemas.environment import (
    EnvironmentCreate, EnvironmentUpdate, EnvironmentResponse, EnvironmentQuery,
    EnvironmentPageResponse, ConnectionTestRequest, ConnectionTestResponse,
    EnvironmentStatsResponse, SupportedTypeResponse
)
from app.core.exceptions import (
    raise_not_found_error, raise_conflict_error, raise_business_error
)
from app.services.base import BaseService


class EnvironmentService(BaseService):
    """环境管理业务服务类"""

    def __init__(self, db: AsyncSession, redis_client = None):
        super().__init__(db, redis_client)
        self.env_crud = EnvironmentCRUD(db)

    async def create_environment(self, env_data: EnvironmentCreate) -> EnvironmentResponse:
        """创建环境"""
        # 检查名称是否重复
        existing_env = await self.env_crud.get_by_name(env_data.name)
        if existing_env:
            raise_conflict_error(f"环境名称 '{env_data.name}' 已存在")

        env = await self.env_crud.create_environment(env_data)
        return self._convert_to_response(env)

    async def list_environments(self, query: EnvironmentQuery) -> EnvironmentPageResponse:
        """查询环境列表"""
        environments, total = await self.env_crud.list(
            keyword=query.keyword,
            env_type=query.env_type,
            status=query.status,
            tags=query.tags,
            offset=query.offset,
            limit=query.size
        )

        # 转换为响应格式
        env_responses = [self._convert_to_response(env) for env in environments]
        await self._resolve_user_names(env_responses)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return EnvironmentPageResponse(
            items=env_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def get_environment_by_id(self, env_id: int) -> EnvironmentResponse:
        """根据ID获取环境"""
        env = await self.env_crud.get(self.db, env_id)
        if not env:
            raise_not_found_error("环境不存在")

        return self._convert_to_response(env)

    async def update_environment(self, env_id: int, env_data: EnvironmentUpdate) -> EnvironmentResponse:
        """更新环境"""
        env = await self.env_crud.update_environment(env_id, env_data)
        if not env:
            raise_not_found_error("环境不存在")

        return self._convert_to_response(env)

    async def delete_environment(self, env_id: int) -> bool:
        """删除环境"""
        env = await self.env_crud.remove(self.db, id=env_id)
        if not env:
            raise_not_found_error("环境不存在")

        return True

    async def test_connection(self, test_request: ConnectionTestRequest) -> ConnectionTestResponse:
        """测试环境连接"""
        start_time = time.time()
        
        try:
            # 简化的连接测试逻辑
            success, message, details = await self._simple_connection_test(
                test_request.type, 
                test_request.config or {},
                test_request.timeout or 10
            )
            
            duration = time.time() - start_time
            
            return ConnectionTestResponse(
                success=success,
                message=message,
                duration=duration,
                details=details
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionTestResponse(
                success=False,
                message=f"连接测试失败: {str(e)}",
                duration=duration,
                details={"error": str(e), "error_type": type(e).__name__}
            )

    async def test_environment_connection(self, env_id: int, test_request: ConnectionTestRequest = None) -> ConnectionTestResponse:
        """测试指定环境的连接"""
        env = await self.env_crud.get(self.db, env_id)
        if not env:
            raise_not_found_error("环境不存在")

        # 构建连接测试请求
        connection_test_request = ConnectionTestRequest(
            type=env.type,
            config={
                'host': env.host,
                'port': env.port,
                **(env.config or {}),
                **(test_request.config if test_request and test_request.config else {})
            },
            timeout=test_request.timeout if test_request else 10
        )

        # 执行连接测试
        result = await self.test_connection(connection_test_request)

        # 更新环境状态
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await self.env_crud.update_status(
                env_id,
                'connected' if result.success else 'failed',
                current_time
            )
        except Exception as e:
            # 记录更新失败，但不影响测试结果返回
            pass

        return result

    async def batch_test_connections(self, env_ids: List[int]) -> List[Dict[str, Any]]:
        """批量测试环境连接"""
        results = []
        environments = await self.env_crud.get_environments_by_ids(env_ids)
        
        # 并发测试连接（限制并发数）
        semaphore = asyncio.Semaphore(5)  # 最多5个并发连接测试
        
        async def test_single_env(env):
            async with semaphore:
                try:
                    test_request = ConnectionTestRequest(
                        type=env.type,
                        config={
                            'host': env.host,
                            'port': env.port,
                            **(env.config or {})
                        }
                    )
                    
                    result = await self.test_connection(test_request)
                    
                    # 更新环境状态
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    await self.env_crud.update_status(
                        env.id,
                        'connected' if result.success else 'failed',
                        current_time
                    )
                    
                    return {
                        'id': env.id,
                        'name': env.name,
                        'success': result.success,
                        'message': result.message,
                        'duration': result.duration
                    }
                    
                except Exception as e:
                    return {
                        'id': env.id,
                        'name': env.name,
                        'success': False,
                        'message': f"测试失败: {str(e)}",
                        'duration': 0
                    }
        
        # 并发执行测试
        tasks = [test_single_env(env) for env in environments]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                env = environments[i]
                final_results.append({
                    'id': env.id,
                    'name': env.name,
                    'success': False,
                    'message': f"测试异常: {str(result)}",
                    'duration': 0
                })
            else:
                final_results.append(result)
        
        return final_results

    async def get_environment_stats(self) -> EnvironmentStatsResponse:
        """获取环境统计信息"""
        try:
            # 类型统计
            type_stats = await self.env_crud.get_type_stats()
            
            # 状态统计
            status_stats = await self.env_crud.get_status_stats()
            
            # 最近环境
            recent_envs = await self.env_crud.get_recent_environments(limit=5)
            recent_env_responses = [self._convert_to_response(env) for env in recent_envs]
            
            # 总数统计
            total_count = sum(type_stats.values())
            
            return EnvironmentStatsResponse(
                total_count=total_count,
                type_stats=type_stats,
                status_stats=status_stats,
                recent_environments=recent_env_responses
            )
        except Exception as e:
            raise_business_error(f"获取环境统计信息失败: {str(e)}")

    async def get_supported_types(self) -> List[SupportedTypeResponse]:
        """获取支持的环境类型"""
        # 硬编码支持的类型
        supported_types = [
            {
                "type": "database",
                "display_name": "数据库",
                "description": "MySQL、PostgreSQL等关系型数据库",
                "default_port": 3306,
                "required_fields": ["db_type", "database_name"],
                "optional_fields": ["username", "password"],
                "icon": "database",
                "category": "数据存储"
            },
            {
                "type": "redis",
                "display_name": "Redis",
                "description": "Redis缓存数据库",
                "default_port": 6379,
                "required_fields": [],
                "optional_fields": ["password", "db"],
                "icon": "redis",
                "category": "缓存"
            },
            {
                "type": "ssh",
                "display_name": "SSH服务器",
                "description": "Linux/Unix服务器SSH连接",
                "default_port": 22,
                "required_fields": ["username"],
                "optional_fields": ["password", "private_key_path"],
                "icon": "server",
                "category": "服务器"
            },
            {
                "type": "api",
                "display_name": "API服务",
                "description": "HTTP/HTTPS API接口",
                "default_port": 80,
                "required_fields": [],
                "optional_fields": ["api_key", "headers"],
                "icon": "api",
                "category": "接口"
            }
        ]
        
        return [SupportedTypeResponse(**type_info) for type_info in supported_types]

    async def _simple_connection_test(self, env_type: str, config: Dict[str, Any], timeout: int) -> tuple:
        """智能连接测试实现 - 根据环境类型选择合适的测试方法"""
        host = config.get('host')
        port = config.get('port')

        if not host:
            return False, "缺少主机地址", {}

        try:
            # 根据环境类型选择测试方法
            if env_type.lower() == 'ssh':
                return await self._test_ssh_connection(config, timeout)
            elif env_type.lower() in ['database', 'mysql', 'postgresql', 'oracle']:
                return await self._test_database_connection(env_type, config, timeout)
            elif env_type.lower() in ['redis', 'cache']:
                return await self._test_redis_connection(config, timeout)
            elif env_type.lower() in ['http', 'https', 'api', 'web']:
                return await self._test_http_connection(config, timeout)
            else:
                # 对于未知类型，回退到TCP测试，但提供更详细的信息
                return await self._test_tcp_connection(host, port, timeout)

        except Exception as e:
            return False, f"连接测试异常: {str(e)}", {"error": str(e), "error_type": type(e).__name__}

    async def _test_ssh_connection(self, config: Dict[str, Any], timeout: int) -> tuple:
        """SSH连接测试"""
        try:
            from app.utils.clients.ssh_client import SSHClient

            ssh_client = SSHClient(config)
            result = await ssh_client.test_connection(timeout)

            return result.success, result.message, result.details

        except ImportError:
            return False, "SSH客户端不可用，请安装asyncssh", {"error": "missing_dependency"}
        except Exception as e:
            return False, f"SSH测试失败: {str(e)}", {"error": str(e)}

    async def _test_database_connection(self, db_type: str, config: Dict[str, Any], timeout: int) -> tuple:
        """数据库连接测试"""
        # 这里可以根据数据库类型实现具体的连接测试
        # 暂时使用TCP测试作为基础，后续可以扩展
        host = config.get('host')
        port = config.get('port', 3306 if db_type.lower() == 'mysql' else 5432)

        success, message, details = await self._test_tcp_connection(host, port, timeout)

        if success:
            details.update({
                "database_type": db_type,
                "note": "TCP连接成功，建议实现具体的数据库连接测试"
            })

        return success, message, details

    async def _test_redis_connection(self, config: Dict[str, Any], timeout: int) -> tuple:
        """Redis连接测试"""
        # 暂时使用TCP测试，后续可以实现Redis协议测试
        host = config.get('host')
        port = config.get('port', 6379)

        success, message, details = await self._test_tcp_connection(host, port, timeout)

        if success:
            details.update({
                "service_type": "redis",
                "note": "TCP连接成功，建议实现Redis协议测试"
            })

        return success, message, details

    async def _test_http_connection(self, config: Dict[str, Any], timeout: int) -> tuple:
        """HTTP/HTTPS连接测试"""
        try:
            import aiohttp
            import asyncio

            host = config.get('host')
            port = config.get('port')
            protocol = config.get('protocol', 'http')
            path = config.get('path', '/')

            # 构建URL
            if port:
                url = f"{protocol}://{host}:{port}{path}"
            else:
                url = f"{protocol}://{host}{path}"

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.get(url) as response:
                    return True, f"HTTP连接成功 - 状态码: {response.status}", {
                        "url": url,
                        "status_code": response.status,
                        "headers": dict(response.headers)
                    }

        except ImportError:
            # 如果没有aiohttp，回退到TCP测试
            host = config.get('host')
            port = config.get('port', 80 if config.get('protocol', 'http') == 'http' else 443)
            return await self._test_tcp_connection(host, port, timeout)
        except Exception as e:
            return False, f"HTTP测试失败: {str(e)}", {"error": str(e)}

    async def _test_tcp_connection(self, host: str, port: int, timeout: int) -> tuple:
        """TCP连接测试 - 改进版本"""
        try:
            import socket
            import asyncio

            # 使用异步方式进行TCP连接测试
            future = asyncio.get_event_loop().run_in_executor(
                None, self._sync_tcp_test, host, port, timeout
            )

            result = await asyncio.wait_for(future, timeout=timeout + 1)
            return result

        except asyncio.TimeoutError:
            return False, f"连接超时: {host}:{port}", {"host": host, "port": port, "timeout": timeout}
        except Exception as e:
            return False, f"TCP测试异常: {str(e)}", {"error": str(e)}

    def _sync_tcp_test(self, host: str, port: int, timeout: int) -> tuple:
        """同步TCP连接测试"""
        try:
            import socket

            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)

            result = sock.connect_ex((host, port))
            sock.close()

            if result == 0:
                return True, f"TCP连接 {host}:{port} 成功", {
                    "host": host,
                    "port": port,
                    "connection_type": "tcp",
                    "note": "仅验证TCP连接，不保证服务可用性"
                }
            else:
                return False, f"无法连接到 {host}:{port}", {
                    "host": host,
                    "port": port,
                    "error_code": result,
                    "error_description": self._get_socket_error_description(result)
                }

        except Exception as e:
            return False, f"连接测试异常: {str(e)}", {"error": str(e)}

    def _get_socket_error_description(self, error_code: int) -> str:
        """获取socket错误码的描述"""
        error_descriptions = {
            10061: "连接被拒绝 - 目标端口未开放或服务未运行",
            10060: "连接超时 - 网络不可达或防火墙阻止",
            11001: "主机名解析失败 - DNS查询失败",
            10051: "网络不可达 - 路由问题",
            10054: "连接被重置 - 远程主机强制关闭连接"
        }
        return error_descriptions.get(error_code, f"未知错误码: {error_code}")

    def _convert_to_response(self, env) -> EnvironmentResponse:
        """将Environment对象转换为EnvironmentResponse"""
        return EnvironmentResponse(
            id=env.id,
            name=env.name,
            type=env.type,
            description=env.description,
            host=env.host,
            port=env.port,
            status=env.status,
            last_test_time=env.last_test_time,
            tags=env.tags,
            created_at=env.created_at,
            updated_at=env.updated_at,
            created_by=env.created_by,
            updated_by=env.updated_by,
            created_by_name=None,  # 将在_resolve_user_names中设置
            updated_by_name=None,  # 将在_resolve_user_names中设置
        )
