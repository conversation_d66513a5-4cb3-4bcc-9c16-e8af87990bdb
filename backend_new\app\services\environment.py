"""
环境管理业务服务
"""
import time
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.environment import EnvironmentCRUD
from app.schemas.environment import (
    EnvironmentCreate, EnvironmentUpdate, EnvironmentResponse, EnvironmentQuery,
    EnvironmentPageResponse, ConnectionTestRequest, ConnectionTestResponse,
    EnvironmentStatsResponse, SupportedTypeResponse
)
from app.core.exceptions import (
    raise_not_found_error, raise_conflict_error, raise_business_error
)
from app.services.base import BaseService


class EnvironmentService(BaseService):
    """环境管理业务服务类"""

    def __init__(self, db: AsyncSession, redis_client = None):
        super().__init__(db, redis_client)
        self.env_crud = EnvironmentCRUD(db)

    async def create_environment(self, env_data: EnvironmentCreate) -> EnvironmentResponse:
        """创建环境"""
        # 检查名称是否重复
        existing_env = await self.env_crud.get_by_name(env_data.name)
        if existing_env:
            raise_conflict_error(f"环境名称 '{env_data.name}' 已存在")

        env = await self.env_crud.create_environment(env_data)
        return self._convert_to_response(env)

    async def list_environments(self, query: EnvironmentQuery) -> EnvironmentPageResponse:
        """查询环境列表"""
        environments, total = await self.env_crud.list(
            keyword=query.keyword,
            env_type=query.env_type,
            status=query.status,
            tags=query.tags,
            offset=query.offset,
            limit=query.size
        )

        # 转换为响应格式
        env_responses = [self._convert_to_response(env) for env in environments]
        await self._resolve_user_names(env_responses)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return EnvironmentPageResponse(
            items=env_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def get_environment_by_id(self, env_id: int) -> EnvironmentResponse:
        """根据ID获取环境"""
        env = await self.env_crud.get(self.db, env_id)
        if not env:
            raise_not_found_error("环境不存在")

        return self._convert_to_response(env)

    async def update_environment(self, env_id: int, env_data: EnvironmentUpdate) -> EnvironmentResponse:
        """更新环境"""
        env = await self.env_crud.update_environment(env_id, env_data)
        if not env:
            raise_not_found_error("环境不存在")

        return self._convert_to_response(env)

    async def delete_environment(self, env_id: int) -> bool:
        """删除环境"""
        env = await self.env_crud.remove(self.db, id=env_id)
        if not env:
            raise_not_found_error("环境不存在")

        return True

    async def test_connection(self, test_request: ConnectionTestRequest) -> ConnectionTestResponse:
        """测试环境连接"""
        start_time = time.time()
        
        try:
            # 简化的连接测试逻辑
            success, message, details = await self._simple_connection_test(
                test_request.type, 
                test_request.config or {},
                test_request.timeout or 10
            )
            
            duration = time.time() - start_time
            
            return ConnectionTestResponse(
                success=success,
                message=message,
                duration=duration,
                details=details
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionTestResponse(
                success=False,
                message=f"连接测试失败: {str(e)}",
                duration=duration,
                details={"error": str(e), "error_type": type(e).__name__}
            )

    async def test_environment_connection(self, env_id: int, test_request: ConnectionTestRequest = None) -> ConnectionTestResponse:
        """测试指定环境的连接"""
        env = await self.env_crud.get(self.db, env_id)
        if not env:
            raise_not_found_error("环境不存在")

        # 构建连接测试请求
        connection_test_request = ConnectionTestRequest(
            type=env.type,
            config={
                'host': env.host,
                'port': env.port,
                **(env.config or {}),
                **(test_request.config if test_request and test_request.config else {})
            },
            timeout=test_request.timeout if test_request else 10
        )

        # 执行连接测试
        result = await self.test_connection(connection_test_request)

        # 更新环境状态
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await self.env_crud.update_status(
                env_id,
                'connected' if result.success else 'failed',
                current_time
            )
        except Exception as e:
            # 记录更新失败，但不影响测试结果返回
            pass

        return result

    async def batch_test_connections(self, env_ids: List[int]) -> List[Dict[str, Any]]:
        """批量测试环境连接"""
        results = []
        environments = await self.env_crud.get_environments_by_ids(env_ids)
        
        # 并发测试连接（限制并发数）
        semaphore = asyncio.Semaphore(5)  # 最多5个并发连接测试
        
        async def test_single_env(env):
            async with semaphore:
                try:
                    test_request = ConnectionTestRequest(
                        type=env.type,
                        config={
                            'host': env.host,
                            'port': env.port,
                            **(env.config or {})
                        }
                    )
                    
                    result = await self.test_connection(test_request)
                    
                    # 更新环境状态
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    await self.env_crud.update_status(
                        env.id,
                        'connected' if result.success else 'failed',
                        current_time
                    )
                    
                    return {
                        'id': env.id,
                        'name': env.name,
                        'success': result.success,
                        'message': result.message,
                        'duration': result.duration
                    }
                    
                except Exception as e:
                    return {
                        'id': env.id,
                        'name': env.name,
                        'success': False,
                        'message': f"测试失败: {str(e)}",
                        'duration': 0
                    }
        
        # 并发执行测试
        tasks = [test_single_env(env) for env in environments]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                env = environments[i]
                final_results.append({
                    'id': env.id,
                    'name': env.name,
                    'success': False,
                    'message': f"测试异常: {str(result)}",
                    'duration': 0
                })
            else:
                final_results.append(result)
        
        return final_results

    async def get_environment_stats(self) -> EnvironmentStatsResponse:
        """获取环境统计信息"""
        try:
            # 类型统计
            type_stats = await self.env_crud.get_type_stats()
            
            # 状态统计
            status_stats = await self.env_crud.get_status_stats()
            
            # 最近环境
            recent_envs = await self.env_crud.get_recent_environments(limit=5)
            recent_env_responses = [self._convert_to_response(env) for env in recent_envs]
            
            # 总数统计
            total_count = sum(type_stats.values())
            
            return EnvironmentStatsResponse(
                total_count=total_count,
                type_stats=type_stats,
                status_stats=status_stats,
                recent_environments=recent_env_responses
            )
        except Exception as e:
            raise_business_error(f"获取环境统计信息失败: {str(e)}")

    async def get_supported_types(self) -> List[SupportedTypeResponse]:
        """获取支持的环境类型"""
        # 硬编码支持的类型
        supported_types = [
            {
                "type": "database",
                "display_name": "数据库",
                "description": "MySQL、PostgreSQL等关系型数据库",
                "default_port": 3306,
                "required_fields": ["db_type", "database_name"],
                "optional_fields": ["username", "password"],
                "icon": "database",
                "category": "数据存储"
            },
            {
                "type": "redis",
                "display_name": "Redis",
                "description": "Redis缓存数据库",
                "default_port": 6379,
                "required_fields": [],
                "optional_fields": ["password", "db"],
                "icon": "redis",
                "category": "缓存"
            },
            {
                "type": "ssh",
                "display_name": "SSH服务器",
                "description": "Linux/Unix服务器SSH连接",
                "default_port": 22,
                "required_fields": ["username"],
                "optional_fields": ["password", "private_key_path"],
                "icon": "server",
                "category": "服务器"
            },
            {
                "type": "api",
                "display_name": "API服务",
                "description": "HTTP/HTTPS API接口",
                "default_port": 80,
                "required_fields": [],
                "optional_fields": ["api_key", "headers"],
                "icon": "api",
                "category": "接口"
            }
        ]
        
        return [SupportedTypeResponse(**type_info) for type_info in supported_types]

    async def _simple_connection_test(self, env_type: str, config: Dict[str, Any], timeout: int) -> tuple:
        """简化的连接测试实现"""
        host = config.get('host')
        port = config.get('port')
        
        if not host:
            return False, "缺少主机地址", {}
        
        try:
            # 简单的TCP连接测试
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            
            result = sock.connect_ex((host, port or 80))
            sock.close()
            
            if result == 0:
                return True, f"连接 {host}:{port} 成功", {"host": host, "port": port}
            else:
                return False, f"无法连接到 {host}:{port}", {"host": host, "port": port, "error_code": result}
                
        except Exception as e:
            return False, f"连接测试异常: {str(e)}", {"error": str(e)}

    def _convert_to_response(self, env) -> EnvironmentResponse:
        """将Environment对象转换为EnvironmentResponse"""
        return EnvironmentResponse(
            id=env.id,
            name=env.name,
            type=env.type,
            description=env.description,
            host=env.host,
            port=env.port,
            status=env.status,
            last_test_time=env.last_test_time,
            tags=env.tags,
            created_at=env.created_at,
            updated_at=env.updated_at,
            created_by=env.created_by,
            updated_by=env.updated_by,
            created_by_name=None,  # 将在_resolve_user_names中设置
            updated_by_name=None,  # 将在_resolve_user_names中设置
        )
