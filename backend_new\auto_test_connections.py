#!/usr/bin/env python3
"""
自动连接测试 - 直接调用服务层
"""
import asyncio
import sys
import json

# 添加项目路径
sys.path.append('.')

from app.services.environment import EnvironmentService
from app.schemas.environment import ConnectionTestRequest
from app.db.session import get_async_db


async def test_all_connections():
    """测试各种连接类型"""
    print("🚀 开始全面连接测试...")
    print("=" * 60)
    
    # 测试配置
    test_cases = [
        {
            "name": "HTTP连接测试 - httpbin.org",
            "type": "http",
            "config": {
                "host": "httpbin.org",
                "port": 80,
                "protocol": "http",
                "path": "/get"
            }
        },
        {
            "name": "HTTPS连接测试 - httpbin.org",
            "type": "https",
            "config": {
                "host": "httpbin.org",
                "port": 443,
                "protocol": "https",
                "path": "/get"
            }
        },
        {
            "name": "TCP连接测试 - 百度",
            "type": "tcp",
            "config": {
                "host": "www.baidu.com",
                "port": 80
            }
        },
        {
            "name": "TCP连接测试 - Google DNS",
            "type": "tcp",
            "config": {
                "host": "*******",
                "port": 53
            }
        },
        {
            "name": "SSH连接测试 - 无效地址",
            "type": "ssh",
            "config": {
                "host": "192.168.1.999",  # 无效IP
                "port": 22,
                "username": "test",
                "password": "test"
            }
        },
        {
            "name": "数据库连接测试 - 本地MySQL",
            "type": "mysql",
            "config": {
                "host": "127.0.0.1",
                "port": 3306
            }
        },
        {
            "name": "Redis连接测试 - 本地Redis",
            "type": "redis",
            "config": {
                "host": "127.0.0.1",
                "port": 6379
            }
        }
    ]
    
    async for db in get_async_db():
        env_service = EnvironmentService(db)
        
        success_count = 0
        total_count = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 测试 {i}/{total_count}: {test_case['name']}")
            print("-" * 50)
            
            try:
                # 创建测试请求
                test_request = ConnectionTestRequest(
                    type=test_case['type'],
                    config=test_case['config'],
                    timeout=10
                )
                
                # 执行测试
                result = await env_service.test_connection(test_request)
                
                # 显示结果
                success_icon = "✅" if result.success else "❌"
                print(f"{success_icon} 结果: {result.success}")
                print(f"📝 消息: {result.message}")
                print(f"⏱️  耗时: {result.duration:.2f}秒")
                
                if result.success:
                    success_count += 1
                
                if result.details:
                    print(f"📊 详情:")
                    for key, value in result.details.items():
                        if isinstance(value, dict):
                            print(f"   {key}: {json.dumps(value, ensure_ascii=False)}")
                        else:
                            print(f"   {key}: {value}")
                
            except Exception as e:
                print(f"❌ 测试异常: {str(e)}")
                import traceback
                traceback.print_exc()
        
        break  # 只需要一个数据库连接
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成！成功: {success_count}/{total_count}")
    
    # 显示测试总结
    success_rate = (success_count / total_count) * 100
    if success_rate >= 70:
        print(f"🎉 测试通过率: {success_rate:.1f}% - 连接测试系统工作正常！")
    elif success_rate >= 40:
        print(f"⚠️  测试通过率: {success_rate:.1f}% - 部分连接可能有问题")
    else:
        print(f"❌ 测试通过率: {success_rate:.1f}% - 连接测试系统可能存在问题")


if __name__ == "__main__":
    print("📋 自动环境连接测试工具")
    print("💡 这个测试直接调用服务层，验证连接测试功能")
    print()
    
    asyncio.run(test_all_connections())
