"""
健康检查API
提供系统健康状态检查接口
"""
from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.api.deps import get_db, get_redis_client
from app.db.redis import RedisClient
from app.core.config import settings
from app.schemas.base import response_builder

router = APIRouter()


@router.get("/", summary="基础健康检查")
async def health_check():
    """基础健康检查接口"""
    return response_builder.success({
        "status": "healthy",
        "timestamp": datetime.now(),
        "service": settings.APP_NAME,
        "version": settings.APP_VERSION
    })


@router.get("/detailed", summary="详细健康检查")
async def detailed_health_check(
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client)
):
    """详细健康检查接口，包含数据库和Redis连接状态"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now(),
        "service": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "components": {}
    }
    
    # 检查数据库连接
    try:
        await db.execute("SELECT 1")
        health_status["components"]["database"] = {
            "status": "healthy",
            "message": "数据库连接正常"
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "message": f"数据库连接失败: {str(e)}"
        }
    
    # 检查Redis连接
    try:
        if redis_client.redis_client:
            await redis_client.redis_client.ping()
            health_status["components"]["redis"] = {
                "status": "healthy",
                "message": "Redis连接正常"
            }
        else:
            health_status["components"]["redis"] = {
                "status": "unhealthy",
                "message": "Redis客户端未初始化"
            }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["components"]["redis"] = {
            "status": "unhealthy",
            "message": f"Redis连接失败: {str(e)}"
        }
    
    return response_builder.success(health_status)
