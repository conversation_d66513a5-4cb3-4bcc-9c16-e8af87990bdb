"""
用户相关 Pydantic 模式
"""
from typing import List, Optional
from pydantic import BaseModel, Field, EmailStr, field_validator
from app.schemas.base import BaseSchema, BaseResponseSchema, PaginationSchema, PaginationResponseSchema


# ------------------------------
# 基础模型（公共字段）
# ------------------------------
class UserBase(BaseSchema):
    """用户基础模型（包含公共字段）"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    avatar: Optional[str] = Field(None, description="头像URL")
    description: Optional[str] = Field(None, description="个人简介")
    is_active: Optional[bool] = Field(None, description="是否激活")


# ------------------------------
# 请求模型（接收客户端数据）
# ------------------------------
class UserCreate(UserBase):
    """创建用户请求模型（必填字段）"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    password: str = Field(..., min_length=6, max_length=50, description="密码")
    is_superuser: bool = Field(default=False, description="是否超级用户")
    role_ids: Optional[List[int]] = Field(default=[], description="角色ID列表")

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('用户名只能包含字母、数字、下划线和连字符')
        return v


class UserUpdate(UserBase):
    """更新用户请求模型（可选字段）"""
    password: Optional[str] = Field(None, min_length=6, max_length=50, description="新密码（可选）")
    is_superuser: Optional[bool] = Field(None, description="是否超级用户")
    role_ids: Optional[List[int]] = Field(None, description="角色ID列表")


class UserQuery(PaginationSchema):
    """用户查询参数模型"""
    keyword: Optional[str] = Field(None, description="搜索关键词")
    status: Optional[str] = Field(None, description="用户状态筛选")
    is_active: Optional[bool] = Field(None, description="激活状态筛选")


# ------------------------------
# 响应模型（返回给客户端的数据）
# ------------------------------
class UserResponse(UserBase, BaseResponseSchema):
    """用户详情响应模型"""
    id: int = Field(..., description="用户ID")
    is_superuser: bool = Field(..., description="是否超级管理员")
    status: str = Field(default="1", description="用户状态")
    last_login_at: Optional[str] = Field(None, description="最后登录时间")
    roles: List[str] = Field(default=[], description="角色代码列表")

    model_config = {"from_attributes": True}


class UserPageResponse(BaseModel):
    """用户列表分页响应模型"""
    items: List[UserResponse] = Field(..., description="当前页用户列表")
    total: int = Field(..., description="总条数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页条数")
    pages: int = Field(..., description="总页数")


class UserUpdatePassword(BaseSchema):
    """用户密码更新模式"""
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, max_length=50, description="新密码")


# ------------------------------
# 认证相关Schema
# ------------------------------
class LoginRequest(BaseSchema):
    """登录请求模式"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class LoginResponse(BaseSchema):
    """登录响应模式"""
    token: str = Field(..., description="访问令牌")
    refreshToken: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    user: UserResponse = Field(..., description="用户信息")


class TokenResponse(BaseSchema):
    """Token响应模式"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")


class TokenData(BaseSchema):
    """Token 数据模式"""
    username: str = Field(..., description="用户名")
    user_id: int = Field(..., description="用户ID")
    is_superuser: bool = Field(default=False, description="是否超级用户")
    roles: list = Field(default=[], description="角色列表")


class RefreshTokenRequest(BaseSchema):
    """刷新令牌请求模式"""
    refresh_token: str = Field(..., description="刷新令牌")


class RegisterRequest(BaseSchema):
    """注册请求模式"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    password: str = Field(..., min_length=6, max_length=50, description="密码")
    email: EmailStr = Field(..., description="邮箱")
    nickname: str = Field(default="", description="昵称")


# ------------------------------
# 角色相关Schema
# ------------------------------
class RoleResponse(BaseSchema):
    """角色响应模式"""
    id: int = Field(..., description="角色ID")
    roleCode: str = Field(..., description="角色代码")
    roleName: str = Field(..., description="角色名称")
    description: str = Field(default="", description="角色描述")
    status: str = Field(default="1", description="角色状态")
    createTime: str = Field(..., description="创建时间")
    updateTime: str = Field(..., description="更新时间")

    model_config = {"from_attributes": True}
