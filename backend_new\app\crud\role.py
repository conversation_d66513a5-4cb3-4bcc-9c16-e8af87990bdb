"""
角色CRUD操作
"""
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.user import Role


class RoleCRUD:
    """角色CRUD操作类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_all_active_roles(self) -> List[Role]:
        """获取所有激活的角色"""
        result = await self.db.execute(
            select(Role)
            .where(Role.is_active == True)
            .order_by(Role.created_at.asc())
        )
        return result.scalars().all()

    async def get_by_id(self, role_id: int) -> Role:
        """根据ID获取角色"""
        result = await self.db.execute(
            select(Role).where(Role.id == role_id)
        )
        return result.scalar_one_or_none()

    async def get_by_code(self, code: str) -> Role:
        """根据代码获取角色"""
        result = await self.db.execute(
            select(Role).where(Role.code == code)
        )
        return result.scalar_one_or_none()
