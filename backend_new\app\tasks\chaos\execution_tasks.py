"""
混沌测试执行任务
基于Celery的分布式故障注入任务
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from celery import Task

from app.core.celery_app import celery_app
from app.core.chaos.fault_injector import fault_injector, FaultConfig
from app.db.session import get_db
from app.services.chaos.execution_service import ChaosExecutionService
from app.services.chaos.task_service import ChaosTaskService

logger = logging.getLogger(__name__)


class CallbackTask(Task):
    """带回调的任务基类"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功回调"""
        logger.info(f"Task {task_id} completed successfully")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败回调"""
        logger.error(f"Task {task_id} failed: {exc}")
        # 这里可以添加失败处理逻辑，如更新数据库状态


@celery_app.task(bind=True, base=CallbackTask, max_retries=3)
def execute_single_chaos_task(self, task_id: int, execution_config: Dict[str, Any]):
    """
    执行单次混沌测试任务
    
    Args:
        task_id: 任务ID
        execution_config: 执行配置
    """
    return asyncio.run(_execute_single_chaos_task_async(self, task_id, execution_config))


async def _execute_single_chaos_task_async(task_instance, task_id: int, execution_config: Dict[str, Any]):
    """异步执行单次混沌测试任务"""
    execution_id = None
    
    try:
        logger.info(f"开始执行混沌测试任务: {task_id}")
        
        # 获取数据库会话
        async for db in get_db():
            # 获取任务服务
            task_service = ChaosTaskService(db)
            execution_service = ChaosExecutionService(db)
            
            # 获取任务详情
            task = await task_service.get_task_by_id(task_id)
            if not task:
                raise ValueError(f"任务不存在: {task_id}")
            
            # 创建执行记录
            execution_data = {
                "task_id": task_id,
                "host_id": execution_config.get("host_id"),
                "host_info": execution_config.get("host_info"),
                "fault_config": {
                    "fault_type": task.fault_type,
                    "fault_params": task.fault_params
                },
                "status": "running",
                "start_time": datetime.now()
            }
            
            execution = await execution_service.create_execution(execution_data)
            execution_id = execution.id
            
            # 构建故障配置
            fault_config = FaultConfig(
                fault_type=task.fault_type,
                fault_params=task.fault_params,
                host_info=execution_config.get("host_info"),
                max_duration=task.max_duration,
                auto_destroy=task.auto_destroy,
                execution_id=execution_id,
                task_id=task_id
            )
            
            # 执行故障注入
            injection_result = await fault_injector.inject_single(fault_config)
            
            # 更新执行记录
            update_data = {
                "end_time": datetime.now(),
                "chaos_uid": injection_result.fault_id,
                "command": injection_result.command,
                "output": injection_result.output,
                "error_message": injection_result.error,
                "status": "success" if injection_result.success else "failed"
            }
            
            await execution_service.update_execution(execution_id, update_data)
            
            # 如果注入成功且设置了自动销毁，调度销毁任务
            if injection_result.success and task.auto_destroy and task.max_duration:
                destroy_task_delay = task.max_duration
                destroy_single_chaos_fault.apply_async(
                    args=[injection_result.fault_id, execution_id],
                    countdown=destroy_task_delay
                )
                logger.info(f"已调度故障销毁任务，延迟: {destroy_task_delay}秒")
            
            result = {
                "task_id": task_id,
                "execution_id": execution_id,
                "success": injection_result.success,
                "fault_id": injection_result.fault_id,
                "message": injection_result.message,
                "start_time": execution_data["start_time"].isoformat(),
                "end_time": update_data["end_time"].isoformat()
            }
            
            logger.info(f"混沌测试任务执行完成: {task_id}, 结果: {injection_result.success}")
            return result
            
    except Exception as e:
        logger.error(f"执行混沌测试任务异常: {task_id}, {str(e)}")
        
        # 更新执行记录为失败状态
        if execution_id:
            try:
                async for db in get_db():
                    execution_service = ChaosExecutionService(db)
                    await execution_service.update_execution(execution_id, {
                        "status": "failed",
                        "error_message": str(e),
                        "end_time": datetime.now()
                    })
                    break
            except Exception as update_error:
                logger.error(f"更新执行记录失败: {update_error}")
        
        # 重试逻辑
        if task_instance.request.retries < task_instance.max_retries:
            logger.info(f"任务执行失败，准备重试: {task_id}")
            raise task_instance.retry(countdown=60, exc=e)
        
        raise e


@celery_app.task(bind=True, base=CallbackTask, max_retries=3)
def execute_batch_chaos_task(self, batch_task_id: int, execution_config: Dict[str, Any]):
    """
    执行批次混沌测试任务
    
    Args:
        batch_task_id: 批次任务ID
        execution_config: 执行配置
    """
    return asyncio.run(_execute_batch_chaos_task_async(self, batch_task_id, execution_config))


async def _execute_batch_chaos_task_async(task_instance, batch_task_id: int, execution_config: Dict[str, Any]):
    """异步执行批次混沌测试任务"""
    try:
        logger.info(f"开始执行批次混沌测试任务: {batch_task_id}")
        
        async for db in get_db():
            # 获取批次任务服务
            from app.services.chaos.batch_task_service import ChaosBatchTaskService
            batch_service = ChaosBatchTaskService(db)
            execution_service = ChaosExecutionService(db)
            
            # 获取批次任务详情
            batch_task = await batch_service.get_batch_task_by_id(batch_task_id)
            if not batch_task:
                raise ValueError(f"批次任务不存在: {batch_task_id}")
            
            # 获取子任务列表
            task_items = await batch_service.get_batch_task_items(batch_task_id)
            if not task_items:
                raise ValueError(f"批次任务没有子任务: {batch_task_id}")
            
            # 构建故障配置列表
            fault_configs = []
            for item in task_items:
                fault_config = FaultConfig(
                    fault_type=item.fault_type,
                    fault_params=item.fault_params,
                    host_info=execution_config.get("host_info"),
                    max_duration=item.max_duration,
                    auto_destroy=item.auto_destroy,
                    batch_task_id=batch_task_id
                )
                fault_configs.append(fault_config)
            
            # 根据执行模式执行批次注入
            execution_mode = "sequential" if batch_task.batch_execution_mode == "order" else "parallel"
            batch_result = await fault_injector.inject_batch(fault_configs, execution_mode)
            
            # 创建执行记录
            for i, result in enumerate(batch_result.results):
                execution_data = {
                    "batch_task_id": batch_task_id,
                    "batch_task_item_id": task_items[i].id,
                    "host_id": execution_config.get("host_id"),
                    "host_info": execution_config.get("host_info"),
                    "fault_config": {
                        "fault_type": task_items[i].fault_type,
                        "fault_params": task_items[i].fault_params
                    },
                    "status": "success" if result.success else "failed",
                    "start_time": result.start_time or datetime.now(),
                    "end_time": datetime.now(),
                    "chaos_uid": result.fault_id,
                    "command": result.command,
                    "output": result.output,
                    "error_message": result.error
                }
                
                await execution_service.create_execution(execution_data)
            
            result = {
                "batch_task_id": batch_task_id,
                "total_count": batch_result.total_count,
                "success_count": batch_result.success_count,
                "failed_count": batch_result.failed_count,
                "success_rate": batch_result.success_rate,
                "batch_id": batch_result.batch_id,
                "start_time": batch_result.start_time.isoformat(),
                "end_time": batch_result.end_time.isoformat() if batch_result.end_time else None
            }
            
            logger.info(f"批次混沌测试任务执行完成: {batch_task_id}, 成功率: {batch_result.success_rate:.2%}")
            return result
            
    except Exception as e:
        logger.error(f"执行批次混沌测试任务异常: {batch_task_id}, {str(e)}")
        
        # 重试逻辑
        if task_instance.request.retries < task_instance.max_retries:
            logger.info(f"批次任务执行失败，准备重试: {batch_task_id}")
            raise task_instance.retry(countdown=60, exc=e)
        
        raise e


@celery_app.task(bind=True, base=CallbackTask)
def destroy_single_chaos_fault(self, fault_id: str, execution_id: Optional[int] = None):
    """
    销毁单个混沌故障
    
    Args:
        fault_id: 故障ID
        execution_id: 执行记录ID（可选）
    """
    return asyncio.run(_destroy_single_chaos_fault_async(fault_id, execution_id))


async def _destroy_single_chaos_fault_async(fault_id: str, execution_id: Optional[int] = None):
    """异步销毁单个混沌故障"""
    try:
        logger.info(f"开始销毁混沌故障: {fault_id}")
        
        # 执行故障销毁
        destroy_result = await fault_injector.destroy_fault(fault_id)
        
        # 更新执行记录
        if execution_id:
            async for db in get_db():
                execution_service = ChaosExecutionService(db)
                await execution_service.update_execution(execution_id, {
                    "is_auto_destroyed": True,
                    "destroy_time": datetime.now(),
                    "destroy_output": destroy_result.output
                })
                break
        
        result = {
            "fault_id": fault_id,
            "success": destroy_result.success,
            "message": destroy_result.message,
            "destroy_time": datetime.now().isoformat()
        }
        
        logger.info(f"混沌故障销毁完成: {fault_id}, 结果: {destroy_result.success}")
        return result
        
    except Exception as e:
        logger.error(f"销毁混沌故障异常: {fault_id}, {str(e)}")
        raise e
