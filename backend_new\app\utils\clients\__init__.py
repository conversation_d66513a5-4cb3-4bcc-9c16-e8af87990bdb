# 连接客户端统一导出
from .base_client import BaseClient
from .ssh_client import SSHClient

# 数据库客户端
try:
    from .mysql_client import MySQLClient
except ImportError:
    MySQLClient = None

try:
    from .postgresql_client import PostgreSQLClient
except ImportError:
    PostgreSQLClient = None

try:
    from .mongodb_client import MongoDBClient
except ImportError:
    MongoDBClient = None

# 缓存和消息队列客户端
try:
    from .redis_client import RedisClient
except ImportError:
    RedisClient = None

try:
    from .kafka_client import KafkaClient
except ImportError:
    KafkaClient = None

__all__ = [
    "BaseClient",
    "SSHClient",
    "MySQLClient",
    "PostgreSQLClient",
    "MongoDBClient",
    "RedisClient",
    "KafkaClient"
]
