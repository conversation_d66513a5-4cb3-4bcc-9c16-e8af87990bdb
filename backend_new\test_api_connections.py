#!/usr/bin/env python3
"""
通过API测试环境连接
使用HTTP请求调用后端API进行连接测试
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any


class EnvironmentTester:
    """环境连接测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", token: str = None):
        self.base_url = base_url
        self.token = token
        self.headers = {
            "Content-Type": "application/json"
        }
        if token:
            self.headers["Authorization"] = f"Bearer {token}"
    
    async def login(self, username: str, password: str) -> str:
        """登录获取token"""
        login_data = {
            "username": username,
            "password": password
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/auth/login",
                json=login_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    token = result.get("access_token")
                    if token:
                        self.token = token
                        self.headers["Authorization"] = f"Bearer {token}"
                        print(f"✅ 登录成功，获取到token")
                        return token
                    else:
                        print(f"❌ 登录失败：未获取到token")
                        return None
                else:
                    error_text = await response.text()
                    print(f"❌ 登录失败：{response.status} - {error_text}")
                    return None
    
    async def test_connection(self, test_config: Dict[str, Any]) -> Dict[str, Any]:
        """测试连接"""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/env/test",
                json=test_config,
                headers=self.headers
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "message": f"API调用失败: {response.status} - {error_text}",
                        "duration": 0,
                        "details": {"api_error": True}
                    }
    
    async def get_supported_types(self) -> list:
        """获取支持的环境类型"""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/env/supported-types",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return []


async def test_various_connections():
    """测试各种类型的连接"""
    tester = EnvironmentTester()
    
    # 如果需要认证，先登录
    # await tester.login("admin", "admin123")
    
    print("🔍 获取支持的环境类型...")
    supported_types = await tester.get_supported_types()
    if supported_types:
        print("📋 支持的环境类型:")
        for env_type in supported_types:
            print(f"  - {env_type.get('type')}: {env_type.get('description')}")
    
    print("\n" + "=" * 60)
    
    # 测试配置列表
    test_configs = [
        {
            "name": "HTTP连接测试",
            "config": {
                "type": "http",
                "config": {
                    "host": "httpbin.org",
                    "port": 80,
                    "protocol": "http",
                    "path": "/get"
                },
                "timeout": 10
            }
        },
        {
            "name": "HTTPS连接测试",
            "config": {
                "type": "https",
                "config": {
                    "host": "httpbin.org",
                    "port": 443,
                    "protocol": "https",
                    "path": "/get"
                },
                "timeout": 10
            }
        },
        {
            "name": "TCP连接测试 - 百度",
            "config": {
                "type": "tcp",
                "config": {
                    "host": "www.baidu.com",
                    "port": 80
                },
                "timeout": 5
            }
        },
        {
            "name": "TCP连接测试 - Google DNS",
            "config": {
                "type": "tcp",
                "config": {
                    "host": "*******",
                    "port": 53
                },
                "timeout": 5
            }
        },
        {
            "name": "SSH连接测试 - 本地",
            "config": {
                "type": "ssh",
                "config": {
                    "host": "127.0.0.1",
                    "port": 22,
                    "username": "test",
                    "password": "test"
                },
                "timeout": 5
            }
        },
        {
            "name": "MySQL连接测试",
            "config": {
                "type": "mysql",
                "config": {
                    "host": "127.0.0.1",
                    "port": 3306,
                    "username": "root",
                    "password": "password"
                },
                "timeout": 5
            }
        },
        {
            "name": "Redis连接测试",
            "config": {
                "type": "redis",
                "config": {
                    "host": "127.0.0.1",
                    "port": 6379
                },
                "timeout": 5
            }
        }
    ]
    
    # 执行测试
    for test_case in test_configs:
        print(f"\n🧪 {test_case['name']}")
        print("-" * 40)
        
        try:
            result = await tester.test_connection(test_case['config'])
            
            success_icon = "✅" if result.get('success') else "❌"
            print(f"{success_icon} 结果: {result.get('success')}")
            print(f"📝 消息: {result.get('message')}")
            print(f"⏱️  耗时: {result.get('duration', 0):.2f}秒")
            
            details = result.get('details', {})
            if details:
                print(f"📊 详情:")
                for key, value in details.items():
                    print(f"   {key}: {value}")
                    
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")


async def interactive_test():
    """交互式测试"""
    tester = EnvironmentTester()
    
    print("🎯 交互式环境连接测试")
    print("=" * 40)
    
    while True:
        print("\n请选择测试类型:")
        print("1. HTTP/HTTPS连接")
        print("2. SSH连接")
        print("3. 数据库连接")
        print("4. Redis连接")
        print("5. 自定义TCP连接")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            host = input("请输入主机地址 (默认: httpbin.org): ").strip() or "httpbin.org"
            port = input("请输入端口 (默认: 80): ").strip() or "80"
            protocol = input("请输入协议 (http/https, 默认: http): ").strip() or "http"
            path = input("请输入路径 (默认: /get): ").strip() or "/get"
            
            config = {
                "type": protocol,
                "config": {
                    "host": host,
                    "port": int(port),
                    "protocol": protocol,
                    "path": path
                },
                "timeout": 10
            }
        elif choice == "2":
            host = input("请输入SSH主机地址: ").strip()
            port = input("请输入SSH端口 (默认: 22): ").strip() or "22"
            username = input("请输入用户名: ").strip()
            password = input("请输入密码: ").strip()
            
            config = {
                "type": "ssh",
                "config": {
                    "host": host,
                    "port": int(port),
                    "username": username,
                    "password": password
                },
                "timeout": 10
            }
        elif choice == "3":
            host = input("请输入数据库主机地址: ").strip()
            port = input("请输入数据库端口 (默认: 3306): ").strip() or "3306"
            db_type = input("请输入数据库类型 (mysql/postgresql, 默认: mysql): ").strip() or "mysql"
            
            config = {
                "type": db_type,
                "config": {
                    "host": host,
                    "port": int(port)
                },
                "timeout": 10
            }
        elif choice == "4":
            host = input("请输入Redis主机地址: ").strip()
            port = input("请输入Redis端口 (默认: 6379): ").strip() or "6379"
            
            config = {
                "type": "redis",
                "config": {
                    "host": host,
                    "port": int(port)
                },
                "timeout": 10
            }
        elif choice == "5":
            host = input("请输入主机地址: ").strip()
            port = input("请输入端口: ").strip()
            
            config = {
                "type": "tcp",
                "config": {
                    "host": host,
                    "port": int(port)
                },
                "timeout": 10
            }
        else:
            print("❌ 无效选择，请重新输入")
            continue
        
        print(f"\n🧪 正在测试连接...")
        try:
            result = await tester.test_connection(config)
            
            success_icon = "✅" if result.get('success') else "❌"
            print(f"{success_icon} 结果: {result.get('success')}")
            print(f"📝 消息: {result.get('message')}")
            print(f"⏱️  耗时: {result.get('duration', 0):.2f}秒")
            
            details = result.get('details', {})
            if details:
                print(f"📊 详情: {json.dumps(details, indent=2, ensure_ascii=False)}")
                
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")


async def main():
    """主函数"""
    print("🚀 环境连接API测试工具")
    print("💡 确保后端服务已启动 (http://localhost:8000)")
    print()
    
    mode = input("请选择模式:\n1. 自动测试\n2. 交互式测试\n请输入选择 (1-2): ").strip()
    
    if mode == "1":
        await test_various_connections()
    elif mode == "2":
        await interactive_test()
    else:
        print("❌ 无效选择")
        return
    
    print("\n🏁 测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
