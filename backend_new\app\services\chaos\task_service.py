"""
混沌测试任务服务
"""
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload

from app.models.chaos_task import ChaosTask
from app.models.chaos_execution import ChaosExecution
from app.schemas.chaos.task_schemas import (
    ChaosTaskCreate, ChaosTaskUpdate, ChaosTaskResponse, 
    ChaosTaskQuery, ChaosTaskPageResponse, ChaosTaskStatsResponse
)
from app.services.base import BaseService
from app.core.exceptions import raise_not_found_error, raise_conflict_error
from app.tasks.chaos.schedule_tasks import schedule_chaos_task

logger = logging.getLogger(__name__)


class ChaosTaskService(BaseService):
    """混沌测试任务服务"""

    def __init__(self, db: AsyncSession, redis_client=None):
        super().__init__(db, redis_client)

    async def create_task(self, task_data: ChaosTaskCreate) -> ChaosTaskResponse:
        """创建混沌测试任务"""
        # 检查任务名称是否重复
        existing_task = await self._get_task_by_name(task_data.name)
        if existing_task:
            raise_conflict_error(f"任务名称 '{task_data.name}' 已存在")

        # 创建任务
        task_dict = task_data.model_dump()
        task = ChaosTask(**task_dict)
        
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)

        response = self._convert_to_response(task)
        await self._resolve_single_user_names(response)
        return response

    async def get_task_by_id(self, task_id: int) -> Optional[ChaosTaskResponse]:
        """根据ID获取任务"""
        task = await self._get_task_by_id(task_id)
        if not task:
            return None

        response = self._convert_to_response(task)
        await self._resolve_single_user_names(response)
        return response

    async def update_task(self, task_id: int, task_data: ChaosTaskUpdate) -> ChaosTaskResponse:
        """更新混沌测试任务"""
        task = await self._get_task_by_id(task_id)
        if not task:
            raise_not_found_error("任务不存在")

        # 检查名称冲突
        if task_data.name and task_data.name != task.name:
            existing_task = await self._get_task_by_name(task_data.name)
            if existing_task:
                raise_conflict_error(f"任务名称 '{task_data.name}' 已存在")

        # 更新字段
        update_dict = task_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            if hasattr(task, field):
                setattr(task, field, value)

        await self.db.commit()
        await self.db.refresh(task)

        response = self._convert_to_response(task)
        await self._resolve_single_user_names(response)
        return response

    async def delete_task(self, task_id: int) -> bool:
        """删除混沌测试任务"""
        task = await self._get_task_by_id(task_id)
        if not task:
            raise_not_found_error("任务不存在")

        # 检查是否有运行中的执行记录
        running_executions = await self._get_running_executions_by_task(task_id)
        if running_executions:
            raise_conflict_error("任务有运行中的执行记录，无法删除")

        await self.db.delete(task)
        await self.db.commit()
        return True

    async def list_tasks(self, query: ChaosTaskQuery) -> ChaosTaskPageResponse:
        """查询任务列表"""
        # 构建查询
        stmt = select(ChaosTask)

        # 添加筛选条件
        if query.keyword:
            stmt = stmt.where(
                or_(
                    ChaosTask.name.ilike(f"%{query.keyword}%"),
                    ChaosTask.description.ilike(f"%{query.keyword}%")
                )
            )

        if query.fault_type:
            stmt = stmt.where(ChaosTask.fault_type == query.fault_type)

        if query.execution_type:
            stmt = stmt.where(ChaosTask.execution_type == query.execution_type)

        if query.task_status:
            stmt = stmt.where(ChaosTask.task_status == query.task_status)

        # 计算总数
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()

        # 分页查询
        stmt = stmt.offset(query.offset).limit(query.size).order_by(ChaosTask.created_at.desc())
        result = await self.db.execute(stmt)
        tasks = result.scalars().all()

        # 转换响应
        task_responses = [self._convert_to_response(task) for task in tasks]
        await self._resolve_user_names(task_responses)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return ChaosTaskPageResponse(
            items=task_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def execute_task(self, task_id: int, execution_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行混沌测试任务"""
        task = await self._get_task_by_id(task_id)
        if not task:
            raise_not_found_error("任务不存在")

        if not task.can_execute():
            raise_conflict_error("任务当前无法执行")

        # 构建执行配置
        config = execution_config or {}
        config.update({
            "task_id": task_id,
            "env_ids": task.env_ids,
            "execution_time": datetime.now().isoformat()
        })

        # 提交到Celery队列
        celery_result = schedule_chaos_task.delay(task_id, config)

        # 更新任务执行统计
        task.last_execution_time = datetime.now()
        task.execution_count = (task.execution_count or 0) + 1
        await self.db.commit()

        return {
            "task_id": task_id,
            "celery_task_id": celery_result.id,
            "status": "submitted",
            "message": "任务已提交到执行队列"
        }

    async def get_task_stats(self) -> ChaosTaskStatsResponse:
        """获取任务统计信息"""
        # 基础统计
        total_tasks_result = await self.db.execute(select(func.count(ChaosTask.id)))
        total_tasks = total_tasks_result.scalar()

        enabled_tasks_result = await self.db.execute(
            select(func.count(ChaosTask.id)).where(ChaosTask.task_status == "enabled")
        )
        enabled_tasks = enabled_tasks_result.scalar()

        disabled_tasks = total_tasks - enabled_tasks

        # 运行中执行统计
        running_executions_result = await self.db.execute(
            select(func.count(ChaosExecution.id)).where(
                ChaosExecution.status.in_(["pending", "running"])
            )
        )
        running_executions = running_executions_result.scalar()

        # 今日执行统计
        today = datetime.now().date()
        today_executions_result = await self.db.execute(
            select(func.count(ChaosExecution.id)).where(
                func.date(ChaosExecution.start_time) == today
            )
        )
        today_executions = today_executions_result.scalar()

        # 故障类型统计
        fault_type_stats_result = await self.db.execute(
            select(ChaosTask.fault_type, func.count(ChaosTask.id))
            .group_by(ChaosTask.fault_type)
        )
        fault_type_stats = dict(fault_type_stats_result.all())

        # 执行类型统计
        execution_type_stats_result = await self.db.execute(
            select(ChaosTask.execution_type, func.count(ChaosTask.id))
            .group_by(ChaosTask.execution_type)
        )
        execution_type_stats = dict(execution_type_stats_result.all())

        return ChaosTaskStatsResponse(
            total_tasks=total_tasks,
            enabled_tasks=enabled_tasks,
            disabled_tasks=disabled_tasks,
            running_executions=running_executions,
            today_executions=today_executions,
            fault_type_stats=fault_type_stats,
            execution_type_stats=execution_type_stats
        )

    async def get_scheduled_tasks(self, current_time: datetime) -> List[ChaosTask]:
        """获取到期的调度任务"""
        stmt = select(ChaosTask).where(
            and_(
                ChaosTask.task_status == "enabled",
                ChaosTask.execution_type == "scheduled",
                ChaosTask.scheduled_time <= current_time
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    # 私有方法
    async def _get_task_by_id(self, task_id: int) -> Optional[ChaosTask]:
        """根据ID获取任务"""
        result = await self.db.execute(select(ChaosTask).where(ChaosTask.id == task_id))
        return result.scalar_one_or_none()

    async def _get_task_by_name(self, name: str) -> Optional[ChaosTask]:
        """根据名称获取任务"""
        result = await self.db.execute(select(ChaosTask).where(ChaosTask.name == name))
        return result.scalar_one_or_none()

    async def _get_running_executions_by_task(self, task_id: int) -> List[ChaosExecution]:
        """获取任务的运行中执行记录"""
        stmt = select(ChaosExecution).where(
            and_(
                ChaosExecution.task_id == task_id,
                ChaosExecution.status.in_(["pending", "running"])
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    def _convert_to_response(self, task: ChaosTask) -> ChaosTaskResponse:
        """转换为响应对象"""
        return ChaosTaskResponse(
            id=task.id,
            name=task.name,
            description=task.description,
            env_ids=task.env_ids,
            fault_type=task.fault_type,
            fault_params=task.fault_params,
            execution_type=task.execution_type,
            scheduled_time=task.scheduled_time,
            periodic_config=task.periodic_config,
            cron_expression=task.cron_expression,
            auto_destroy=task.auto_destroy,
            max_duration=task.max_duration,
            task_status=task.task_status,
            last_execution_time=task.last_execution_time,
            execution_count=task.execution_count,
            monitor_config=task.monitor_config,
            created_at=task.created_at,
            updated_at=task.updated_at,
            created_by=task.created_by,
            updated_by=task.updated_by,
            created_by_name=None,
            updated_by_name=None
        )
