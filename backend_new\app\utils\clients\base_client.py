"""
连接客户端基类
定义统一的连接管理接口
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class ConnectionResult:
    """
    连接结果数据类
    统一的连接结果格式
    """
    success: bool
    message: str
    duration: float = 0.0
    details: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}


class BaseClient(ABC):
    """
    连接客户端基类
    定义统一的连接管理接口
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化客户端
        
        Args:
            config: 连接配置
        """
        self.config = config
        self.connection = None
        self.is_connected = False

    @abstractmethod
    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        pass

    @abstractmethod
    async def disconnect(self) -> None:
        """断开连接"""
        pass

    @abstractmethod
    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        pass

    def __enter__(self):
        """同步上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """同步上下文管理器出口"""
        pass

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
