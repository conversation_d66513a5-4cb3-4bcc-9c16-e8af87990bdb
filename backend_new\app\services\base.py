"""
基础服务类
提供通用的服务功能，包括用户昵称解析
"""
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.redis import RedisClient
from app.utils.user_cache import UserCacheService


class BaseService:
    """基础服务类"""
    
    def __init__(self, db: AsyncSession, redis_client: RedisClient = None):
        self.db = db
        self.redis_client = redis_client
        self._user_cache_service = None
    
    @property
    def user_cache_service(self) -> UserCacheService:
        """获取用户缓存服务实例"""
        if self._user_cache_service is None:
            self._user_cache_service = UserCacheService(self.db, self.redis_client)
        return self._user_cache_service
    
    async def _resolve_user_names(self, items: List[Any]) -> List[Any]:
        """
        批量解析用户昵称
        
        Args:
            items: 包含created_by和updated_by字段的对象列表
            
        Returns:
            解析后的对象列表
        """
        if not items:
            return items
        
        # 收集所有需要解析的用户ID
        user_ids = set()
        for item in items:
            if hasattr(item, 'created_by') and item.created_by:
                user_ids.add(item.created_by)
            if hasattr(item, 'updated_by') and item.updated_by:
                user_ids.add(item.updated_by)
        
        if not user_ids:
            return items
        
        # 批量获取用户信息
        users_info = await self.user_cache_service.get_users_info_batch(list(user_ids))
        
        # 为每个对象设置用户昵称
        for item in items:
            if hasattr(item, 'created_by') and item.created_by:
                user_info = users_info.get(item.created_by)
                if user_info:
                    item.created_by_name = user_info.get('nickname')
            
            if hasattr(item, 'updated_by') and item.updated_by:
                user_info = users_info.get(item.updated_by)
                if user_info:
                    item.updated_by_name = user_info.get('nickname')
        
        return items
    
    async def _resolve_single_user_names(self, item: Any) -> Any:
        """
        解析单个对象的用户昵称
        
        Args:
            item: 包含created_by和updated_by字段的对象
            
        Returns:
            解析后的对象
        """
        if not item:
            return item
        
        # 收集需要解析的用户ID
        user_ids = []
        if hasattr(item, 'created_by') and item.created_by:
            user_ids.append(item.created_by)
        if hasattr(item, 'updated_by') and item.updated_by:
            user_ids.append(item.updated_by)
        
        if not user_ids:
            return item
        
        # 批量获取用户信息（即使只有一个或两个用户）
        users_info = await self.user_cache_service.get_users_info_batch(user_ids)
        
        # 设置用户昵称
        if hasattr(item, 'created_by') and item.created_by:
            user_info = users_info.get(item.created_by)
            if user_info:
                item.created_by_name = user_info.get('nickname')
        
        if hasattr(item, 'updated_by') and item.updated_by:
            user_info = users_info.get(item.updated_by)
            if user_info:
                item.updated_by_name = user_info.get('nickname')
        
        return item
