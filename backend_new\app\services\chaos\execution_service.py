"""
混沌测试执行记录服务
"""
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload

from app.models.chaos_execution import ChaosExecution
from app.schemas.chaos.execution_schemas import (
    ChaosExecutionCreate, ChaosExecutionUpdate, ChaosExecutionResponse,
    ChaosExecutionQuery, ChaosExecutionPageResponse
)
from app.services.base import BaseService
from app.core.exceptions import raise_not_found_error

logger = logging.getLogger(__name__)


class ChaosExecutionService(BaseService):
    """混沌测试执行记录服务"""

    def __init__(self, db: AsyncSession, redis_client=None):
        super().__init__(db, redis_client)

    async def create_execution(self, execution_data: Dict[str, Any]) -> ChaosExecutionResponse:
        """创建执行记录"""
        execution = ChaosExecution(**execution_data)
        
        self.db.add(execution)
        await self.db.commit()
        await self.db.refresh(execution)

        response = self._convert_to_response(execution)
        await self._resolve_single_user_names(response)
        return response

    async def get_execution_by_id(self, execution_id: int) -> Optional[ChaosExecutionResponse]:
        """根据ID获取执行记录"""
        execution = await self._get_execution_by_id(execution_id)
        if not execution:
            return None

        response = self._convert_to_response(execution)
        await self._resolve_single_user_names(response)
        return response

    async def update_execution(self, execution_id: int, update_data: Dict[str, Any]) -> ChaosExecutionResponse:
        """更新执行记录"""
        execution = await self._get_execution_by_id(execution_id)
        if not execution:
            raise_not_found_error("执行记录不存在")

        # 更新字段
        for field, value in update_data.items():
            if hasattr(execution, field):
                setattr(execution, field, value)

        await self.db.commit()
        await self.db.refresh(execution)

        response = self._convert_to_response(execution)
        await self._resolve_single_user_names(response)
        return response

    async def delete_execution(self, execution_id: int) -> bool:
        """删除执行记录"""
        execution = await self._get_execution_by_id(execution_id)
        if not execution:
            raise_not_found_error("执行记录不存在")

        await self.db.delete(execution)
        await self.db.commit()
        return True

    async def get_running_executions(self) -> List[ChaosExecution]:
        """获取运行中的执行记录"""
        stmt = select(ChaosExecution).where(
            ChaosExecution.status.in_(["pending", "running"])
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_expired_running_executions(self, current_time: datetime) -> List[ChaosExecution]:
        """获取过期的运行中执行记录"""
        # 运行超过24小时的任务视为过期
        expire_time = current_time - timedelta(hours=24)
        
        stmt = select(ChaosExecution).where(
            and_(
                ChaosExecution.status.in_(["pending", "running"]),
                ChaosExecution.start_time < expire_time
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_old_completed_executions(self, cutoff_time: datetime) -> List[ChaosExecution]:
        """获取需要清理的已完成执行记录"""
        stmt = select(ChaosExecution).where(
            and_(
                ChaosExecution.status.in_(["success", "failed", "timeout", "destroyed"]),
                ChaosExecution.end_time < cutoff_time
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_execution_by_chaos_uid(self, chaos_uid: str) -> Optional[ChaosExecution]:
        """根据ChaosBlade UID获取执行记录"""
        stmt = select(ChaosExecution).where(ChaosExecution.chaos_uid == chaos_uid)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def list_executions(self, query: ChaosExecutionQuery) -> ChaosExecutionPageResponse:
        """查询执行记录列表"""
        # 构建查询
        stmt = select(ChaosExecution).options(
            selectinload(ChaosExecution.task),
            selectinload(ChaosExecution.batch_task),
            selectinload(ChaosExecution.batch_task_item)
        )

        # 添加筛选条件
        if query.task_id:
            stmt = stmt.where(ChaosExecution.task_id == query.task_id)

        if query.batch_task_id:
            stmt = stmt.where(ChaosExecution.batch_task_id == query.batch_task_id)

        if query.status:
            stmt = stmt.where(ChaosExecution.status == query.status)

        if query.chaos_uid:
            stmt = stmt.where(ChaosExecution.chaos_uid.ilike(f"%{query.chaos_uid}%"))

        if query.start_date:
            stmt = stmt.where(ChaosExecution.start_time >= query.start_date)

        if query.end_date:
            stmt = stmt.where(ChaosExecution.start_time <= query.end_date)

        # 计算总数
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()

        # 分页查询
        stmt = stmt.offset(query.offset).limit(query.size).order_by(ChaosExecution.start_time.desc())
        result = await self.db.execute(stmt)
        executions = result.scalars().all()

        # 转换响应
        execution_responses = [self._convert_to_response(execution) for execution in executions]
        await self._resolve_user_names(execution_responses)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return ChaosExecutionPageResponse(
            items=execution_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    # 私有方法
    async def _get_execution_by_id(self, execution_id: int) -> Optional[ChaosExecution]:
        """根据ID获取执行记录"""
        stmt = select(ChaosExecution).options(
            selectinload(ChaosExecution.task),
            selectinload(ChaosExecution.batch_task),
            selectinload(ChaosExecution.batch_task_item)
        ).where(ChaosExecution.id == execution_id)
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    def _convert_to_response(self, execution: ChaosExecution) -> ChaosExecutionResponse:
        """转换为响应对象"""
        return ChaosExecutionResponse(
            id=execution.id,
            task_id=execution.task_id,
            batch_task_id=execution.batch_task_id,
            batch_task_item_id=execution.batch_task_item_id,
            host_id=execution.host_id,
            host_info=execution.host_info,
            fault_config=execution.fault_config,
            status=execution.status,
            start_time=execution.start_time,
            end_time=execution.end_time,
            destroy_time=execution.destroy_time,
            chaos_uid=execution.chaos_uid,
            command=execution.command,
            output=execution.output,
            error_message=execution.error_message,
            is_auto_destroyed=execution.is_auto_destroyed,
            destroy_output=execution.destroy_output,
            celery_task_id=execution.celery_task_id,
            created_at=execution.created_at,
            updated_at=execution.updated_at,
            created_by=execution.created_by,
            updated_by=execution.updated_by,
            created_by_name=None,
            updated_by_name=None
        )
