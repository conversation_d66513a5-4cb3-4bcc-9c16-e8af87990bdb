"""
混沌测试批次任务API路由
"""
from typing import List
from fastapi import APIRouter, Depends, Query

from app.api.deps import get_current_user, get_current_superuser
from app.core.dependencies import get_chaos_batch_task_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaos_batch_task_service import ChaosBatchTaskService
from app.schemas.chaos.chaos_batch_task import (
    ChaosBatchTaskCreate, ChaosBatchTaskUpdate, ChaosBatchTaskResponse,
    ChaosBatchTaskExecuteRequest, ChaosBatchTaskStatusRequest,
    ChaosBatchTaskQuery, ChaosBatchTaskPageResponse, ChaosBatchTaskStatistics
)

router = APIRouter()


@router.get("", response_model=ChaosBatchTaskPageResponse, summary="查询批次任务列表")
async def list_batch_tasks(
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    query: ChaosBatchTaskQuery = Depends(),  # 自动接收并校验查询参数
    _current_user: User = Depends(get_current_user)
):
    """查询批次任务列表，支持关键词搜索、状态筛选和分页"""
    result = await service.list_batch_tasks(query)
    return response_builder.success(result)


@router.get("/{task_id}", response_model=ChaosBatchTaskResponse, summary="获取批次任务详情")
async def get_batch_task(
    task_id: int,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    _current_user: User = Depends(get_current_user)
):
    """获取指定ID的批次任务详情"""
    task = await service.get_batch_task_detail(task_id)
    return response_builder.success(task)


@router.post("", response_model=ChaosBatchTaskResponse, status_code=201, summary="创建批次任务")
async def create_batch_task(
    task_data: ChaosBatchTaskCreate,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """创建新的批次任务，需要用户权限"""
    task = await service.create_batch_task(task_data, current_user.id)
    return response_builder.created(task)


@router.put("/{task_id}", response_model=ChaosBatchTaskResponse, summary="更新批次任务")
async def update_batch_task(
    task_id: int,
    task_data: ChaosBatchTaskUpdate,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """更新批次任务信息，支持修改基本信息和子任务配置"""
    task = await service.update_batch_task(task_id, task_data, current_user.id)
    return response_builder.success(task)


@router.delete("/{task_id}", status_code=204, summary="删除批次任务")
async def delete_batch_task(
    task_id: int,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """删除批次任务，只能删除非运行状态的任务"""
    await service.delete_batch_task(task_id, current_user.id)
    # HTTP 204 No Content - 删除成功，无响应体


@router.post("/{task_id}/execute", response_model=dict, summary="执行批次任务")
async def execute_batch_task(
    task_id: int,
    request: ChaosBatchTaskExecuteRequest,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """执行批次任务，按照配置的执行模式间隔或连续执行所有子任务"""
    result = await service.execute_batch_task(task_id, request, current_user.id)
    return response_builder.success(result)


@router.post("/{task_id}/stop", status_code=204, summary="停止批次任务")
async def stop_batch_task(
    task_id: int,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """停止批次任务执行，会停止所有正在运行的子任务"""
    await service.stop_batch_task(task_id, current_user.id)
    # HTTP 204 No Content - 停止成功，无响应体


@router.post("/{task_id}/reset", response_model=bool, summary="重置批次任务")
async def reset_batch_task(
    task_id: int,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """重置批次任务状态，将任务状态重置为pending，清除执行结果"""
    result = await service.reset_batch_task(task_id, current_user.id)
    return response_builder.success(result)


@router.post("/{task_id}/enable", response_model=bool, summary="启用批次任务")
async def enable_batch_task(
    task_id: int,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """启用批次任务，将任务状态设置为enabled"""
    result = await service.enable_batch_task(task_id, current_user.id)
    return response_builder.success(result)


@router.post("/{task_id}/disable", response_model=bool, summary="禁用批次任务")
async def disable_batch_task(
    task_id: int,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """禁用批次任务，将任务状态设置为disabled"""
    result = await service.disable_batch_task(task_id, current_user.id)
    return response_builder.success(result)


@router.patch("/{task_id}/status", response_model=ChaosBatchTaskResponse, summary="更新批次任务状态")
async def update_batch_task_status(
    task_id: int,
    request: ChaosBatchTaskStatusRequest,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """更新批次任务状态（启用/禁用）"""
    # 获取任务
    task = await service.get_by_id(task_id)
    
    # 更新状态
    update_data = ChaosBatchTaskUpdate(task_status=request.task_status)
    updated_task = await service.update_batch_task(task_id, update_data, current_user.id)
    
    return response_builder.success(updated_task)


@router.get("/statistics/overview", response_model=ChaosBatchTaskStatistics, summary="获取批次任务统计信息")
async def get_batch_task_statistics(
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    _current_user: User = Depends(get_current_user)
):
    """获取批次任务的统计信息，包括总数、状态分布、执行模式分布等"""
    stats = await service.get_batch_task_statistics()
    return response_builder.success(stats)


# ==================== 批量操作接口 ====================

@router.post("/batch/delete", response_model=dict, summary="批量删除批次任务")
async def batch_delete_tasks(
    task_ids: List[int],
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """批量删除批次任务"""
    results = {
        "success_count": 0,
        "failed_count": 0,
        "errors": []
    }
    
    for task_id in task_ids:
        try:
            await service.delete_batch_task(task_id, current_user.id)
            results["success_count"] += 1
        except Exception as e:
            results["failed_count"] += 1
            results["errors"].append(f"任务 {task_id}: {str(e)}")
    
    return response_builder.success(results)


@router.post("/batch/status", response_model=dict, summary="批量更新批次任务状态")
async def batch_update_task_status(
    task_ids: List[int],
    request: ChaosBatchTaskStatusRequest,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """批量更新批次任务状态（启用/禁用）"""
    results = {
        "success_count": 0,
        "failed_count": 0,
        "errors": []
    }
    
    update_data = ChaosBatchTaskUpdate(task_status=request.task_status)
    
    for task_id in task_ids:
        try:
            await service.update_batch_task(task_id, update_data, current_user.id)
            results["success_count"] += 1
        except Exception as e:
            results["failed_count"] += 1
            results["errors"].append(f"任务 {task_id}: {str(e)}")
    
    return response_builder.success(results)


@router.post("/batch/execute", response_model=dict, summary="批量执行批次任务")
async def batch_execute_tasks(
    task_ids: List[int],
    request: ChaosBatchTaskExecuteRequest,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """批量执行批次任务"""
    results = {
        "success_count": 0,
        "failed_count": 0,
        "errors": [],
        "execution_results": []
    }
    
    for task_id in task_ids:
        try:
            result = await service.execute_batch_task(task_id, request, current_user.id)
            results["success_count"] += 1
            results["execution_results"].append({
                "task_id": task_id,
                "result": result
            })
        except Exception as e:
            results["failed_count"] += 1
            results["errors"].append(f"任务 {task_id}: {str(e)}")
    
    return response_builder.success(results)


# ==================== 复制和导入导出接口 ====================

@router.post("/{task_id}/copy", response_model=ChaosBatchTaskResponse, summary="复制批次任务")
async def copy_batch_task(
    task_id: int,
    service: ChaosBatchTaskService = Depends(get_chaos_batch_task_service),
    current_user: User = Depends(get_current_user)
):
    """复制批次任务，创建一个新的相同配置的任务"""
    # 获取原任务
    original_task = await service.get_batch_task_detail(task_id)
    
    # 创建复制的任务数据
    copy_data = ChaosBatchTaskCreate(
        name=f"{original_task.name}_副本",
        description=f"复制自: {original_task.name}",
        env_id=original_task.env_id,
        batch_execution_mode=original_task.batch_execution_mode,
        execution_type="immediate",  # 复制的任务默认为立即执行
        auto_destroy=original_task.auto_destroy,
        max_duration=original_task.max_duration,
        task_items=[
            {
                "name": item.name,
                "description": item.description,
                "fault_type": item.fault_type,
                "fault_params": item.fault_params,
                "task_order": item.task_order,
                "auto_destroy": item.auto_destroy,
                "max_duration": item.max_duration
            }
            for item in original_task.task_items
        ]
    )
    
    # 创建新任务
    new_task = await service.create_batch_task(copy_data, current_user.id)
    return response_builder.created(new_task)
