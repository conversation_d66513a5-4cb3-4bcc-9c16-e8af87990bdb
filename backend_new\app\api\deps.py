"""
依赖注入模块
定义FastAPI的依赖注入函数
"""
from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import get_async_db
from app.db.redis import get_redis, RedisClient
from app.core.security import verify_token
from app.core.exceptions import raise_authentication_error
import logging

logger = logging.getLogger(__name__)

# HTTP Bearer认证方案
security = HTTPBearer()


async def get_db() -> AsyncSession:
    """
    获取数据库会话依赖
    """
    async for session in get_async_db():
        yield session


async def get_redis_client() -> RedisClient:
    """
    获取Redis客户端依赖
    """
    return await get_redis()


async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> int:
    """
    获取当前用户ID
    从JWT令牌中解析用户ID
    
    Args:
        credentials: HTTP认证凭据
        
    Returns:
        用户ID
        
    Raises:
        HTTPException: 认证失败时抛出
    """
    try:
        token = credentials.credentials
        user_id = verify_token(token)
        
        if user_id is None:
            raise_authentication_error("无效的认证令牌")
        
        return int(user_id)
        
    except ValueError:
        raise_authentication_error("无效的用户ID格式")
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        raise_authentication_error("认证失败")


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """
    获取当前用户信息

    Args:
        db: 数据库会话
        current_user_id: 当前用户ID

    Returns:
        用户模型实例

    Raises:
        HTTPException: 用户不存在时抛出
    """
    from app.crud.user import UserCRUD

    user_crud = UserCRUD(db)
    user = await user_crud.get(db, current_user_id)
    if not user:
        raise_authentication_error("用户不存在")
    return user


async def get_current_active_user(
    current_user = Depends(get_current_user)
):
    """
    获取当前活跃用户
    检查用户是否处于活跃状态

    Args:
        current_user: 当前用户

    Returns:
        活跃用户模型实例

    Raises:
        HTTPException: 用户未激活时抛出
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户未激活"
        )
    return current_user


async def get_current_superuser(
    current_user = Depends(get_current_user)
):
    """
    获取当前超级用户
    检查用户是否具有超级管理员权限

    Args:
        current_user: 当前用户

    Returns:
        超级用户模型实例

    Raises:
        HTTPException: 用户不是超级管理员时抛出
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级管理员权限"
        )
    return current_user


class CommonQueryParams:
    """通用查询参数类"""
    
    def __init__(
        self,
        page: int = 1,
        size: int = 10,
        keyword: Optional[str] = None
    ):
        self.page = max(1, page)
        self.size = min(max(1, size), 100)  # 限制每页最多100条
        self.keyword = keyword
        
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size


def get_common_params(
    page: int = 1,
    size: int = 10,
    keyword: Optional[str] = None
) -> CommonQueryParams:
    """
    获取通用查询参数依赖
    
    Args:
        page: 页码
        size: 每页条数
        keyword: 搜索关键词
        
    Returns:
        通用查询参数对象
    """
    return CommonQueryParams(page=page, size=size, keyword=keyword)
