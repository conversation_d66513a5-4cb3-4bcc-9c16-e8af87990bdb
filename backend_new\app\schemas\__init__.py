"""
Schema模块
导入所有Pydantic模型
"""
from .base import (
    BaseSchema,
    BaseResponseSchema,
    PaginationSchema,
    PaginationResponseSchema,
    ApiResponse,
    ErrorResponse,
    ResponseBuilder,
    response_builder
)
from .user import (
    UserCreate,
    UserUpdate,
    UserResponse,
    UserPageResponse,
    UserQuery,
    LoginRequest,
    LoginResponse,
    TokenResponse,
    RefreshTokenRequest,
    RegisterRequest,
    RoleResponse
)

__all__ = [
    "BaseSchema",
    "BaseResponseSchema",
    "PaginationSchema",
    "PaginationResponseSchema",
    "ApiResponse",
    "ErrorResponse",
    "ResponseBuilder",
    "response_builder",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserPageResponse",
    "UserQuery",
    "LoginRequest",
    "LoginResponse",
    "TokenResponse",
    "RefreshTokenRequest",
    "RegisterRequest",
    "RoleResponse",
]
