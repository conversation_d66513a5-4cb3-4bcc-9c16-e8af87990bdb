"""
基础Schema模块
定义Pydantic模型的基础类和通用配置
"""
from datetime import datetime
from typing import Optional, Any, Dict
from pydantic import BaseModel, ConfigDict, Field


class BaseSchema(BaseModel):
    """基础Schema类，所有Pydantic模型都应继承此类"""
    
    model_config = ConfigDict(
        # 允许从ORM模型创建Pydantic模型
        from_attributes=True,
        # 验证赋值
        validate_assignment=True,
        # 使用枚举值而不是枚举名称
        use_enum_values=True,
        # 允许额外字段
        extra='forbid'
    )


class BaseResponseSchema(BaseSchema):
    """基础响应Schema，包含通用的响应字段"""

    id: int = Field(..., description="主键ID")
    created_at: Optional[datetime] = Field(default=None, description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    created_by: Optional[str] = Field(default=None, description="创建人ID")
    updated_by: Optional[str] = Field(default=None, description="更新人ID")
    created_by_name: Optional[str] = Field(default=None, description="创建人昵称")
    updated_by_name: Optional[str] = Field(default=None, description="更新人昵称")

class PaginationSchema(BaseSchema):
    """分页参数Schema"""
    
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页条数")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size


class PaginationResponseSchema(BaseSchema):
    """分页响应Schema"""
    
    total: int = Field(..., description="总条数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页条数")
    pages: int = Field(..., description="总页数")


class ApiResponse(BaseSchema):
    """统一API响应格式"""
    
    code: int = Field(200, description="响应状态码")
    message: str = Field("success", description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class ErrorResponse(BaseSchema):
    """错误响应格式"""
    
    code: int = Field(..., description="错误状态码")
    message: str = Field(..., description="错误消息")
    detail: Optional[str] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")


# 响应构建器类
class ResponseBuilder:
    """响应构建器，用于构建统一格式的API响应"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """构建成功响应"""
        return {
            "code": 200,
            "message": message,
            "data": data,       
              }
    
    @staticmethod
    def created(data: Any = None, message: str = "创建成功") -> Dict[str, Any]:
        """构建创建成功响应"""
        return {
            "code": 201,
            "message": message,
            "data": data
            }
    
    @staticmethod
    def error(
        code: int = 400, 
        message: str = "操作失败", 
        detail: Optional[str] = None
    ) -> Dict[str, Any]:
        """构建错误响应"""
        response = {
            "code": code,
            "message": message,
        }
        if detail:
            response["detail"] = detail
        return response
    
    @staticmethod
    def validation_error(detail: str) -> Dict[str, Any]:
        """构建验证错误响应"""
        return ResponseBuilder.error(422, "数据验证失败", detail)
    
    @staticmethod
    def not_found(message: str = "资源不存在") -> Dict[str, Any]:
        """构建资源不存在响应"""
        return ResponseBuilder.error(404, message)
    
    @staticmethod
    def unauthorized(message: str = "认证失败") -> Dict[str, Any]:
        """构建认证失败响应"""
        return ResponseBuilder.error(401, message)
    
    @staticmethod
    def forbidden(message: str = "权限不足") -> Dict[str, Any]:
        """构建权限不足响应"""
        return ResponseBuilder.error(403, message)


# 创建全局响应构建器实例
response_builder = ResponseBuilder()
