"""
故障处理器基类
定义故障注入和销毁的统一接口
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class FaultResult:
    """故障操作结果"""
    success: bool
    fault_id: str  # ChaosBlade UID或其他标识
    message: str
    output: Optional[str] = None
    error: Optional[str] = None
    command: Optional[str] = None


class BaseFaultHandler(ABC):
    """故障处理器基类"""
    
    def __init__(self):
        self.fault_type = self.__class__.__name__.replace('FaultHandler', '').lower()
    
    @abstractmethod
    async def inject(
        self, 
        ssh_client, 
        fault_params: Dict[str, Any],
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """
        注入故障
        
        Args:
            ssh_client: SSH客户端
            fault_params: 故障参数
            chaosblade_path: ChaosBlade可执行文件路径
            
        Returns:
            FaultResult: 故障注入结果
        """
        pass
    
    @abstractmethod
    async def destroy(
        self, 
        ssh_client, 
        fault_id: str,
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """
        销毁故障
        
        Args:
            ssh_client: SSH客户端
            fault_id: 故障ID (ChaosBlade UID)
            chaosblade_path: ChaosBlade可执行文件路径
            
        Returns:
            FaultResult: 故障销毁结果
        """
        pass
    
    @abstractmethod
    def validate_params(self, fault_params: Dict[str, Any]) -> tuple[bool, str]:
        """
        验证故障参数
        
        Args:
            fault_params: 故障参数
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        pass
    
    @abstractmethod
    def get_param_schema(self) -> Dict[str, Any]:
        """
        获取参数结构定义
        
        Returns:
            Dict: 参数结构定义
        """
        pass
    
    async def _execute_chaosblade_command(
        self, 
        ssh_client, 
        command: str,
        timeout: int = 30
    ) -> FaultResult:
        """
        执行ChaosBlade命令的通用方法
        
        Args:
            ssh_client: SSH客户端
            command: 要执行的命令
            timeout: 超时时间(秒)
            
        Returns:
            FaultResult: 执行结果
        """
        try:
            logger.info(f"执行ChaosBlade命令: {command}")
            
            result = await ssh_client.execute_command(command, timeout=timeout)
            
            if result.get("success", False) and result.get("exit_status", -1) == 0:
                output = result.get("stdout", "")
                
                # 尝试从输出中提取UID
                fault_id = self._extract_uid_from_output(output)
                
                return FaultResult(
                    success=True,
                    fault_id=fault_id,
                    message="命令执行成功",
                    output=output,
                    command=command
                )
            else:
                error_msg = result.get("stderr", result.get("error", "命令执行失败"))
                return FaultResult(
                    success=False,
                    fault_id="",
                    message=f"命令执行失败: {error_msg}",
                    error=error_msg,
                    command=command
                )
                
        except Exception as e:
            logger.error(f"执行ChaosBlade命令异常: {str(e)}")
            return FaultResult(
                success=False,
                fault_id="",
                message=f"命令执行异常: {str(e)}",
                error=str(e),
                command=command
            )
    
    def _extract_uid_from_output(self, output: str) -> str:
        """
        从ChaosBlade输出中提取UID
        
        Args:
            output: 命令输出
            
        Returns:
            str: 提取的UID，如果未找到则返回空字符串
        """
        try:
            import json
            import re
            
            # 尝试解析JSON格式的输出
            if output.strip().startswith('{'):
                data = json.loads(output)
                if isinstance(data, dict) and 'result' in data:
                    return data['result']
            
            # 尝试使用正则表达式提取UID
            uid_pattern = r'uid:\s*([a-f0-9-]+)'
            match = re.search(uid_pattern, output, re.IGNORECASE)
            if match:
                return match.group(1)
            
            # 尝试提取其他格式的ID
            id_pattern = r'id:\s*([a-f0-9-]+)'
            match = re.search(id_pattern, output, re.IGNORECASE)
            if match:
                return match.group(1)
                
            # 如果都没找到，返回时间戳作为备用ID
            from datetime import datetime
            return f"fault_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
        except Exception as e:
            logger.warning(f"提取UID失败: {str(e)}")
            from datetime import datetime
            return f"fault_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def _build_chaosblade_command(
        self, 
        action: str,  # create/destroy
        target: str,  # cpu/memory/network等
        params: Dict[str, Any],
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> str:
        """
        构建ChaosBlade命令
        
        Args:
            action: 操作类型 create/destroy
            target: 目标类型
            params: 参数字典
            chaosblade_path: ChaosBlade路径
            
        Returns:
            str: 构建的命令
        """
        cmd_parts = [chaosblade_path, action, target]
        
        # 添加参数
        for key, value in params.items():
            if value is not None and value != "":
                cmd_parts.extend([f"--{key}", str(value)])
        
        return " ".join(cmd_parts)
    
    def _build_destroy_command(
        self, 
        fault_id: str,
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> str:
        """
        构建销毁命令
        
        Args:
            fault_id: 故障ID
            chaosblade_path: ChaosBlade路径
            
        Returns:
            str: 销毁命令
        """
        return f"{chaosblade_path} destroy {fault_id}"
