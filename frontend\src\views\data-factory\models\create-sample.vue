<template>
  <div class="sample-based-creator">
    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" finish-status="success" class="steps-container">
      <el-step title="导入样本数据" description="粘贴JSON或上传文件" />
      <el-step title="字段配置确认" description="确认字段解析结果" />
      <el-step title="完成创建" description="保存数据模型" />
    </el-steps>

    <!-- 步骤1：导入样本数据 -->
    <el-card v-if="currentStep === 0" class="step-card">
      <template #header>
        <div class="card-header">
          <h3>导入样本数据</h3>
          <p class="subtitle">提供样本数据，系统将自动解析字段结构</p>
        </div>
      </template>
      
      <el-tabs v-model="inputMethod" class="input-tabs">
        <!-- JSON输入 -->
        <el-tab-pane label="粘贴JSON" name="json">
          <div class="json-input-area">
            <el-input
              v-model="jsonInput"
              type="textarea"
              :rows="12"
              placeholder="粘贴您的JSON样本数据，支持单个对象或数组格式&#10;例如：&#10;{&#10;  &quot;name&quot;: &quot;张三&quot;,&#10;  &quot;phone&quot;: &quot;13800138000&quot;,&#10;  &quot;email&quot;: &quot;<EMAIL>&quot;,&#10;  &quot;age&quot;: 25&#10;}"
              maxlength="10000"
              show-word-limit
            />
            <div class="input-actions">
              <el-button @click="clearInput">清空</el-button>
              <el-button @click="formatJson">格式化</el-button>
              <el-button type="primary" @click="parseJsonData" :loading="isParsingLoading">
                解析数据
              </el-button>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 文件上传 -->
        <el-tab-pane label="上传文件" name="file">
          <el-upload
            class="upload-area"
            drag
            :auto-upload="false"
            :on-change="handleFileChange"
            accept=".json,.csv,.xlsx"
            :show-file-list="false"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip">
              支持 JSON、CSV、Excel 格式文件
            </div>
          </el-upload>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 步骤2：字段配置确认 -->
    <el-card v-if="currentStep === 1" class="step-card">
      <template #header>
        <div class="card-header">
          <h3>字段配置确认</h3>
          <p class="subtitle">系统已自动解析出 {{ parsedFields.length }} 个字段，请确认或调整生成规则</p>
        </div>
      </template>
      
      <!-- 模型基本信息 -->
      <div class="model-info">
        <el-form :model="modelForm" label-width="100px" class="model-form">
          <el-form-item label="模型名称" required>
            <el-input 
              v-model="modelForm.name" 
              placeholder="输入模型名称" 
              style="width: 300px"
              clearable
            />
          </el-form-item>
          <el-form-item label="模型描述">
            <el-input 
              v-model="modelForm.description" 
              placeholder="模型描述（可选）" 
              style="width: 400px"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 字段配置界面 - 树形结构 -->
      <div class="field-config-container">
        <!-- 左侧字段树 -->
        <div class="field-tree-panel">
          <div class="tree-header">
            <h4>字段结构</h4>
            <el-button @click="addCustomField" size="small" type="primary">+ 添加字段</el-button>
          </div>
          <el-tree
            :data="fieldTreeData"
            :props="treeProps"
            node-key="id"
            :default-expand-all="true"
            :highlight-current="true"
            @node-click="handleFieldSelect"
            class="field-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <span class="node-label">{{ data.label }}</span>
                <span class="node-type">{{ data.type }}</span>
                <el-button
                  v-if="!data.isGroup"
                  @click.stop="removeFieldFromTree(data)"
                  size="small"
                  type="danger"
                  link
                  class="node-delete"
                >
                  ×
                </el-button>
              </div>
            </template>
          </el-tree>
        </div>

        <!-- 右侧配置面板 -->
        <div class="field-config-panel">
          <div v-if="selectedField" class="config-content">
            <div class="config-header">
              <h4>字段配置</h4>
              <el-tag :type="getFieldTypeColor(selectedField.type)">{{ selectedField.type }}</el-tag>
            </div>

            <el-form :model="selectedField" label-width="100px" class="config-form">
              <el-form-item label="字段名">
                <el-input v-model="selectedField.name" placeholder="字段名称" />
              </el-form-item>

              <el-form-item label="数据类型">
                <el-select v-model="selectedField.type" style="width: 100%">
                  <el-option label="字符串" value="string" />
                  <el-option label="整数" value="integer" />
                  <el-option label="小数" value="decimal" />
                  <el-option label="布尔值" value="boolean" />
                  <el-option label="日期" value="date" />
                  <el-option label="日期时间" value="datetime" />
                </el-select>
              </el-form-item>

              <el-form-item label="格式类型">
                <el-radio-group v-model="selectedField.processType" @change="handleProcessTypeChange(selectedField)">
                  <el-radio value="keep">保持原值</el-radio>
                  <el-radio value="builtin">内置生成器</el-radio>
                  <el-radio value="custom">自定义生成器</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 内置生成器配置 -->
              <el-form-item v-if="selectedField.processType === 'builtin'" label="生成器">
                <el-select v-model="selectedField.generator" placeholder="选择生成器" style="width: 100%">
                  <el-option label="UUID" value="uuid" />
                  <el-option label="姓名" value="name" />
                  <el-option label="手机号" value="phone" />
                  <el-option label="邮箱" value="email" />
                  <el-option label="数值范围" value="range" />
                  <el-option label="递增序列" value="sequence" />
                  <el-option label="日期" value="date" />
                </el-select>
              </el-form-item>

              <!-- 自定义生成器配置 -->
              <el-form-item v-if="selectedField.processType === 'custom'" label="自定义代码">
                <div class="custom-config">
                  <el-button @click="editCustomGenerator(selectedField)" type="primary">
                    编辑代码
                  </el-button>
                  <span v-if="selectedField.customCode" class="code-status">✓ 已配置</span>
                </div>
              </el-form-item>

              <!-- 约束配置 -->
              <el-form-item label="约束">
                <div class="constraints">
                  <el-checkbox v-model="selectedField.required">必填</el-checkbox>
                  <el-checkbox v-model="selectedField.unique">唯一</el-checkbox>
                </div>
              </el-form-item>

              <!-- 样本值展示 -->
              <el-form-item label="样本值">
                <div class="sample-values">
                  <el-tag
                    v-for="(sample, index) in selectedField.samples?.slice(0, 5)"
                    :key="index"
                    size="small"
                    class="sample-tag"
                  >
                    {{ formatSampleValue(sample) }}
                  </el-tag>
                  <span v-if="selectedField.samples?.length > 5" class="more-samples">
                    +{{ selectedField.samples.length - 5 }}
                  </span>
                </div>
              </el-form-item>
            </el-form>
          </div>

          <div v-else class="no-selection">
            <el-empty description="请选择左侧字段进行配置" />
          </div>
        </div>
      </div>
      
      <div class="config-actions">
        <el-button @click="currentStep = 0">上一步</el-button>
        <el-button @click="addCustomField">+ 添加字段</el-button>
        <el-button @click="handlePreviewData" type="success" :loading="isPreviewLoading">预览数据</el-button>
        <el-button @click="nextStep" type="primary">下一步</el-button>
      </div>
    </el-card>

    <!-- 步骤3：完成创建 -->
    <el-card v-if="currentStep === 2" class="step-card">
      <template #header>
        <div class="card-header">
          <h3>完成创建</h3>
          <p class="subtitle">确认模型配置并保存</p>
        </div>
      </template>
      
      <div class="summary-info">
        <el-descriptions title="模型信息" :column="2" border>
          <el-descriptions-item label="模型名称">{{ modelForm.name }}</el-descriptions-item>
          <el-descriptions-item label="模型描述">{{ modelForm.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="字段数量">{{ validFields.length }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ new Date().toLocaleString() }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div class="final-actions">
        <el-button @click="currentStep = 1">上一步</el-button>
        <el-button @click="saveModel" type="primary" :loading="isSaveLoading">
          保存模型
        </el-button>
      </div>
    </el-card>

    <!-- 数据预览对话框 -->
    <el-dialog v-model="previewVisible" title="数据预览" width="80%">
      <el-table :data="previewData" border max-height="400">
        <el-table-column 
          v-for="field in validFields" 
          :key="field.name"
          :prop="field.name" 
          :label="field.name"
          min-width="120"
          show-overflow-tooltip
        />
      </el-table>
      <template #footer>
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button @click="generateMorePreview" type="primary" :loading="isPreviewLoading">
          生成更多
        </el-button>
      </template>
    </el-dialog>

    <!-- 自定义生成器编辑对话框 -->
    <el-dialog v-model="codeEditorVisible" title="自定义生成器" width="80%" class="code-editor-dialog">
      <div class="editor-content">
        <div class="editor-info">
          <h4>为字段 "{{ currentField?.name }}" 编写生成器</h4>
          <p>样本数据：{{ currentField?.samples.join(', ') }}</p>
        </div>
        
        <div class="code-section">
          <h5>Python代码 (在沙箱环境中执行)</h5>
          <el-input
            v-model="customCode"
            type="textarea"
            :rows="15"
            placeholder="def generate(samples, index, context):
    &quot;&quot;&quot;
    自定义数据生成函数
    
    参数:
    - samples: 样本数据列表，如 ['张三', '李四', '王五']
    - index: 当前生成的数据索引 (从0开始)
    - context: 上下文信息，包含其他字段的值
    
    返回:
    - 生成的数据值
    &quot;&quot;&quot;
    import random
    
    # 示例1: 随机选择样本数据
    return random.choice(samples)
    
    # 示例2: 基于样本数据变换
    # base_name = random.choice(samples)
    # return f'{base_name}_{index:03d}'
    
    # 示例3: 基于其他字段生成
    # if 'name' in context:
    #     return f'{context[&quot;name&quot;]}_modified'
    
    # 示例4: 完全自定义逻辑
    # return f'custom_value_{index}'"
            class="code-editor"
          />
        </div>
        
        <div class="test-section">
          <h5>测试代码</h5>
          <el-button @click="testCustomCode" type="primary" :loading="isTestingCode">测试运行</el-button>
          <div v-if="testResults.length > 0" class="test-results">
            <h6>测试结果:</h6>
            <el-tag v-for="result in testResults" :key="result" size="small" class="result-tag">
              {{ result }}
            </el-tag>
          </div>
          <div v-if="testError" class="test-error">
            <el-alert :title="testError" type="error" show-icon />
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="codeEditorVisible = false">取消</el-button>
        <el-button @click="saveCustomCode" type="primary">保存代码</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { DataFactoryService } from '@/api/dataFactoryApi'

// 路由
const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const inputMethod = ref('json')
const jsonInput = ref('')
const parsedFields = ref<any[]>([])
const previewVisible = ref(false)
const previewData = ref<any[]>([])
const codeEditorVisible = ref(false)
const currentField = ref<any>(null)
const customCode = ref('')
const testResults = ref<string[]>([])
const testError = ref('')

// 树形结构相关
const selectedField = ref<any>(null)
const fieldTreeData = ref<any[]>([])
const treeProps = {
  children: 'children',
  label: 'label'
}

// 加载状态
const isParsingLoading = ref(false)
const isPreviewLoading = ref(false)
const isSaveLoading = ref(false)
const isTestingCode = ref(false)

// 模型表单
const modelForm = reactive({
  name: '',
  description: ''
})

// 计算属性
const validFields = computed(() => {
  return parsedFields.value.filter(field => field.processType !== 'delete')
})

// 方法实现
const clearInput = () => {
  jsonInput.value = ''
}

const formatJson = () => {
  try {
    const parsed = JSON.parse(jsonInput.value)
    jsonInput.value = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式不正确，无法格式化')
  }
}

const parseJsonData = async () => {
  if (!jsonInput.value.trim()) {
    ElMessage.warning('请输入JSON数据')
    return
  }

  isParsingLoading.value = true

  try {
    // 使用DataFactoryService调用后端API
    const result = await DataFactoryService.parseSampleData(jsonInput.value)

    if (result && (result as any).success) {
      const parseResult = result as any

      // 转换后端返回的字段格式为前端需要的格式
      const fields = Object.values(parseResult.fields).map((field: any, index: number) => ({
        id: `field_${index}`,
        name: field.name,
        type: field.inferred_type,
        processType: 'keep', // 默认保持原值
        generator: field.recommended_generator,
        customCode: '',
        samples: field.sample_values,
        required: field.is_required,
        unique: false
      }))

      parsedFields.value = fields

      // 构建树形结构
      buildFieldTree(fields)

      // 自动生成模型名称
      if (!modelForm.name) {
        modelForm.name = `模型_${Date.now()}`
      }

      currentStep.value = 1
      ElMessage.success(`成功解析出 ${fields.length} 个字段`)
    } else {
      ElMessage.error((result as any)?.error || '解析失败，请检查JSON格式')
    }
  } catch (error) {
    console.error('解析失败:', error)
    ElMessage.error('解析失败，请检查网络连接和JSON格式')
  } finally {
    isParsingLoading.value = false
  }
}

// 前端解析方法已移除，现在使用后端API解析

const handleFileChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      jsonInput.value = e.target?.result as string
      ElMessage.success('文件读取成功')
    } catch (error) {
      ElMessage.error('文件读取失败')
    }
  }
  reader.readAsText(file.raw)
}

const handleProcessTypeChange = (field: any) => {
  // 根据处理方式重置相关配置
  if (field.processType === 'builtin') {
    field.generator = inferGenerator(field.name, field.samples[0])
  } else if (field.processType === 'custom') {
    field.customCode = getDefaultCustomCode()
  }
}

const inferGenerator = (fieldName: string, value: any): string => {
  const name = fieldName.toLowerCase()

  // 基于字段名推断
  if (name.includes('id')) return 'uuid'
  if (name.includes('name') || name.includes('用户名')) return 'name'
  if (name.includes('phone') || name.includes('手机')) return 'phone'
  if (name.includes('email') || name.includes('邮箱')) return 'email'
  if (name.includes('age') || name.includes('年龄')) return 'range'

  // 基于值的格式推断
  if (typeof value === 'string') {
    if (/^\d{11}$/.test(value)) return 'phone'
    if (/^[\w-]+@[\w-]+\.\w+$/.test(value)) return 'email'
    if (/^\d{4}-\d{2}-\d{2}/.test(value)) return 'date'
  }

  if (typeof value === 'number') return 'range'

  return 'name'
}

const nextStep = () => {
  if (validFields.value.length === 0) {
    ElMessage.warning('请至少保留一个字段')
    return
  }

  if (!modelForm.name.trim()) {
    ElMessage.warning('请输入模型名称')
    return
  }

  currentStep.value = 2
}

const removeField = (index: number) => {
  parsedFields.value.splice(index, 1)
}

const buildFieldTree = (fields: any[]) => {
  const tree: any[] = []

  fields.forEach((field, index) => {
    field.id = `field_${index}`
    field.unique = false // 添加唯一约束字段

    const parts = field.name.split('.')
    insertIntoTree(tree, parts, field, 0)
  })

  fieldTreeData.value = tree
}

const insertIntoTree = (tree: any[], parts: string[], field: any, depth: number) => {
  if (parts.length === 1) {
    // 叶子节点 - 实际字段
    tree.push({
      id: field.id,
      label: parts[0],
      type: field.type,
      isGroup: false,
      field: field
    })
    return
  }

  // 中间节点 - 分组
  const currentPart = parts[0]
  const remainingParts = parts.slice(1)

  // 查找是否已存在该分组
  let group = tree.find(node => node.label === currentPart && node.isGroup)

  if (!group) {
    // 创建新分组
    group = {
      id: `group_${currentPart}_${depth}`,
      label: currentPart,
      type: 'object',
      isGroup: true,
      children: []
    }
    tree.push(group)
  }

  // 递归插入到子树
  insertIntoTree(group.children, remainingParts, field, depth + 1)
}

const handleFieldSelect = (data: any) => {
  if (!data.isGroup && data.field) {
    selectedField.value = data.field
  }
}

const removeFieldFromTree = (data: any) => {
  if (data.field) {
    const index = parsedFields.value.findIndex(f => f.id === data.field.id)
    if (index !== -1) {
      parsedFields.value.splice(index, 1)
      buildFieldTree(parsedFields.value)
      if (selectedField.value?.id === data.field.id) {
        selectedField.value = null
      }
    }
  }
}

const getFieldTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'string': '',
    'integer': 'success',
    'decimal': 'success',
    'boolean': 'warning',
    'date': 'info',
    'datetime': 'info',
    'email': 'primary',
    'phone': 'primary',
    'uuid': 'primary'
  }
  return colorMap[type] || ''
}

const addCustomField = () => {
  const newField = {
    id: `field_${Date.now()}`,
    name: `字段_${parsedFields.value.length + 1}`,
    type: 'string',
    processType: 'builtin',
    generator: 'name',
    customCode: '',
    samples: ['示例值'],
    required: true,
    unique: false
  }

  parsedFields.value.push(newField)
  buildFieldTree(parsedFields.value)
  selectedField.value = newField
}

const formatSampleValue = (value: any): string => {
  if (value === null || value === undefined) return 'null'
  if (typeof value === 'string' && value.length > 20) {
    return value.substring(0, 20) + '...'
  }
  return String(value)
}

const editCustomGenerator = (field: any) => {
  currentField.value = field
  customCode.value = field.customCode || getDefaultCustomCode()
  codeEditorVisible.value = true
  testResults.value = []
  testError.value = ''
}

const getDefaultCustomCode = () => {
  return `def generate(samples, index, context):
    """
    自定义数据生成函数

    参数:
    - samples: 样本数据列表
    - index: 当前生成的数据索引
    - context: 上下文信息，包含其他字段的值

    返回:
    - 生成的数据值
    """
    import random

    # 随机选择样本数据
    return random.choice(samples)
`
}

const testCustomCode = async () => {
  if (!currentField.value || !customCode.value.trim()) {
    ElMessage.warning('请输入代码')
    return
  }

  isTestingCode.value = true
  testError.value = ''
  testResults.value = []

  try {
    // 使用DataFactoryService调用后端API测试代码
    const result = await DataFactoryService.testCustomGenerator(
      customCode.value,
      currentField.value.samples,
      5
    )

    if (result && (result as any).success) {
      testResults.value = (result as any).results
      ElMessage.success('代码测试成功')
    } else {
      testError.value = (result as any).error
    }
  } catch (error) {
    testError.value = '测试失败：网络错误'
  } finally {
    isTestingCode.value = false
  }
}

const saveCustomCode = () => {
  if (!currentField.value) return

  currentField.value.customCode = customCode.value
  codeEditorVisible.value = false
  ElMessage.success('自定义生成器保存成功')
}

const handlePreviewData = async () => {
  isPreviewLoading.value = true

  try {
    // 模拟生成预览数据
    const mockData = []
    for (let i = 0; i < 5; i++) {
      const record: any = {}
      validFields.value.forEach(field => {
        if (field.processType === 'keep') {
          record[field.name] = field.samples[Math.floor(Math.random() * field.samples.length)]
        } else if (field.processType === 'builtin') {
          record[field.name] = generateMockValue(field.generator, i)
        } else {
          record[field.name] = `自定义_${i}`
        }
      })
      mockData.push(record)
    }

    previewData.value = mockData
    previewVisible.value = true
  } catch (error) {
    ElMessage.error('预览生成失败')
  } finally {
    isPreviewLoading.value = false
  }
}

const generateMockValue = (generator: string, index: number): any => {
  switch (generator) {
    case 'uuid':
      return `uuid-${index}-${Math.random().toString(36).substr(2, 9)}`
    case 'name':
      return ['张三', '李四', '王五', '赵六', '钱七'][index % 5]
    case 'phone':
      return `138${String(index).padStart(8, '0')}`
    case 'email':
      return `user${index}@example.com`
    case 'range':
      return Math.floor(Math.random() * 100) + 1
    case 'sequence':
      return index + 1
    case 'date':
      return new Date().toISOString().split('T')[0]
    default:
      return `值_${index}`
  }
}

const generateMorePreview = () => {
  handlePreviewData()
}

const saveModel = async () => {
  if (!modelForm.name.trim()) {
    ElMessage.warning('请输入模型名称')
    return
  }

  if (validFields.value.length === 0) {
    ElMessage.warning('至少保留一个字段')
    return
  }

  isSaveLoading.value = true

  try {
    // 构建字段配置
    const fieldsConfig = validFields.value.map(field => {
      const config: any = {
        name: field.name,
        type: field.type,
        required: field.required
      }

      switch (field.processType) {
        case 'keep':
          config.generator = 'sample'
          config.options = { samples: field.samples }
          break
        case 'builtin':
          config.generator = field.generator
          config.options = {}
          break
        case 'custom':
          config.generator = 'custom'
          config.options = {
            code: field.customCode,
            samples: field.samples
          }
          break
      }

      return config
    })

    const modelData = {
      name: modelForm.name,
      description: modelForm.description || '基于样本数据创建',
      version: '1.0.0',
      fields_config: fieldsConfig,
      status: '1'
    }

    // 使用DataFactoryService保存模型
    const result = await DataFactoryService.createDataModel(modelData as any)

    if (result) {
      ElMessage.success('数据模型创建成功')
      router.push('/data-factory/models')
    } else {
      throw new Error('保存失败')
    }
  } catch (error) {
    ElMessage.error('创建失败，请重试')
  } finally {
    isSaveLoading.value = false
  }
}
</script>

<style scoped>
.sample-based-creator {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.steps-container {
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.step-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.step-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: #1e293b;
  color: white;
  padding: 16px 20px;
  border-radius: 8px 8px 0 0;
}

.card-header h3 {
  margin: 0 0 4px 0;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #cbd5e1;
  font-size: 13px;
}

.json-input-area {
  margin-top: 20px;
  padding: 20px;
}

.json-input-area :deep(.el-textarea__inner) {
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: border-color 0.2s ease;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #ffffff;
}

.json-input-area :deep(.el-textarea__inner):focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-actions {
  margin-top: 16px;
  text-align: right;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.input-actions .el-button {
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
}

.input-actions .el-button--primary {
  background: #3b82f6;
  border-color: #3b82f6;
}

.input-actions .el-button--primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.upload-area {
  margin-top: 20px;
  padding: 20px;
}

.upload-area :deep(.el-upload-dragger) {
  border-radius: 6px;
  border: 2px dashed #d1d5db;
  background: #ffffff;
  transition: border-color 0.2s ease;
}

.upload-area :deep(.el-upload-dragger):hover {
  border-color: #3b82f6;
}

.model-info {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.field-config-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  min-height: 500px;
}

.field-tree-panel {
  width: 300px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 6px 6px 0 0;
}

.tree-header h4 {
  margin: 0;
  color: #1e293b;
  font-size: 14px;
  font-weight: 600;
}

.tree-header .el-button {
  border-radius: 4px;
  font-size: 12px;
  padding: 4px 8px;
}

.field-tree {
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.field-tree::-webkit-scrollbar {
  width: 4px;
}

.field-tree::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.field-tree::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 6px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.tree-node:hover {
  background: #f1f5f9;
}

.node-label {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.node-type {
  font-size: 11px;
  color: #6b7280;
  margin-right: 8px;
  background: #f1f5f9;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
}

.node-delete {
  opacity: 0;
  transition: opacity 0.2s ease;
  color: #ef4444;
  font-size: 14px;
}

.tree-node:hover .node-delete {
  opacity: 1;
}

.field-config-panel {
  flex: 1;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.config-content {
  padding: 20px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.config-header h4 {
  margin: 0;
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
}

.config-form {
  max-width: 500px;
}

.config-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.config-form :deep(.el-input__wrapper) {
  border-radius: 4px;
}

.config-form :deep(.el-select) {
  width: 100%;
}

.custom-config {
  display: flex;
  align-items: center;
  gap: 12px;
}

.custom-config .el-button {
  border-radius: 4px;
  background: #10b981;
  border-color: #10b981;
  color: white;
  font-weight: 500;
}

.custom-config .el-button:hover {
  background: #059669;
  border-color: #059669;
}

.code-status {
  color: #10b981;
  font-size: 12px;
  font-weight: 500;
  background: rgba(16, 185, 129, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

.constraints {
  display: flex;
  gap: 16px;
}

.constraints :deep(.el-checkbox__label) {
  font-weight: 500;
  color: #374151;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px dashed #cbd5e1;
}

.no-selection :deep(.el-empty__description p) {
  color: #64748b;
  font-size: 14px;
}

.sample-values {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.sample-tag {
  max-width: 120px;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.more-samples {
  color: #6b7280;
  font-size: 12px;
  background: rgba(107, 114, 128, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.custom-generator {
  display: flex;
  align-items: center;
  gap: 12px;
}

.code-status {
  color: #10b981;
  font-size: 12px;
  font-weight: 500;
  background: rgba(16, 185, 129, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

.keep-original {
  color: #6b7280;
  font-size: 12px;
  font-style: italic;
}

.config-actions {
  text-align: right;
  margin-top: 32px;
  padding: 20px 24px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 0 0 16px 16px;
  border-top: 1px solid rgba(228, 231, 237, 0.3);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.config-actions .el-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.config-actions .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.config-actions .el-button--success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.config-actions .el-button:hover {
  transform: translateY(-2px);
}

.summary-info {
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.final-actions {
  text-align: right;
  padding: 20px 24px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 0 0 16px 16px;
  border-top: 1px solid rgba(228, 231, 237, 0.3);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.final-actions .el-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.final-actions .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.final-actions .el-button:hover {
  transform: translateY(-2px);
}

.editor-content {
  padding: 20px 0;
}

.editor-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.editor-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.editor-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.code-section {
  margin-bottom: 20px;
}

.code-section h5 {
  margin: 0 0 10px 0;
  color: #303133;
}

.code-editor {
  font-family: 'Courier New', monospace;
}

.test-section h5 {
  margin: 0 0 10px 0;
  color: #303133;
}

.test-results {
  margin-top: 15px;
}

.test-results h6 {
  margin: 0 0 8px 0;
  color: #303133;
}

.result-tag {
  margin: 2px 4px 2px 0;
}

.test-error {
  margin-top: 15px;
}
</style>
