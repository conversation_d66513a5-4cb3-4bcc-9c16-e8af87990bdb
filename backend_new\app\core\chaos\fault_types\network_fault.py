"""
网络故障处理器
实现网络相关的故障注入和销毁
"""
import logging
from typing import Dict, Any
from .base_fault import BaseFaultHandler, FaultResult

logger = logging.getLogger(__name__)


class NetworkFaultHandler(BaseFaultHandler):
    """网络故障处理器"""
    
    async def inject(
        self, 
        ssh_client, 
        fault_params: Dict[str, Any],
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """
        注入网络故障
        
        支持的参数:
        - fault_action: 故障动作 (delay/loss/duplicate/corrupt/reorder)
        - interface: 网络接口名称 (如: eth0)
        - destination-ip: 目标IP地址
        - destination-port: 目标端口
        - time: 延迟时间(ms) - 用于delay
        - percent: 丢包率(%) - 用于loss
        - timeout: 持续时间(秒)
        """
        # 验证参数
        is_valid, error_msg = self.validate_params(fault_params)
        if not is_valid:
            return FaultResult(
                success=False,
                fault_id="",
                message=f"参数验证失败: {error_msg}"
            )
        
        # 构建命令参数
        cmd_params = {}
        fault_action = fault_params.get("fault_action", "delay")
        
        # 网络接口
        if "interface" in fault_params:
            cmd_params["interface"] = fault_params["interface"]
        
        # 目标IP
        if "destination_ip" in fault_params:
            cmd_params["destination-ip"] = fault_params["destination_ip"]
        
        # 目标端口
        if "destination_port" in fault_params:
            cmd_params["destination-port"] = fault_params["destination_port"]
        
        # 根据故障动作添加特定参数
        if fault_action == "delay" and "time" in fault_params:
            cmd_params["time"] = fault_params["time"]
        elif fault_action in ["loss", "duplicate", "corrupt"] and "percent" in fault_params:
            cmd_params["percent"] = fault_params["percent"]
        
        # 持续时间
        if "timeout" in fault_params:
            cmd_params["timeout"] = fault_params["timeout"]
        
        # 构建ChaosBlade命令
        command = self._build_chaosblade_command(
            action="create",
            target=f"network {fault_action}",
            params=cmd_params,
            chaosblade_path=chaosblade_path
        )
        
        # 执行命令
        return await self._execute_chaosblade_command(ssh_client, command)
    
    async def destroy(
        self, 
        ssh_client, 
        fault_id: str,
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """销毁网络故障"""
        command = self._build_destroy_command(fault_id, chaosblade_path)
        return await self._execute_chaosblade_command(ssh_client, command)
    
    def validate_params(self, fault_params: Dict[str, Any]) -> tuple[bool, str]:
        """验证网络故障参数"""
        # 验证故障动作
        fault_action = fault_params.get("fault_action", "delay")
        valid_actions = ["delay", "loss", "duplicate", "corrupt", "reorder"]
        if fault_action not in valid_actions:
            return False, f"fault_action必须是{valid_actions}中的一个"
        
        # 验证网络接口
        interface = fault_params.get("interface")
        if interface and not isinstance(interface, str):
            return False, "interface必须是字符串"
        
        # 验证目标IP
        destination_ip = fault_params.get("destination_ip")
        if destination_ip and not self._validate_ip(destination_ip):
            return False, "destination_ip格式错误"
        
        # 验证目标端口
        destination_port = fault_params.get("destination_port")
        if destination_port is not None:
            if not isinstance(destination_port, int) or not (1 <= destination_port <= 65535):
                return False, "destination_port必须是1-65535之间的整数"
        
        # 验证延迟时间
        if fault_action == "delay":
            time = fault_params.get("time")
            if time is not None:
                if not isinstance(time, int) or time <= 0:
                    return False, "time必须是正整数(毫秒)"
        
        # 验证百分比
        if fault_action in ["loss", "duplicate", "corrupt"]:
            percent = fault_params.get("percent")
            if percent is not None:
                if not isinstance(percent, (int, float)) or not (0 <= percent <= 100):
                    return False, "percent必须是0-100之间的数字"
        
        # 验证超时时间
        timeout = fault_params.get("timeout")
        if timeout is not None:
            if not isinstance(timeout, int) or timeout <= 0:
                return False, "timeout必须是正整数"
        
        return True, ""
    
    def _validate_ip(self, ip: str) -> bool:
        """验证IP地址格式"""
        import re
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip):
            return False
        
        # 验证每个数字段是否在0-255范围内
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    
    def get_param_schema(self) -> Dict[str, Any]:
        """获取网络故障参数结构定义"""
        return {
            "type": "object",
            "properties": {
                "fault_action": {
                    "type": "string",
                    "enum": ["delay", "loss", "duplicate", "corrupt", "reorder"],
                    "description": "故障动作类型",
                    "default": "delay"
                },
                "interface": {
                    "type": "string",
                    "description": "网络接口名称，如eth0",
                    "required": False
                },
                "destination_ip": {
                    "type": "string",
                    "pattern": r"^(\d{1,3}\.){3}\d{1,3}$",
                    "description": "目标IP地址",
                    "required": False
                },
                "destination_port": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 65535,
                    "description": "目标端口",
                    "required": False
                },
                "time": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "延迟时间(毫秒)，用于delay动作",
                    "required": False
                },
                "percent": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 100,
                    "description": "百分比，用于loss/duplicate/corrupt动作",
                    "required": False
                },
                "timeout": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "持续时间(秒)，不指定则持续到手动销毁",
                    "required": False
                }
            },
            "required": [],
            "additionalProperties": False
        }
