#!/usr/bin/env python3
"""
Celery监控脚本
用于启动Celery Flower监控界面
"""
import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.celery_app import celery_app
from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """启动Celery Flower监控"""
    logger.info("启动Celery Flower监控界面...")
    logger.info(f"Redis连接: {settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}")
    
    # Flower参数
    flower_args = [
        'flower',
        '--app=app.core.celery_app:celery_app',
        '--port=5555',
        f'--broker=redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}',
        '--persistent=True',
        '--db=flower.db',
        '--max_tasks=10000',
        '--url_prefix=flower'
    ]
    
    # 启动Flower
    celery_app.worker_main(flower_args)

if __name__ == '__main__':
    main()
