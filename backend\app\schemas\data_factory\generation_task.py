"""
数据生成任务相关Schema定义
"""
from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator,ConfigDict
from app.schemas.base import BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema


class GenerationTaskBase(BaseModel):
    """生成任务基础Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=1000, description="任务描述")
    model_id: int = Field(..., description="数据模型ID")
    record_count: int = Field(..., ge=1, le=1000000, description="生成数据条数")
    export_format: str = Field(default="json", description="导出格式：json/csv/excel/sql")
    export_config: Optional[Dict[str, Any]] = Field(None, description="导出配置")

    model_config = ConfigDict(protected_namespaces=())
    
    @validator('export_format')
    def validate_export_format(cls, v):
        """验证导出格式"""
        allowed_formats = ['json', 'csv', 'excel', 'sql']
        if v not in allowed_formats:
            raise ValueError(f'导出格式必须是以下之一：{", ".join(allowed_formats)}')
        return v
    
    @validator('record_count')
    def validate_record_count(cls, v):
        """验证记录数量"""
        if v <= 0:
            raise ValueError('生成数据条数必须大于0')
        if v > 1000000:
            raise ValueError('MVP版本最大支持100万条数据生成')
        return v


class GenerationTaskCreate(GenerationTaskBase, BaseCreateSchema):
    """创建生成任务Schema"""
    pass


class GenerationTaskUpdate(BaseUpdateSchema):
    """更新生成任务Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=1000, description="任务描述")
    status: Optional[str] = Field(None, description="任务状态")
    progress: Optional[int] = Field(None, ge=0, le=100, description="执行进度")
    error_message: Optional[str] = Field(None, description="错误信息")


class GenerationTaskResponse(BaseResponseSchema):
    """生成任务响应Schema"""
    name: str = Field(description="任务名称")
    description: Optional[str] = Field(description="任务描述")
    model_id: int = Field(description="数据模型ID")
    record_count: int = Field(description="生成数据条数")
    export_format: str = Field(description="导出格式")
    export_config: Optional[Dict[str, Any]] = Field(description="导出配置")
    status: str = Field(description="任务状态")
    progress: int = Field(description="执行进度")
    started_at: Optional[datetime] = Field(description="开始执行时间")
    completed_at: Optional[datetime] = Field(description="完成时间")
    execution_time: Optional[int] = Field(description="执行耗时（秒）")
    result_file_path: Optional[str] = Field(description="结果文件路径")
    result_file_size: Optional[int] = Field(description="结果文件大小（字节）")
    error_message: Optional[str] = Field(description="错误信息")
    memory_usage: Optional[int] = Field(description="内存使用量（MB）")
    cpu_usage: Optional[int] = Field(description="CPU使用率（%）")

    model_config = ConfigDict(protected_namespaces=())



class TaskStatusUpdate(BaseModel):
    """任务状态更新Schema"""
    status: str = Field(..., description="任务状态：pending/running/completed/failed/cancelled")
    progress: int = Field(default=0, ge=0, le=100, description="执行进度")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    @validator('status')
    def validate_status(cls, v):
        """验证任务状态"""
        allowed_statuses = ['pending', 'running', 'completed', 'failed', 'cancelled']
        if v not in allowed_statuses:
            raise ValueError(f'任务状态必须是以下之一：{", ".join(allowed_statuses)}')
        return v


class TaskExecutionRequest(BaseModel):
    """任务执行请求Schema"""
    task_id: int = Field(..., description="任务ID")
    action: str = Field(..., description="操作类型：start/pause/resume/cancel")
    
    @validator('action')
    def validate_action(cls, v):
        """验证操作类型"""
        allowed_actions = ['start', 'pause', 'resume', 'cancel']
        if v not in allowed_actions:
            raise ValueError(f'操作类型必须是以下之一：{", ".join(allowed_actions)}')
        return v


class TaskStatistics(BaseModel):
    """任务统计Schema"""
    total_tasks: int = Field(description="总任务数")
    pending_tasks: int = Field(description="等待执行任务数")
    running_tasks: int = Field(description="正在执行任务数")
    completed_tasks: int = Field(description="已完成任务数")
    failed_tasks: int = Field(description="失败任务数")
    success_rate: float = Field(description="成功率（%）")
    avg_execution_time: Optional[float] = Field(description="平均执行时间（秒）")
