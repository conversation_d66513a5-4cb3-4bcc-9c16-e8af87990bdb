"""
简化的Redis连接客户端
使用redis-py库进行同步连接测试
"""
import time
import asyncio
from typing import Dict, Any, Optional

from .base_client import BaseClient, ConnectionResult

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class RedisSimpleClient(BaseClient):
    """
    简化的Redis连接客户端
    使用redis-py进行同步连接测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Redis客户端
        
        Args:
            config: Redis连接配置
                - host: Redis主机地址 (默认: localhost)
                - port: Redis端口 (默认: 6379)
                - password: Redis密码 (可选)
                - db: 数据库编号 (默认: 0)
                - username: Redis用户名 (可选，Redis 6.0+)
                - ssl: 是否使用SSL (默认: False)
        """
        super().__init__(config)
        
        if not REDIS_AVAILABLE:
            raise ImportError("redis库未安装，请运行: pip install redis")
        
        # 设置默认值
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 6379)
        self.password = config.get('password')
        self.db = config.get('db', 0)
        self.username = config.get('username')
        self.ssl = config.get('ssl', False)
        
        self.redis_client = None

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立Redis连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 创建Redis连接
            connection_kwargs = {
                'host': self.host,
                'port': self.port,
                'db': self.db,
                'socket_timeout': timeout,
                'socket_connect_timeout': timeout,
                'decode_responses': True
            }
            
            if self.password:
                connection_kwargs['password'] = self.password
            if self.username:
                connection_kwargs['username'] = self.username
            if self.ssl:
                connection_kwargs['ssl'] = True
            
            # 在异步环境中运行同步Redis连接
            self.redis_client = await asyncio.get_event_loop().run_in_executor(
                None, lambda: redis.Redis(**connection_kwargs)
            )
            
            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.ping
            )
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到Redis服务器 {self.host}:{self.port}",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "db": self.db,
                    "ssl": self.ssl,
                    "auth": "是" if self.password else "否"
                }
            )
            
        except redis.ConnectionError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis连接被拒绝 - 请检查Redis服务是否运行",
                duration=duration,
                details={"error_type": "ConnectionError"}
            )
        except redis.AuthenticationError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message="Redis认证失败 - 请检查用户名和密码",
                duration=duration,
                details={"error_type": "AuthenticationError"}
            )
        except redis.ResponseError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis响应错误: {str(e)}",
                duration=duration,
                details={"error_type": "ResponseError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Redis连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开Redis连接"""
        try:
            if self.redis_client:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.close
                )
                self.redis_client = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试Redis连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        # 如果已连接，先断开
        if self.is_connected:
            await self.disconnect()
        
        # 尝试连接
        result = await self.connect(timeout)
        
        # 如果连接成功，获取服务器信息
        if result.success:
            try:
                # 获取Redis服务器信息
                info = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.info
                )
                
                if info:
                    result.details.update({
                        "redis_version": info.get("redis_version", "未知"),
                        "redis_mode": info.get("redis_mode", "未知"),
                        "used_memory_human": info.get("used_memory_human", "未知"),
                        "connected_clients": info.get("connected_clients", 0),
                        "uptime_in_seconds": info.get("uptime_in_seconds", 0)
                    })
                    
                    result.message += f" (版本: {info.get('redis_version', '未知')})"
                
                # 测试基本操作
                test_key = "connection_test"
                await asyncio.get_event_loop().run_in_executor(
                    None, lambda: self.redis_client.set(test_key, "test_value", ex=10)
                )
                test_value = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: self.redis_client.get(test_key)
                )
                await asyncio.get_event_loop().run_in_executor(
                    None, lambda: self.redis_client.delete(test_key)
                )
                
                if test_value == "test_value":
                    result.details["operation_test"] = "读写操作正常"
                else:
                    result.details["operation_test"] = "读写操作异常"
                
            except Exception as e:
                result.details["info_test"] = f"获取服务器信息失败: {str(e)}"
        
        # 测试完成后断开连接
        if result.success:
            await self.disconnect()
        
        return result

    async def execute_command(self, command: str, *args) -> Dict[str, Any]:
        """
        执行Redis命令
        
        Args:
            command: Redis命令
            *args: 命令参数
            
        Returns:
            Dict: 命令执行结果
        """
        if not self.redis_client:
            raise RuntimeError("Redis未连接")
        
        try:
            result = await asyncio.get_event_loop().run_in_executor(
                None, lambda: self.redis_client.execute_command(command, *args)
            )
            
            return {
                "success": True,
                "result": result,
                "command": f"{command} {' '.join(map(str, args))}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": f"{command} {' '.join(map(str, args))}"
            }

    async def get_server_info(self) -> Dict[str, Any]:
        """
        获取Redis服务器信息
        
        Returns:
            Dict: 服务器信息
        """
        if not self.redis_client:
            raise RuntimeError("Redis未连接")
        
        try:
            info = await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.info
            )
            
            # 提取关键信息
            key_info = {
                "redis_version": info.get("redis_version"),
                "redis_mode": info.get("redis_mode"),
                "os": info.get("os"),
                "arch_bits": info.get("arch_bits"),
                "process_id": info.get("process_id"),
                "tcp_port": info.get("tcp_port"),
                "uptime_in_seconds": info.get("uptime_in_seconds"),
                "uptime_in_days": info.get("uptime_in_days"),
                "connected_clients": info.get("connected_clients"),
                "used_memory": info.get("used_memory"),
                "used_memory_human": info.get("used_memory_human"),
                "total_connections_received": info.get("total_connections_received"),
                "total_commands_processed": info.get("total_commands_processed"),
            }
            
            # 过滤掉None值
            return {k: v for k, v in key_info.items() if v is not None}
            
        except Exception as e:
            return {"error": str(e)}
