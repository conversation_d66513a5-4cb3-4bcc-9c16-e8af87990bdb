"""
混沌测试调度任务
处理定时、周期性和Cron表达式的任务调度
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from celery import Task
from celery.schedules import crontab

from app.core.celery_app import celery_app
from app.db.session import get_db
from app.services.chaos.task_service import ChaosTaskService
from app.services.chaos.batch_task_service import ChaosBatchTaskService
from app.tasks.chaos.execution_tasks import execute_single_chaos_task, execute_batch_chaos_task

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def schedule_chaos_task(self, task_id: int, schedule_config: Dict[str, Any]):
    """
    调度混沌测试任务
    
    Args:
        task_id: 任务ID
        schedule_config: 调度配置
    """
    return asyncio.run(_schedule_chaos_task_async(task_id, schedule_config))


async def _schedule_chaos_task_async(task_id: int, schedule_config: Dict[str, Any]):
    """异步调度混沌测试任务"""
    try:
        logger.info(f"开始调度混沌测试任务: {task_id}")
        
        async for db in get_db():
            task_service = ChaosTaskService(db)
            
            # 获取任务详情
            task = await task_service.get_task_by_id(task_id)
            if not task:
                raise ValueError(f"任务不存在: {task_id}")
            
            # 检查任务状态
            if task.task_status != "enabled":
                logger.warning(f"任务已禁用，跳过执行: {task_id}")
                return {"task_id": task_id, "status": "skipped", "reason": "task_disabled"}
            
            # 构建执行配置
            execution_config = {
                "host_id": schedule_config.get("host_id"),
                "host_info": schedule_config.get("host_info"),
                "scheduled": True,
                "schedule_time": datetime.now().isoformat()
            }
            
            # 执行任务
            if task.execution_type == "immediate":
                # 立即执行
                result = execute_single_chaos_task.delay(task_id, execution_config)
                logger.info(f"已提交立即执行任务: {task_id}, Celery任务ID: {result.id}")
                
            elif task.execution_type == "scheduled":
                # 定时执行
                if task.scheduled_time and task.scheduled_time > datetime.now():
                    eta = task.scheduled_time
                    result = execute_single_chaos_task.apply_async(
                        args=[task_id, execution_config],
                        eta=eta
                    )
                    logger.info(f"已调度定时执行任务: {task_id}, 执行时间: {eta}, Celery任务ID: {result.id}")
                else:
                    # 立即执行（时间已过）
                    result = execute_single_chaos_task.delay(task_id, execution_config)
                    logger.info(f"定时时间已过，立即执行任务: {task_id}, Celery任务ID: {result.id}")
                    
            elif task.execution_type == "periodic":
                # 周期性执行
                result = _schedule_periodic_task(task_id, execution_config, task.periodic_config)
                logger.info(f"已调度周期性执行任务: {task_id}")
                
            elif task.execution_type == "cron":
                # Cron表达式执行
                result = _schedule_cron_task(task_id, execution_config, task.cron_expression)
                logger.info(f"已调度Cron执行任务: {task_id}")
                
            else:
                raise ValueError(f"不支持的执行类型: {task.execution_type}")
            
            return {
                "task_id": task_id,
                "execution_type": task.execution_type,
                "status": "scheduled",
                "celery_task_id": getattr(result, 'id', None),
                "schedule_time": datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"调度混沌测试任务异常: {task_id}, {str(e)}")
        raise e


@celery_app.task(bind=True)
def schedule_batch_chaos_task(self, batch_task_id: int, schedule_config: Dict[str, Any]):
    """
    调度批次混沌测试任务
    
    Args:
        batch_task_id: 批次任务ID
        schedule_config: 调度配置
    """
    return asyncio.run(_schedule_batch_chaos_task_async(batch_task_id, schedule_config))


async def _schedule_batch_chaos_task_async(batch_task_id: int, schedule_config: Dict[str, Any]):
    """异步调度批次混沌测试任务"""
    try:
        logger.info(f"开始调度批次混沌测试任务: {batch_task_id}")
        
        async for db in get_db():
            batch_service = ChaosBatchTaskService(db)
            
            # 获取批次任务详情
            batch_task = await batch_service.get_batch_task_by_id(batch_task_id)
            if not batch_task:
                raise ValueError(f"批次任务不存在: {batch_task_id}")
            
            # 检查任务状态
            if batch_task.task_status != "enabled":
                logger.warning(f"批次任务已禁用，跳过执行: {batch_task_id}")
                return {"batch_task_id": batch_task_id, "status": "skipped", "reason": "task_disabled"}
            
            # 构建执行配置
            execution_config = {
                "host_id": schedule_config.get("host_id"),
                "host_info": schedule_config.get("host_info"),
                "scheduled": True,
                "schedule_time": datetime.now().isoformat()
            }
            
            # 执行批次任务
            if batch_task.execution_type == "immediate":
                result = execute_batch_chaos_task.delay(batch_task_id, execution_config)
                logger.info(f"已提交立即执行批次任务: {batch_task_id}, Celery任务ID: {result.id}")
                
            elif batch_task.execution_type == "scheduled":
                if batch_task.scheduled_time and batch_task.scheduled_time > datetime.now():
                    eta = batch_task.scheduled_time
                    result = execute_batch_chaos_task.apply_async(
                        args=[batch_task_id, execution_config],
                        eta=eta
                    )
                    logger.info(f"已调度定时执行批次任务: {batch_task_id}, 执行时间: {eta}")
                else:
                    result = execute_batch_chaos_task.delay(batch_task_id, execution_config)
                    logger.info(f"定时时间已过，立即执行批次任务: {batch_task_id}")
                    
            else:
                raise ValueError(f"不支持的执行类型: {batch_task.execution_type}")
            
            return {
                "batch_task_id": batch_task_id,
                "execution_type": batch_task.execution_type,
                "status": "scheduled",
                "celery_task_id": result.id,
                "schedule_time": datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"调度批次混沌测试任务异常: {batch_task_id}, {str(e)}")
        raise e


def _schedule_periodic_task(task_id: int, execution_config: Dict[str, Any], periodic_config: Dict[str, Any]):
    """调度周期性任务"""
    try:
        # 解析周期配置
        interval_type = periodic_config.get("interval_type", "minutes")  # minutes/hours/days
        interval_value = periodic_config.get("interval_value", 60)
        
        # 计算下次执行时间
        if interval_type == "minutes":
            next_run = datetime.now() + timedelta(minutes=interval_value)
        elif interval_type == "hours":
            next_run = datetime.now() + timedelta(hours=interval_value)
        elif interval_type == "days":
            next_run = datetime.now() + timedelta(days=interval_value)
        else:
            raise ValueError(f"不支持的周期类型: {interval_type}")
        
        # 调度下次执行
        result = execute_single_chaos_task.apply_async(
            args=[task_id, execution_config],
            eta=next_run
        )
        
        # 调度下一个周期
        schedule_chaos_task.apply_async(
            args=[task_id, execution_config],
            eta=next_run + timedelta(seconds=1)  # 稍微延迟以避免冲突
        )
        
        return result
        
    except Exception as e:
        logger.error(f"调度周期性任务失败: {task_id}, {str(e)}")
        raise e


def _schedule_cron_task(task_id: int, execution_config: Dict[str, Any], cron_expression: str):
    """调度Cron任务"""
    try:
        # 解析Cron表达式
        cron_parts = cron_expression.split()
        if len(cron_parts) != 5:
            raise ValueError(f"无效的Cron表达式: {cron_expression}")
        
        minute, hour, day, month, day_of_week = cron_parts
        
        # 创建Celery crontab对象
        schedule = crontab(
            minute=minute,
            hour=hour,
            day_of_month=day,
            month_of_year=month,
            day_of_week=day_of_week
        )
        
        # 注册到Celery Beat调度器
        # 注意：这里需要动态注册到beat_schedule中
        # 实际实现中可能需要使用数据库调度器或其他方式
        
        logger.info(f"Cron任务调度配置: {task_id}, 表达式: {cron_expression}")
        
        # 返回调度信息
        return {
            "task_id": task_id,
            "cron_expression": cron_expression,
            "schedule": str(schedule)
        }
        
    except Exception as e:
        logger.error(f"调度Cron任务失败: {task_id}, {str(e)}")
        raise e


@celery_app.task
def check_scheduled_tasks():
    """检查并执行到期的调度任务"""
    return asyncio.run(_check_scheduled_tasks_async())


async def _check_scheduled_tasks_async():
    """异步检查调度任务"""
    try:
        logger.info("开始检查调度任务")
        
        async for db in get_db():
            task_service = ChaosTaskService(db)
            batch_service = ChaosBatchTaskService(db)
            
            current_time = datetime.now()
            
            # 检查单次任务
            scheduled_tasks = await task_service.get_scheduled_tasks(current_time)
            for task in scheduled_tasks:
                logger.info(f"发现到期的调度任务: {task.id}")
                # 这里可以添加执行逻辑
                
            # 检查批次任务
            scheduled_batch_tasks = await batch_service.get_scheduled_batch_tasks(current_time)
            for batch_task in scheduled_batch_tasks:
                logger.info(f"发现到期的批次调度任务: {batch_task.id}")
                # 这里可以添加执行逻辑
            
            return {
                "check_time": current_time.isoformat(),
                "scheduled_tasks_count": len(scheduled_tasks),
                "scheduled_batch_tasks_count": len(scheduled_batch_tasks)
            }
            
    except Exception as e:
        logger.error(f"检查调度任务异常: {str(e)}")
        raise e
