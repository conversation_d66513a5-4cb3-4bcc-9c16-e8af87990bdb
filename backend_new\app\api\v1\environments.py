"""
环境管理API路由
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_current_active_user
from app.schemas.environment import (
    EnvironmentCreate, EnvironmentUpdate, EnvironmentResponse, EnvironmentQuery,
    EnvironmentPageResponse, ConnectionTestRequest, ConnectionTestResponse,
    EnvironmentStatsResponse, SupportedTypeResponse
)
from app.services.environment import EnvironmentService

router = APIRouter()


@router.post("", response_model=EnvironmentResponse, summary="创建环境")
async def create_environment(
    env_data: EnvironmentCreate,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """创建新环境，需要登录权限"""
    env_service = EnvironmentService(db)
    result = await env_service.create_environment(env_data)
    return result


@router.get("", response_model=EnvironmentPageResponse, summary="查询环境列表")
async def list_environments(
    query: EnvironmentQuery = Depends(),
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """查询环境列表，支持关键词搜索、类型筛选和分页"""
    env_service = EnvironmentService(db)
    result = await env_service.list_environments(query)
    return result


@router.get("/{env_id}", response_model=EnvironmentResponse, summary="获取环境详情")
async def get_environment(
    env_id: int,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """获取指定ID的环境详情"""
    env_service = EnvironmentService(db)
    result = await env_service.get_environment_by_id(env_id)
    return result


@router.put("/{env_id}", response_model=EnvironmentResponse, summary="更新环境")
async def update_environment(
    env_id: int,
    env_data: EnvironmentUpdate,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """更新环境信息，支持修改基本信息和配置"""
    env_service = EnvironmentService(db)
    result = await env_service.update_environment(env_id, env_data)
    return result


@router.delete("/{env_id}", status_code=204, summary="删除环境")
async def delete_environment(
    env_id: int,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """删除环境"""
    env_service = EnvironmentService(db)
    await env_service.delete_environment(env_id)
    return None


@router.post("/test", response_model=ConnectionTestResponse, summary="测试环境连接")
async def test_connection(
    test_request: ConnectionTestRequest,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """测试环境连接（通用接口，不更新环境状态）"""
    env_service = EnvironmentService(db)
    result = await env_service.test_connection(test_request)
    return result


@router.post("/{env_id}/test", response_model=ConnectionTestResponse, summary="测试指定环境连接")
async def test_environment_connection(
    env_id: int,
    test_request: ConnectionTestRequest = None,
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """测试指定环境的连接（会更新环境状态）"""
    env_service = EnvironmentService(db)
    result = await env_service.test_environment_connection(env_id, test_request)
    return result


@router.post("/batch-test", response_model=List[Dict[str, Any]], summary="批量测试环境连接")
async def batch_test_connections(
    env_ids: List[int],
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """批量测试环境连接"""
    env_service = EnvironmentService(db)
    results = await env_service.batch_test_connections(env_ids)
    return results


@router.get("/stats/overview", response_model=EnvironmentStatsResponse, summary="获取环境统计信息")
async def get_environment_stats(
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """获取环境统计信息"""
    env_service = EnvironmentService(db)
    stats = await env_service.get_environment_stats()
    return stats


@router.get("/types/supported", response_model=List[SupportedTypeResponse], summary="获取支持的环境类型")
async def get_supported_types(
    db: AsyncSession = Depends(get_db),
    _current_user = Depends(get_current_active_user)
):
    """获取系统支持的环境类型"""
    env_service = EnvironmentService(db)
    types = await env_service.get_supported_types()
    return types
