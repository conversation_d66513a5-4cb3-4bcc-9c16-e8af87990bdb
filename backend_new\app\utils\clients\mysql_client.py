"""
MySQL数据库连接客户端
支持MySQL数据库的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional

from .base_client import BaseClient, ConnectionResult

try:
    import aiomysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False


class MySQLClient(BaseClient):
    """
    MySQL数据库连接客户端
    支持MySQL数据库的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化MySQL客户端
        
        Args:
            config: MySQL连接配置
                - host: MySQL主机地址 (默认: localhost)
                - port: MySQL端口 (默认: 3306)
                - user/username: 用户名
                - password: 密码
                - database/db: 数据库名 (可选)
                - charset: 字符集 (默认: utf8mb4)
                - autocommit: 自动提交 (默认: True)
        """
        super().__init__(config)
        
        if not MYSQL_AVAILABLE:
            raise ImportError("aiomysql库未安装，请运行: pip install aiomysql")
        
        # 设置默认值
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 3306)
        self.user = config.get('user') or config.get('username', 'root')
        self.password = config.get('password', '')
        self.database = config.get('database') or config.get('db')
        self.charset = config.get('charset', 'utf8mb4')
        self.autocommit = config.get('autocommit', True)
        
        self.mysql_connection = None

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立MySQL连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 准备连接参数
            connect_kwargs = {
                'host': self.host,
                'port': self.port,
                'user': self.user,
                'password': self.password,
                'charset': self.charset,
                'autocommit': self.autocommit,
                'connect_timeout': timeout,
            }
            
            # 如果指定了数据库名
            if self.database:
                connect_kwargs['db'] = self.database
            
            # 建立MySQL连接
            self.mysql_connection = await aiomysql.connect(**connect_kwargs)
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到MySQL数据库 {self.host}:{self.port}",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "user": self.user,
                    "database": self.database or "未指定",
                    "charset": self.charset
                }
            )
            
        except asyncio.TimeoutError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MySQL连接超时 ({timeout}秒)",
                duration=duration,
                details={"error_type": "TimeoutError"}
            )
        except aiomysql.Error as e:
            duration = time.time() - start_time
            error_code = getattr(e, 'args', [None])[0] if hasattr(e, 'args') and e.args else None
            
            # 常见MySQL错误码处理
            error_messages = {
                1045: "访问被拒绝 - 用户名或密码错误",
                2003: "无法连接到MySQL服务器 - 服务器未运行或网络问题",
                1049: "数据库不存在",
                1044: "用户没有访问数据库的权限",
                2006: "MySQL服务器已断开连接",
                2013: "查询过程中与MySQL服务器失去连接"
            }
            
            error_msg = error_messages.get(error_code, str(e))
            
            return ConnectionResult(
                success=False,
                message=f"MySQL连接失败: {error_msg}",
                duration=duration,
                details={
                    "error_code": error_code,
                    "error_type": "MySQLError",
                    "host": self.host,
                    "port": self.port
                }
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"MySQL连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开MySQL连接"""
        try:
            if self.mysql_connection:
                self.mysql_connection.close()
                self.mysql_connection = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试MySQL连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        # 如果已连接，先断开
        if self.is_connected:
            await self.disconnect()
        
        # 尝试连接
        result = await self.connect(timeout)
        
        # 如果连接成功，执行一个简单的查询来验证
        if result.success:
            try:
                cursor = await self.mysql_connection.cursor()
                await cursor.execute("SELECT VERSION()")
                version_result = await cursor.fetchone()
                await cursor.close()
                
                if version_result:
                    result.details["mysql_version"] = version_result[0]
                    result.message += f" (版本: {version_result[0]})"
                
            except Exception as e:
                result.details["query_test"] = f"查询测试失败: {str(e)}"
        
        # 测试完成后断开连接
        if result.success:
            await self.disconnect()
        
        return result

    async def execute_query(self, query: str, params: tuple = None) -> Dict[str, Any]:
        """
        执行SQL查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            Dict: 查询结果
        """
        if not self.mysql_connection:
            raise RuntimeError("MySQL未连接")
        
        try:
            cursor = await self.mysql_connection.cursor()
            
            if params:
                await cursor.execute(query, params)
            else:
                await cursor.execute(query)
            
            # 如果是SELECT查询，获取结果
            if query.strip().upper().startswith('SELECT'):
                results = await cursor.fetchall()
                columns = [desc[0] for desc in cursor.description] if cursor.description else []
                
                await cursor.close()
                
                return {
                    "success": True,
                    "results": results,
                    "columns": columns,
                    "row_count": len(results) if results else 0
                }
            else:
                # 对于INSERT, UPDATE, DELETE等操作
                affected_rows = cursor.rowcount
                await cursor.close()
                
                return {
                    "success": True,
                    "affected_rows": affected_rows
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    async def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库信息
        
        Returns:
            Dict: 数据库信息
        """
        if not self.mysql_connection:
            raise RuntimeError("MySQL未连接")
        
        try:
            info = {}
            cursor = await self.mysql_connection.cursor()
            
            # 获取版本信息
            await cursor.execute("SELECT VERSION()")
            version = await cursor.fetchone()
            if version:
                info["version"] = version[0]
            
            # 获取当前数据库
            await cursor.execute("SELECT DATABASE()")
            current_db = await cursor.fetchone()
            if current_db and current_db[0]:
                info["current_database"] = current_db[0]
            
            # 获取字符集
            await cursor.execute("SHOW VARIABLES LIKE 'character_set_server'")
            charset = await cursor.fetchone()
            if charset:
                info["charset"] = charset[1]
            
            await cursor.close()
            return info
            
        except Exception as e:
            return {"error": str(e)}
