"""
Celery应用配置
基于Redis的分布式任务队列系统
"""
from celery import Celery
from celery.schedules import crontab
from kombu import Queue
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# 创建Celery应用实例
celery_app = Celery(
    "dptest_platform",
    broker=f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}",
    backend=f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}",
    include=[
        # 混沌测试任务
        "app.tasks.chaos.execution_tasks",
        "app.tasks.chaos.schedule_tasks",
        "app.tasks.chaos.cleanup_tasks",
        # 数据处理任务
        "app.tasks.data.export_tasks",
        "app.tasks.data.import_tasks",
        "app.tasks.data.analysis_tasks",
        # 系统维护任务
        "app.tasks.system.backup_tasks",
        "app.tasks.system.cleanup_tasks",
        "app.tasks.system.monitor_tasks",
        # 通知任务
        "app.tasks.notification.email_tasks",
        "app.tasks.notification.webhook_tasks"
    ]
)

# Celery配置
celery_app.conf.update(
    # 时区设置
    timezone='Asia/Shanghai',
    enable_utc=True,
    
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    
    # 任务路由
    task_routes={
        # 混沌测试任务路由
        'app.tasks.chaos.execution_tasks.*': {'queue': 'chaos_execution'},
        'app.tasks.chaos.schedule_tasks.*': {'queue': 'chaos_schedule'},
        'app.tasks.chaos.cleanup_tasks.*': {'queue': 'chaos_cleanup'},
        # 数据处理任务路由
        'app.tasks.data.export_tasks.*': {'queue': 'data_export'},
        'app.tasks.data.import_tasks.*': {'queue': 'data_import'},
        'app.tasks.data.analysis_tasks.*': {'queue': 'data_analysis'},
        # 系统维护任务路由
        'app.tasks.system.*': {'queue': 'system_maintenance'},
        # 通知任务路由
        'app.tasks.notification.*': {'queue': 'notifications'},
    },
    
    # 任务结果配置
    result_expires=3600,  # 结果保存1小时
    task_track_started=True,
    task_send_sent_event=True,
    
    # 任务重试配置
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    
    # 工作进程配置
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    
    # 定时任务配置
    beat_schedule={
        # 混沌测试相关定时任务
        'cleanup-expired-faults': {
            'task': 'app.tasks.chaos.cleanup_tasks.cleanup_expired_faults',
            'schedule': 300.0,  # 5分钟
        },
        'check-running-tasks': {
            'task': 'app.tasks.chaos.cleanup_tasks.check_running_tasks_status',
            'schedule': 60.0,  # 1分钟
        },
        'cleanup-completed-executions': {
            'task': 'app.tasks.chaos.cleanup_tasks.cleanup_completed_executions',
            'schedule': 3600.0,  # 1小时
        },
        # 系统维护相关定时任务
        'system-health-check': {
            'task': 'app.tasks.system.monitor_tasks.system_health_check',
            'schedule': 300.0,  # 5分钟
        },
        'daily-backup': {
            'task': 'app.tasks.system.backup_tasks.daily_backup',
            'schedule': crontab(hour=2, minute=0),  # 每天凌晨2点
        },
        'weekly-cleanup': {
            'task': 'app.tasks.system.cleanup_tasks.weekly_cleanup',
            'schedule': crontab(hour=3, minute=0, day_of_week=0),  # 每周日凌晨3点
        },
    },
    beat_schedule_filename='celerybeat-schedule',
)

# 任务装饰器配置
celery_app.conf.task_default_queue = 'default'
celery_app.conf.task_default_exchange = 'default'
celery_app.conf.task_default_exchange_type = 'direct'
celery_app.conf.task_default_routing_key = 'default'

# 队列配置

celery_app.conf.task_queues = (
    # 默认队列
    Queue('default', routing_key='default'),
    # 混沌测试队列
    Queue('chaos_execution', routing_key='chaos_execution'),
    Queue('chaos_schedule', routing_key='chaos_schedule'),
    Queue('chaos_cleanup', routing_key='chaos_cleanup'),
    # 数据处理队列
    Queue('data_export', routing_key='data_export'),
    Queue('data_import', routing_key='data_import'),
    Queue('data_analysis', routing_key='data_analysis'),
    # 系统维护队列
    Queue('system_maintenance', routing_key='system_maintenance'),
    # 通知队列
    Queue('notifications', routing_key='notifications'),
)

@celery_app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f'Request: {self.request!r}')
    return "Debug task completed"

# 任务失败处理
@celery_app.task(bind=True)
def task_failure_handler(self, task_id, error, traceback):
    """任务失败处理器"""
    logger.error(f"Task {task_id} failed: {error}")
    logger.error(f"Traceback: {traceback}")
    
    # 这里可以添加失败通知逻辑
    # 例如：发送邮件、更新数据库状态等
    
    return f"Handled failure for task {task_id}"

# 任务成功处理
@celery_app.task(bind=True)
def task_success_handler(self, retval, task_id, args, kwargs):
    """任务成功处理器"""
    logger.info(f"Task {task_id} completed successfully")
    return f"Handled success for task {task_id}"

# 启动时的初始化
def init_celery_app():
    """初始化Celery应用"""
    logger.info("Initializing Celery application...")
    
    # 这里可以添加启动时的初始化逻辑
    # 例如：检查Redis连接、注册任务等
    
    logger.info("Celery application initialized successfully")
    return celery_app

if __name__ == '__main__':
    celery_app.start()
