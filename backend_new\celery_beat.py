#!/usr/bin/env python3
"""
Celery Beat启动脚本
用于启动Celery定时任务调度器
"""
import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.celery_app import celery_app
from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/celery_beat.log')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """启动Celery Beat"""
    logger.info("启动Celery Beat调度器...")
    logger.info(f"Redis连接: {settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}")
    
    # Celery Beat参数
    beat_args = [
        'beat',
        '--app=app.core.celery_app:celery_app',
        '--loglevel=info',
        '--schedule=celerybeat-schedule',  # 调度文件
        '--pidfile=celery_beat.pid',
        '--logfile=logs/celery_beat.log'
    ]
    
    # 启动Beat
    celery_app.worker_main(beat_args)

if __name__ == '__main__':
    main()
