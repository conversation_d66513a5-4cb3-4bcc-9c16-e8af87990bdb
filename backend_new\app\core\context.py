"""
应用上下文管理
用于在整个请求生命周期中跟踪当前用户等信息
"""
from contextvars import ContextVar
from typing import Optional

# 当前用户ID的上下文变量
current_user_id: ContextVar[Optional[str]] = ContextVar('current_user_id', default=None)


def set_current_user_id(user_id: str) -> None:
    """设置当前用户ID"""
    current_user_id.set(user_id)


def get_current_user_id() -> Optional[str]:
    """获取当前用户ID"""
    return current_user_id.get()


def clear_current_user_id() -> None:
    """清除当前用户ID"""
    current_user_id.set(None)
