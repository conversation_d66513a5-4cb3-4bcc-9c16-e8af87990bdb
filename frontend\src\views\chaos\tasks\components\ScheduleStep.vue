<template>
  <div class="schedule-step">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="step-form"
    >
      <el-form-item label="执行方式" prop="execution_type">
        <el-radio-group
          :model-value="formData.execution_type"
          @update:model-value="(val) => formData.execution_type = val as string"
        >
          <el-radio value="immediate">立即执行</el-radio>
          <el-radio value="scheduled">定时执行</el-radio>
          <!-- <el-radio value="periodic">周期执行</el-radio> -->
          <el-radio value="cron">Cron执行</el-radio>
        </el-radio-group>
        <div class="form-tip">
          选择任务的执行方式：立即执行、单次定时执行或使用Cron表达式执行
        </div>
      </el-form-item>

      <!-- 批次执行模式配置 -->
      <el-form-item
        v-if="formData.task_type === 'batch'"
        label="批次执行模式"
        prop="batch_execution_mode"
      >
        <el-radio-group
          :model-value="formData.batch_execution_mode"
          @update:model-value="(val) => formData.batch_execution_mode = val as string"
        >
          <el-radio value="sequential">依次执行</el-radio>
          <el-radio value="order">顺序执行</el-radio>
        </el-radio-group>
        <div class="form-tip">
          <p v-if="formData.batch_execution_mode === 'sequential'">
            依次执行：一个子任务完成后等待一段时间再执行下一个子任务
          </p>
          <p v-if="formData.batch_execution_mode === 'order'">
            顺序执行：一个子任务完成后立即执行下一个子任务
          </p>
        </div>
      </el-form-item>

      <!-- 定时执行配置 -->
      <el-form-item
        v-if="formData.execution_type === 'scheduled'"
        label="执行时间"
        prop="scheduled_time"
      >
        <el-date-picker
          :model-value="formData.scheduled_time"
          @update:model-value="(val) => formData.scheduled_time = val"
          type="datetime"
          placeholder="选择执行时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DDTHH:mm:ss"
          :disabled-date="disabledDate"
          style="width: 100%"
        />
        <div class="form-tip">
          选择任务的执行时间，必须是未来时间
        </div>
      </el-form-item>

      <!-- 周期执行配置 -->
      <!-- <el-form-item
        v-if="formData.execution_type === 'periodic'"
        label="执行周期"
        prop="periodic_config"
      >
        <div class="periodic-config">
          <el-input-number
            :model-value="formData.periodic_config?.minutes || 0"
            @update:model-value="(val) => updatePeriodicConfig('minutes', val || 0)"
            :min="0"
            :max="59"
            placeholder="分钟"
            style="width: 120px"
          />
          <span class="unit-label">分钟</span>

          <el-input-number
            :model-value="formData.periodic_config?.hours || 0"
            @update:model-value="(val) => updatePeriodicConfig('hours', val || 0)"
            :min="0"
            :max="23"
            placeholder="小时"
            style="width: 120px; margin-left: 10px"
          />
          <span class="unit-label">小时</span>

          <el-input-number
            :model-value="formData.periodic_config?.days || 0"
            @update:model-value="(val) => updatePeriodicConfig('days', val || 0)"
            :min="0"
            :max="365"
            placeholder="天"
            style="width: 120px; margin-left: 10px"
          />
          <span class="unit-label">天</span>
        </div>
        <div class="form-tip">
          设置任务执行的周期间隔，至少设置一个时间单位且不能全为0
        </div>
      </el-form-item> -->

      <!-- Cron执行配置 -->
      <el-form-item
        v-if="formData.execution_type === 'cron'"
        label="Cron表达式"
        prop="cron_expression"
      >
        <div class="cron-config">
          <el-input
            :model-value="formData.cron_expression"
            @update:model-value="(val) => formData.cron_expression = val"
            placeholder="例如: */5 * * * * (每5分钟执行一次)"
            style="width: 100%"
          />
          <div class="cron-templates">
            <span class="template-label">常用模板：</span>
            <el-button
              v-for="template in cronTemplates"
              :key="template.value"
              size="small"
              type="primary"
              link
              @click="formData.cron_expression = template.value"
            >
              {{ template.label }}
            </el-button>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="自动销毁" prop="auto_destroy">
        <el-switch
          :model-value="formData.auto_destroy"
          @update:model-value="(val) => formData.auto_destroy = val as boolean"
          active-text="开启"
          inactive-text="关闭"
        />
        <div class="form-tip">
          开启后，故障注入会在指定时间后自动销毁
        </div>
      </el-form-item>

      <el-form-item
        v-if="formData.auto_destroy"
        label="任务超时时间"
        prop="max_duration"
      >
        <el-input-number
          :model-value="formData.max_duration"
          @update:model-value="(val) => formData.max_duration = val"
          :min="60"
          :max="3600"
          :step="60"
          placeholder="秒"
          style="width: 200px"
        />
        <span style="margin-left: 10px; color: #909399">秒</span>
        <div class="form-tip">
          整个混沌测试任务的最大执行时间，包括故障注入、持续和恢复的总时长（60-3600秒）
        </div>
      </el-form-item>

      <!-- 高级配置 -->
      <div class="advanced-config">
        <h4 class="section-title">高级配置</h4>
        
        <!-- 单次任务：故障注入超时 -->
        <el-form-item v-if="!isBatch && formData.fault_params" label="故障注入超时">
          <el-input-number
            :model-value="formData.fault_params!.timeout"
            @update:model-value="(val) => formData.fault_params!.timeout = val"
            :min="30"
            :max="1800"
            :step="30"
            placeholder="超时时间"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399">秒</span>
          <div class="form-tip">
            单个故障注入操作的超时时间，超时后该操作失败但任务继续（30-1800秒）
          </div>
        </el-form-item>

        <el-form-item label="重试次数">
          <el-input-number
            :model-value="retryCount"
            @update:model-value="(val) => retryCount = val || 0"
            :min="0"
            :max="5"
            :step="1"
            style="width: 200px"
          />
          <div class="form-tip">
            故障注入失败时的重试次数，默认0次
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  formData: {
    task_type?: 'single' | 'batch'
    execution_type?: string
    batch_execution_mode?: string
    scheduled_time?: string
    periodic_config?: {
      minutes?: number
      hours?: number
      days?: number
    }
    cron_expression?: string
    auto_destroy?: boolean
    max_duration?: number
    fault_params?: Record<string, any>
  }
  isBatch?: boolean
}

const props = defineProps<Props>()

const formRef = ref<FormInstance>()
const retryCount = ref(0)

// 计算属性
const isBatch = computed(() => props.isBatch || false)

// Cron表达式模板
const cronTemplates = [
  { label: '每分钟', value: '*/1 * * * *' },
  { label: '每5分钟', value: '*/5 * * * *' },
  { label: '每小时', value: '0 */1 * * *' },
  { label: '每天上午9点', value: '0 9 * * *' },
  { label: '工作日上午9点', value: '0 9 * * 1-5' },
  { label: '每周一上午9点', value: '0 9 * * 1' }
]

// 更新周期配置
const updatePeriodicConfig = (key: 'minutes' | 'hours' | 'days', value: number) => {
  if (!props.formData.periodic_config) {
    props.formData.periodic_config = {}
  }
  props.formData.periodic_config[key] = value
}

// 禁用过去的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 表单验证规则
const rules: FormRules = {
  execution_type: [
    { required: true, message: '请选择执行方式', trigger: 'change' }
  ],
  scheduled_time: [
    {
      required: true,
      message: '请选择执行时间',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (props.formData.execution_type === 'scheduled' && !value) {
          callback(new Error('请选择执行时间'))
        } else {
          callback()
        }
      }
    }
  ],
  periodic_config: [
    {
      validator: (rule, value, callback) => {
        if (props.formData.execution_type === 'periodic') {
          const config = props.formData.periodic_config
          if (!config || (!config.minutes && !config.hours && !config.days)) {
            callback(new Error('请设置至少一个时间间隔'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  cron_expression: [
    {
      validator: (rule, value, callback) => {
        if (props.formData.execution_type === 'cron') {
          if (!value) {
            callback(new Error('请输入Cron表达式'))
          } else {
            // 简单的Cron表达式格式验证
            const parts = value.trim().split(/\s+/)
            if (parts.length !== 5 && parts.length !== 6) {
              callback(new Error('Cron表达式格式错误，应为5位或6位格式'))
            } else {
              callback()
            }
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  max_duration: [
    { required: true, message: '请设置任务超时时间', trigger: 'blur' },
    { type: 'number', min: 60, max: 3600, message: '任务超时时间范围为60-3600秒', trigger: 'blur' }
  ]
}

// 动态验证规则
const dynamicRules = computed(() => {
  const baseRules = { ...rules }
  
  // 如果是定时执行，则需要验证执行时间
  if (formRef.value?.model?.execution_type === 'scheduled') {
    baseRules.scheduled_time = [
      { required: true, message: '请选择执行时间', trigger: 'change' }
    ]
  }
  
  // 如果开启自动销毁，则需要验证任务超时时间
  if (formRef.value?.model?.auto_destroy) {
    baseRules.max_duration = [
      { required: true, message: '请设置任务超时时间', trigger: 'blur' },
      { type: 'number', min: 60, max: 3600, message: '任务超时时间范围为60-3600秒', trigger: 'blur' }
    ]
  }
  
  return baseRules
})

// 暴露验证方法
const validate = () => {
  return formRef.value?.validate()
}

defineExpose({
  validate
})
</script>

<style scoped>
.schedule-step {
  padding: 20px 0;
}

.step-form {
  max-width: 600px;
  margin: 0 auto;
}

.advanced-config {
  margin-top: 30px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 5px;
  line-height: 1.4;
}

.periodic-config {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.unit-label {
  margin-left: 5px;
  color: #909399;
  font-size: 14px;
}

.cron-config {
  width: 100%;
}

.cron-templates {
  margin-top: 10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.template-label {
  font-size: 12px;
  color: #909399;
  margin-right: 5px;
}
</style>
