"""
内存故障处理器
实现内存相关的故障注入和销毁
"""
import logging
from typing import Dict, Any
from .base_fault import BaseFaultHandler, FaultResult

logger = logging.getLogger(__name__)


class MemoryFaultHandler(BaseFaultHandler):
    """内存故障处理器"""
    
    async def inject(
        self, 
        ssh_client, 
        fault_params: Dict[str, Any],
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """
        注入内存故障
        
        支持的参数:
        - mem-percent: 内存使用率百分比 (0-100)
        - size: 占用内存大小 (如: 1G, 500M)
        - timeout: 持续时间(秒)
        """
        # 验证参数
        is_valid, error_msg = self.validate_params(fault_params)
        if not is_valid:
            return FaultResult(
                success=False,
                fault_id="",
                message=f"参数验证失败: {error_msg}"
            )
        
        # 构建命令参数
        cmd_params = {}
        
        # 内存使用率
        if "mem_percent" in fault_params:
            cmd_params["mem-percent"] = fault_params["mem_percent"]
        
        # 内存大小
        if "size" in fault_params:
            cmd_params["size"] = fault_params["size"]
        
        # 持续时间
        if "timeout" in fault_params:
            cmd_params["timeout"] = fault_params["timeout"]
        
        # 构建ChaosBlade命令
        command = self._build_chaosblade_command(
            action="create",
            target="mem",
            params=cmd_params,
            chaosblade_path=chaosblade_path
        )
        
        # 执行命令
        return await self._execute_chaosblade_command(ssh_client, command)
    
    async def destroy(
        self, 
        ssh_client, 
        fault_id: str,
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """销毁内存故障"""
        command = self._build_destroy_command(fault_id, chaosblade_path)
        return await self._execute_chaosblade_command(ssh_client, command)
    
    def validate_params(self, fault_params: Dict[str, Any]) -> tuple[bool, str]:
        """验证内存故障参数"""
        # 必须指定mem_percent或size中的一个
        if "mem_percent" not in fault_params and "size" not in fault_params:
            return False, "必须指定mem_percent或size参数"
        
        # 验证内存使用率
        mem_percent = fault_params.get("mem_percent")
        if mem_percent is not None:
            if not isinstance(mem_percent, (int, float)) or not (0 <= mem_percent <= 100):
                return False, "mem_percent必须是0-100之间的数字"
        
        # 验证内存大小
        size = fault_params.get("size")
        if size is not None:
            if not isinstance(size, str) or not self._validate_size_format(size):
                return False, "size格式错误，应为如'1G'、'500M'的格式"
        
        # 验证超时时间
        timeout = fault_params.get("timeout")
        if timeout is not None:
            if not isinstance(timeout, int) or timeout <= 0:
                return False, "timeout必须是正整数"
        
        return True, ""
    
    def _validate_size_format(self, size: str) -> bool:
        """验证内存大小格式"""
        import re
        # 支持格式: 数字+单位(B/K/M/G/T)
        pattern = r'^\d+[BKMGT]?$'
        return bool(re.match(pattern, size.upper()))
    
    def get_param_schema(self) -> Dict[str, Any]:
        """获取内存故障参数结构定义"""
        return {
            "type": "object",
            "properties": {
                "mem_percent": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 100,
                    "description": "内存使用率百分比",
                    "required": False
                },
                "size": {
                    "type": "string",
                    "pattern": r"^\d+[BKMGT]?$",
                    "description": "占用内存大小，如1G、500M",
                    "required": False
                },
                "timeout": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "持续时间(秒)，不指定则持续到手动销毁",
                    "required": False
                }
            },
            "anyOf": [
                {"required": ["mem_percent"]},
                {"required": ["size"]}
            ],
            "additionalProperties": False
        }
