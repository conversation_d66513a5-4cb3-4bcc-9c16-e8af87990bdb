"""
混沌测试执行记录Schema定义
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from app.schemas.base import BaseResponseSchema, BaseSchema


class ChaosExecutionBase(BaseSchema):
    """混沌测试执行记录基础Schema"""
    task_id: Optional[int] = Field(None, description="单次任务ID")
    batch_task_id: Optional[int] = Field(None, description="批次任务ID")
    batch_task_item_id: Optional[int] = Field(None, description="批次任务子项ID")
    host_id: Optional[int] = Field(None, description="目标主机ID")
    host_info: Dict[str, Any] = Field(..., description="主机连接信息")
    fault_config: Dict[str, Any] = Field(..., description="故障配置信息")
    status: str = Field(default="pending", description="执行状态")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    destroy_time: Optional[datetime] = Field(None, description="销毁时间")
    chaos_uid: Optional[str] = Field(None, description="ChaosBlade故障UID")
    command: Optional[str] = Field(None, description="执行的命令")
    output: Optional[str] = Field(None, description="命令输出")
    error_message: Optional[str] = Field(None, description="错误信息")
    is_auto_destroyed: bool = Field(default=False, description="是否自动销毁")
    destroy_output: Optional[str] = Field(None, description="销毁命令输出")
    celery_task_id: Optional[str] = Field(None, description="Celery任务ID")

    @validator('status')
    def validate_status(cls, v):
        valid_statuses = ['pending', 'running', 'success', 'failed', 'timeout', 'destroyed']
        if v not in valid_statuses:
            raise ValueError(f'执行状态必须是 {valid_statuses} 中的一个')
        return v


class ChaosExecutionCreate(ChaosExecutionBase):
    """创建混沌测试执行记录Schema"""
    pass


class ChaosExecutionUpdate(BaseSchema):
    """更新混沌测试执行记录Schema"""
    status: Optional[str] = Field(None, description="执行状态")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    destroy_time: Optional[datetime] = Field(None, description="销毁时间")
    chaos_uid: Optional[str] = Field(None, description="ChaosBlade故障UID")
    command: Optional[str] = Field(None, description="执行的命令")
    output: Optional[str] = Field(None, description="命令输出")
    error_message: Optional[str] = Field(None, description="错误信息")
    is_auto_destroyed: Optional[bool] = Field(None, description="是否自动销毁")
    destroy_output: Optional[str] = Field(None, description="销毁命令输出")
    celery_task_id: Optional[str] = Field(None, description="Celery任务ID")


class ChaosExecutionResponse(ChaosExecutionBase, BaseResponseSchema):
    """混沌测试执行记录响应Schema"""
    duration: Optional[int] = Field(None, description="执行持续时间(秒)")
    
    class Config:
        from_attributes = True

    @property
    def duration(self) -> Optional[int]:
        """计算执行持续时间"""
        if self.start_time and self.end_time:
            return int((self.end_time - self.start_time).total_seconds())
        return None


class ChaosExecutionQuery(BaseSchema):
    """混沌测试执行记录查询Schema"""
    task_id: Optional[int] = Field(None, description="任务ID筛选")
    batch_task_id: Optional[int] = Field(None, description="批次任务ID筛选")
    status: Optional[str] = Field(None, description="执行状态筛选")
    chaos_uid: Optional[str] = Field(None, description="ChaosBlade UID搜索")
    start_date: Optional[datetime] = Field(None, description="开始时间筛选(起)")
    end_date: Optional[datetime] = Field(None, description="开始时间筛选(止)")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")

    @property
    def offset(self) -> int:
        return (self.page - 1) * self.size


class ChaosExecutionPageResponse(BaseSchema):
    """混沌测试执行记录分页响应Schema"""
    items: List[ChaosExecutionResponse] = Field(..., description="执行记录列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    pages: int = Field(..., description="总页数")


class ChaosExecutionStatsResponse(BaseSchema):
    """混沌测试执行记录统计响应Schema"""
    total_executions: int = Field(..., description="总执行数")
    running_executions: int = Field(..., description="运行中执行数")
    success_executions: int = Field(..., description="成功执行数")
    failed_executions: int = Field(..., description="失败执行数")
    today_executions: int = Field(..., description="今日执行数")
    avg_duration: Optional[float] = Field(None, description="平均执行时长(秒)")
    success_rate: float = Field(..., description="成功率")
    status_stats: Dict[str, int] = Field(..., description="状态统计")
    fault_type_stats: Dict[str, int] = Field(..., description="故障类型统计")


class ChaosExecutionDestroyRequest(BaseSchema):
    """混沌测试执行销毁请求Schema"""
    execution_ids: List[int] = Field(..., min_items=1, description="要销毁的执行记录ID列表")
    force_destroy: bool = Field(default=False, description="是否强制销毁")


class ChaosExecutionDestroyResponse(BaseSchema):
    """混沌测试执行销毁响应Schema"""
    total_count: int = Field(..., description="总数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    results: List[Dict[str, Any]] = Field(..., description="详细结果")
    message: str = Field(..., description="操作结果消息")
