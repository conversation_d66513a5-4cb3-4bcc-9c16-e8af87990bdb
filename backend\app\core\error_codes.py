"""
统一错误码定义
错误码格式: MTTSS
- M: 模块编码 (1位)
- TT: 错误类型编码 (2位)  
- SS: 序号编码 (2位)
"""
from enum import IntEnum


class ErrorCode(IntEnum):
    """统一错误码枚举"""
    
    # ==================== 系统通用错误 (9xxxx) ====================
    
    # 系统验证错误 (901xx)
    INVALID_REQUEST_PARAMS = 90101  # 请求参数无效
    INVALID_JSON_FORMAT = 90102     # JSON格式错误
    MISSING_REQUIRED_FIELD = 90103  # 缺少必填字段
    FIELD_VALUE_INVALID = 90104     # 字段值无效
    FIELD_LENGTH_INVALID = 90105    # 字段长度无效
    
    # 系统权限错误 (902xx)
    UNAUTHORIZED = 90201            # 未授权访问
    TOKEN_INVALID = 90202           # 令牌无效
    TOKEN_EXPIRED = 90203           # 令牌已过期
    PERMISSION_DENIED = 90204       # 权限不足
    ACCESS_FORBIDDEN = 90205        # 访问被禁止
    
    # 系统资源错误 (903xx)
    RESOURCE_NOT_FOUND = 90301      # 资源不存在
    ENDPOINT_NOT_FOUND = 90302      # 接口不存在
    METHOD_NOT_ALLOWED = 90303      # 请求方法不允许
    
    # 系统业务错误 (904xx)
    OPERATION_FAILED = 90401        # 操作失败
    RESOURCE_CONFLICT = 90402       # 资源冲突
    OPERATION_NOT_ALLOWED = 90403   # 操作不被允许
    RATE_LIMIT_EXCEEDED = 90404     # 请求频率超限
    
    # 系统内部错误 (905xx)
    INTERNAL_SERVER_ERROR = 90501   # 服务器内部错误
    DATABASE_ERROR = 90502          # 数据库错误
    EXTERNAL_SERVICE_ERROR = 90503  # 外部服务错误
    CONFIGURATION_ERROR = 90504     # 配置错误
    NETWORK_ERROR = 90505           # 网络错误
    
    # ==================== 用户模块错误 (1xxxx) ====================
    
    # 用户验证错误 (101xx)
    USER_USERNAME_INVALID = 10101   # 用户名格式无效
    USER_PASSWORD_INVALID = 10102   # 密码格式无效
    USER_EMAIL_INVALID = 10103      # 邮箱格式无效
    USER_PHONE_INVALID = 10104      # 手机号格式无效
    USER_NICKNAME_INVALID = 10105   # 昵称格式无效
    
    # 用户权限错误 (102xx)
    USER_LOGIN_REQUIRED = 10201     # 需要登录
    USER_LOGIN_FAILED = 10202       # 登录失败
    USER_PASSWORD_WRONG = 10203     # 密码错误
    USER_ACCOUNT_DISABLED = 10204   # 账户已禁用
    USER_ACCOUNT_LOCKED = 10205     # 账户已锁定
    
    # 用户资源错误 (103xx)
    USER_NOT_FOUND = 10301          # 用户不存在
    USER_ROLE_NOT_FOUND = 10302     # 角色不存在
    
    # 用户业务错误 (104xx)
    USER_ALREADY_EXISTS = 10401     # 用户已存在
    USER_EMAIL_ALREADY_EXISTS = 10402  # 邮箱已存在
    USER_CANNOT_DELETE_SELF = 10403 # 不能删除自己
    USER_CANNOT_MODIFY_ADMIN = 10404  # 不能修改管理员
    
    # ==================== 环境模块错误 (2xxxx) ====================
    
    # 环境验证错误 (201xx)
    ENV_NAME_INVALID = 20101        # 环境名称无效
    ENV_TYPE_INVALID = 20102        # 环境类型无效
    ENV_CONFIG_INVALID = 20103      # 环境配置无效
    ENV_HOST_INVALID = 20104        # 主机地址无效
    ENV_PORT_INVALID = 20105        # 端口号无效
    
    # 环境权限错误 (202xx)
    ENV_ACCESS_DENIED = 20201       # 环境访问被拒绝
    ENV_OPERATION_FORBIDDEN = 20202 # 环境操作被禁止
    
    # 环境资源错误 (203xx)
    ENV_NOT_FOUND = 20301           # 环境不存在
    ENV_CONFIG_NOT_FOUND = 20302    # 环境配置不存在
    
    # 环境业务错误 (204xx)
    ENV_NAME_ALREADY_EXISTS = 20401 # 环境名称已存在
    ENV_CONNECTION_FAILED = 20402   # 环境连接失败
    ENV_IN_USE = 20403              # 环境正在使用中
    ENV_TEST_FAILED = 20404         # 环境测试失败
    
    # ==================== 混沌测试模块错误 (3xxxx) ====================
    
    # 混沌测试验证错误 (301xx)
    CHAOS_TASK_NAME_INVALID = 30101     # 任务名称无效
    CHAOS_FAULT_TYPE_INVALID = 30102    # 故障类型无效
    CHAOS_PARAMS_INVALID = 30103        # 故障参数无效
    CHAOS_SCHEDULE_INVALID = 30104      # 调度配置无效
    
    # 混沌测试权限错误 (302xx)
    CHAOS_TASK_ACCESS_DENIED = 30201    # 任务访问被拒绝
    CHAOS_EXECUTION_FORBIDDEN = 30202   # 执行被禁止
    
    # 混沌测试资源错误 (303xx)
    CHAOS_TASK_NOT_FOUND = 30301        # 任务不存在
    CHAOS_SCENARIO_NOT_FOUND = 30302    # 场景不存在
    CHAOS_EXECUTION_NOT_FOUND = 30303   # 执行记录不存在
    
    # 混沌测试业务错误 (304xx)
    CHAOS_TASK_ALREADY_RUNNING = 30401  # 任务已在运行
    CHAOS_TASK_CANNOT_EXECUTE = 30402   # 任务无法执行
    CHAOS_BLADE_NOT_INSTALLED = 30403   # ChaosBlade未安装
    CHAOS_EXECUTION_FAILED = 30404      # 执行失败
    
    # ==================== 数据工厂模块错误 (4xxxx) ====================
    
    # 数据工厂验证错误 (401xx)
    DATA_MODEL_NAME_INVALID = 40101     # 数据模型名称无效
    DATA_FIELD_CONFIG_INVALID = 40102   # 字段配置无效
    DATA_GENERATION_PARAMS_INVALID = 40103  # 生成参数无效
    
    # 数据工厂权限错误 (402xx)
    DATA_MODEL_ACCESS_DENIED = 40201    # 数据模型访问被拒绝
    DATA_GENERATION_FORBIDDEN = 40202   # 数据生成被禁止
    
    # 数据工厂资源错误 (403xx)
    DATA_MODEL_NOT_FOUND = 40301        # 数据模型不存在
    DATA_GENERATION_TASK_NOT_FOUND = 40302  # 生成任务不存在
    
    # 数据工厂业务错误 (404xx)
    DATA_MODEL_NAME_EXISTS = 40401      # 数据模型名称已存在
    DATA_GENERATION_FAILED = 40402      # 数据生成失败
    DATA_EXPORT_FAILED = 40403          # 数据导出失败
    
    # ==================== 模型配置模块错误 (5xxxx) ====================
    
    # 模型配置验证错误 (501xx)
    MODEL_NAME_INVALID = 50101          # 模型名称无效
    MODEL_PLATFORM_INVALID = 50102      # 模型平台无效
    MODEL_API_URL_INVALID = 50103       # API地址无效
    MODEL_API_KEY_INVALID = 50104       # API密钥无效
    MODEL_CONFIG_INVALID = 50105        # 模型配置无效
    
    # 模型配置权限错误 (502xx)
    MODEL_ACCESS_DENIED = 50201         # 模型访问被拒绝
    MODEL_CALL_FORBIDDEN = 50202        # 模型调用被禁止
    
    # 模型配置资源错误 (503xx)
    MODEL_NOT_FOUND = 50301             # 模型不存在
    MODEL_CONFIG_NOT_FOUND = 50302      # 模型配置不存在
    
    # 模型配置业务错误 (504xx)
    MODEL_NAME_ALREADY_EXISTS = 50401   # 模型名称已存在
    MODEL_CONNECTION_FAILED = 50402     # 模型连接失败
    MODEL_CALL_FAILED = 50403           # 模型调用失败
    MODEL_HEALTH_CHECK_FAILED = 50404   # 健康检查失败


# 错误码到HTTP状态码的映射
ERROR_CODE_TO_HTTP_STATUS = {
    # 验证错误 -> 400 Bad Request
    **{code: 400 for code in ErrorCode if str(code).endswith(('01', '02', '03', '04', '05')) and str(code)[1:3] == '01'},
    
    # 权限错误 -> 401 Unauthorized / 403 Forbidden
    ErrorCode.UNAUTHORIZED: 401,
    ErrorCode.TOKEN_INVALID: 401,
    ErrorCode.TOKEN_EXPIRED: 401,
    ErrorCode.USER_LOGIN_REQUIRED: 401,
    ErrorCode.USER_LOGIN_FAILED: 401,
    ErrorCode.USER_PASSWORD_WRONG: 401,
    **{code: 403 for code in ErrorCode if str(code)[1:3] == '02' and code not in [90201, 90202, 90203, 10201, 10202, 10203]},
    
    # 资源不存在错误 -> 404 Not Found
    **{code: 404 for code in ErrorCode if str(code)[1:3] == '03'},
    
    # 业务逻辑错误 -> 409 Conflict / 422 Unprocessable Entity
    ErrorCode.RESOURCE_CONFLICT: 409,
    ErrorCode.USER_ALREADY_EXISTS: 409,
    ErrorCode.USER_EMAIL_ALREADY_EXISTS: 409,
    ErrorCode.ENV_NAME_ALREADY_EXISTS: 409,
    ErrorCode.DATA_MODEL_NAME_EXISTS: 409,
    ErrorCode.MODEL_NAME_ALREADY_EXISTS: 409,
    **{code: 422 for code in ErrorCode if str(code)[1:3] == '04' and code not in [90402, 10401, 10402, 20401, 40401, 50401]},
    
    # 系统内部错误 -> 500 Internal Server Error
    **{code: 500 for code in ErrorCode if str(code)[1:3] == '05'},
}


def get_http_status_code(error_code: ErrorCode) -> int:
    """根据错误码获取对应的HTTP状态码"""
    return ERROR_CODE_TO_HTTP_STATUS.get(error_code, 400)
