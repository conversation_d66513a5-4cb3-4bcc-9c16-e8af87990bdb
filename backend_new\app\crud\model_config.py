"""
模型配置CRUD操作
"""
from typing import Optional, List, Tuple, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, update
from app.crud.base import CRUDBase
from app.models.model_config import ModelConfig
from app.schemas.model_config import ModelConfigCreate, ModelConfigUpdate


class ModelConfigCRUD(CRUDBase[ModelConfig, ModelConfigCreate, ModelConfigUpdate]):
    """模型配置CRUD操作类"""

    def __init__(self, db: AsyncSession):
        super().__init__(ModelConfig)
        self.db = db

    async def get_by_name(self, name: str) -> Optional[ModelConfig]:
        """根据名称查询模型配置"""
        result = await self.db.execute(
            select(ModelConfig).where(ModelConfig.name == name)
        )
        return result.scalar_one_or_none()

    async def list(
        self,
        keyword: Optional[str] = None,
        platform: Optional[str] = None,
        status: Optional[str] = None,
        health_status: Optional[str] = None,
        offset: int = 0,
        limit: int = 10
    ) -> Tuple[List[ModelConfig], int]:
        """查询模型配置列表（带筛选和分页）"""
        # 基础查询
        query = select(ModelConfig)

        # 筛选条件
        if keyword:
            query = query.where(
                or_(
                    ModelConfig.name.ilike(f"%{keyword}%"),
                    ModelConfig.description.ilike(f"%{keyword}%"),
                    ModelConfig.model_name.ilike(f"%{keyword}%")
                )
            )

        if platform:
            query = query.where(ModelConfig.platform == platform)

        if status:
            query = query.where(ModelConfig.status == status)

        if health_status:
            query = query.where(ModelConfig.health_status == health_status)

        # 计算总条数
        total_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(total_query)
        total = total_result.scalar()

        # 分页查询
        query = query.offset(offset).limit(limit).order_by(ModelConfig.created_at.desc())
        result = await self.db.execute(query)
        items = result.scalars().all()

        return items, total

    async def get_available_models(self) -> List[ModelConfig]:
        """获取所有可用的模型（启用且健康）"""
        result = await self.db.execute(
            select(ModelConfig).where(
                ModelConfig.status == 'enabled',
                ModelConfig.health_status == 'healthy'
            ).order_by(ModelConfig.created_at.desc())
        )
        return result.scalars().all()

    async def get_enabled_models(self) -> List[ModelConfig]:
        """获取所有启用的模型"""
        result = await self.db.execute(
            select(ModelConfig).where(
                ModelConfig.status == 'enabled'
            ).order_by(ModelConfig.created_at.desc())
        )
        return result.scalars().all()

    async def get_models_by_ids(self, model_ids: List[int]) -> List[ModelConfig]:
        """根据ID列表获取模型配置"""
        result = await self.db.execute(
            select(ModelConfig).where(ModelConfig.id.in_(model_ids))
        )
        return result.scalars().all()

    async def update_health_status(
        self, 
        model_id: int, 
        health_status: str, 
        last_check_time: Optional[str] = None
    ) -> bool:
        """更新模型健康状态"""
        update_data = {"health_status": health_status}
        if last_check_time:
            update_data["last_health_check"] = last_check_time

        result = await self.db.execute(
            update(ModelConfig)
            .where(ModelConfig.id == model_id)
            .values(**update_data)
        )
        await self.db.commit()
        return result.rowcount > 0

    async def batch_update_health_status(self, status_updates: List[Dict[str, Any]]) -> int:
        """批量更新模型健康状态"""
        if not status_updates:
            return 0
        
        updated_count = 0
        for update_data in status_updates:
            model_id = update_data.pop("id")
            result = await self.db.execute(
                update(ModelConfig).where(ModelConfig.id == model_id).values(**update_data)
            )
            updated_count += result.rowcount
        
        await self.db.commit()
        return updated_count

    async def enable_model(self, model_id: int) -> Optional[ModelConfig]:
        """启用模型"""
        model = await self.get(self.db, model_id)
        if model:
            model.enable()
            await self.db.commit()
            await self.db.refresh(model)
        return model

    async def disable_model(self, model_id: int) -> Optional[ModelConfig]:
        """停用模型"""
        model = await self.get(self.db, model_id)
        if model:
            model.disable()
            await self.db.commit()
            await self.db.refresh(model)
        return model

    async def get_platform_stats(self) -> Dict[str, int]:
        """获取平台统计"""
        result = await self.db.execute(
            select(ModelConfig.platform, func.count(ModelConfig.id))
            .group_by(ModelConfig.platform)
        )
        return {row[0]: row[1] for row in result.fetchall()}

    async def get_status_stats(self) -> Dict[str, int]:
        """获取状态统计"""
        result = await self.db.execute(
            select(ModelConfig.status, func.count(ModelConfig.id))
            .group_by(ModelConfig.status)
        )
        return {row[0]: row[1] for row in result.fetchall()}

    async def get_health_stats(self) -> Dict[str, int]:
        """获取健康状态统计"""
        result = await self.db.execute(
            select(ModelConfig.health_status, func.count(ModelConfig.id))
            .group_by(ModelConfig.health_status)
        )
        return {row[0]: row[1] for row in result.fetchall()}

    async def get_recent_models(self, limit: int = 5) -> List[ModelConfig]:
        """获取最近创建的模型"""
        result = await self.db.execute(
            select(ModelConfig)
            .order_by(ModelConfig.created_at.desc())
            .limit(limit)
        )
        return result.scalars().all()

    async def create_model_config(self, model_data: ModelConfigCreate) -> ModelConfig:
        """创建模型配置"""
        # 创建模型数据字典
        model_dict = model_data.model_dump()

        # 处理API Key加密（简化版本，实际应该使用加密）
        if model_dict.get('api_key'):
            # 这里应该使用真正的加密算法
            model_dict['api_key'] = model_dict['api_key']  # 暂时不加密

        # 创建模型实例
        db_model = ModelConfig(**model_dict)
        self.db.add(db_model)
        await self.db.commit()
        await self.db.refresh(db_model)

        return db_model

    async def update_model_config(self, model_id: int, model_data: ModelConfigUpdate) -> Optional[ModelConfig]:
        """更新模型配置"""
        model = await self.get(self.db, model_id)
        if not model:
            return None

        # 更新数据
        update_dict = model_data.model_dump(exclude_unset=True)

        # 处理API Key加密
        if 'api_key' in update_dict and update_dict['api_key']:
            # 这里应该使用真正的加密算法
            update_dict['api_key'] = update_dict['api_key']  # 暂时不加密

        # 更新字段
        for field, value in update_dict.items():
            if hasattr(model, field):
                setattr(model, field, value)

        await self.db.commit()
        await self.db.refresh(model)

        return model
