<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量操作"
    width="600px"
    :before-close="handleClose"
    class="batch-operation-dialog"
  >
    <div class="dialog-content">
      <!-- 选中任务信息 -->
      <div class="selected-tasks-section">
        <div class="section-header">
          <h4>已选中任务</h4>
          <el-tag type="info">{{ selectedTasks.length }} 个任务</el-tag>
        </div>
        
        <div class="tasks-list">
          <div
            v-for="task in selectedTasks"
            :key="task.id"
            class="task-item"
          >
            <div class="task-info">
              <span class="task-name">{{ task.name }}</span>
              <TaskStatusTag :status="task.status" />
            </div>
            <div class="task-meta">
              <span class="task-id">ID: {{ task.id }}</span>
              <span class="task-time">{{ formatDateTime(task.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作选择 -->
      <div class="operation-section">
        <div class="section-header">
          <h4>选择操作</h4>
        </div>
        
        <el-radio-group v-model="selectedOperation" class="operation-list">
          <div class="operation-item">
            <el-radio label="cancel" :disabled="!canCancel">
              <div class="operation-content">
                <div class="operation-icon cancel">
                  <el-icon><Close /></el-icon>
                </div>
                <div class="operation-info">
                  <div class="operation-name">批量取消</div>
                  <div class="operation-desc">取消选中的等待中或执行中任务</div>
                </div>
              </div>
            </el-radio>
          </div>

          <div class="operation-item">
            <el-radio label="delete" :disabled="!canDelete">
              <div class="operation-content">
                <div class="operation-icon delete">
                  <el-icon><Delete /></el-icon>
                </div>
                <div class="operation-info">
                  <div class="operation-name">批量删除</div>
                  <div class="operation-desc">删除选中的已完成、失败或已取消任务</div>
                </div>
              </div>
            </el-radio>
          </div>

          <div class="operation-item">
            <el-radio label="retry" :disabled="!canRetry">
              <div class="operation-content">
                <div class="operation-icon retry">
                  <el-icon><RefreshRight /></el-icon>
                </div>
                <div class="operation-info">
                  <div class="operation-name">批量重试</div>
                  <div class="operation-desc">重新执行选中的失败任务</div>
                </div>
              </div>
            </el-radio>
          </div>

          <div class="operation-item">
            <el-radio label="download" :disabled="!canDownload">
              <div class="operation-content">
                <div class="operation-icon download">
                  <el-icon><Download /></el-icon>
                </div>
                <div class="operation-info">
                  <div class="operation-name">批量下载</div>
                  <div class="operation-desc">下载选中的已完成任务结果文件</div>
                </div>
              </div>
            </el-radio>
          </div>
        </el-radio-group>
      </div>

      <!-- 操作确认 -->
      <div v-if="selectedOperation" class="confirmation-section">
        <div class="section-header">
          <h4>操作确认</h4>
        </div>
        
        <el-alert
          :title="confirmationMessage"
          :type="confirmationType"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="confirmation-details">
              <p>{{ confirmationDetails }}</p>
              <div v-if="selectedOperation === 'delete'" class="warning-text">
                <el-icon><WarningFilled /></el-icon>
                <span>此操作不可恢复，请谨慎操作！</span>
              </div>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 操作统计 -->
      <div class="statistics-section">
        <div class="section-header">
          <h4>操作统计</h4>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getOperableCount('cancel') }}</div>
              <div class="stat-label">可取消</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getOperableCount('delete') }}</div>
              <div class="stat-label">可删除</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getOperableCount('retry') }}</div>
              <div class="stat-label">可重试</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getOperableCount('download') }}</div>
              <div class="stat-label">可下载</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!selectedOperation"
          @click="handleConfirm"
        >
          确认执行
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Close, Delete, RefreshRight, Download, WarningFilled } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils'
import TaskStatusTag from './TaskStatusTag.vue'

interface Props {
  visible: boolean
  selectedTasks: Api.DataFactory.TaskInfo[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', operation: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const selectedOperation = ref('')

// 计算属性
const canCancel = computed(() => {
  return props.selectedTasks.some(task => 
    task.status === 'pending' || task.status === 'running'
  )
})

const canDelete = computed(() => {
  return props.selectedTasks.some(task => 
    ['completed', 'failed', 'cancelled'].includes(task.status)
  )
})

const canRetry = computed(() => {
  return props.selectedTasks.some(task => task.status === 'failed')
})

const canDownload = computed(() => {
  return props.selectedTasks.some(task => 
    task.status === 'completed' && task.result_file_path
  )
})

const confirmationMessage = computed(() => {
  const messages: Record<string, string> = {
    cancel: '批量取消任务',
    delete: '批量删除任务',
    retry: '批量重试任务',
    download: '批量下载结果'
  }
  return messages[selectedOperation.value] || ''
})

const confirmationType = computed(() => {
  const types: Record<string, string> = {
    cancel: 'warning',
    delete: 'error',
    retry: 'info',
    download: 'success'
  }
  return types[selectedOperation.value] || 'info'
})

const confirmationDetails = computed(() => {
  const count = getOperableCount(selectedOperation.value)
  const details: Record<string, string> = {
    cancel: `将取消 ${count} 个任务的执行`,
    delete: `将永久删除 ${count} 个任务及其相关数据`,
    retry: `将重新执行 ${count} 个失败的任务`,
    download: `将下载 ${count} 个任务的结果文件`
  }
  return details[selectedOperation.value] || ''
})

// 监听器
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      selectedOperation.value = ''
    }
  },
  { immediate: true }
)

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleConfirm = async () => {
  if (!selectedOperation.value) {
    ElMessage.warning('请选择要执行的操作')
    return
  }

  const operableCount = getOperableCount(selectedOperation.value)
  if (operableCount === 0) {
    ElMessage.warning('没有可执行此操作的任务')
    return
  }

  try {
    loading.value = true
    
    // 发送确认事件
    emit('confirm', selectedOperation.value)
    
    ElMessage.success(`批量${getOperationName(selectedOperation.value)}操作已提交`)
    
  } catch (error) {
    ElMessage.error('操作执行失败')
  } finally {
    loading.value = false
  }
}

const getOperableCount = (operation: string) => {
  switch (operation) {
    case 'cancel':
      return props.selectedTasks.filter(task => 
        task.status === 'pending' || task.status === 'running'
      ).length
    case 'delete':
      return props.selectedTasks.filter(task => 
        ['completed', 'failed', 'cancelled'].includes(task.status)
      ).length
    case 'retry':
      return props.selectedTasks.filter(task => task.status === 'failed').length
    case 'download':
      return props.selectedTasks.filter(task => 
        task.status === 'completed' && task.result_file_path
      ).length
    default:
      return 0
  }
}

const getOperationName = (operation: string) => {
  const names: Record<string, string> = {
    cancel: '取消',
    delete: '删除',
    retry: '重试',
    download: '下载'
  }
  return names[operation] || operation
}
</script>

<style scoped lang="scss">
.batch-operation-dialog {
  .dialog-content {
    .selected-tasks-section,
    .operation-section,
    .confirmation-section,
    .statistics-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }
    }

    .selected-tasks-section {
      .tasks-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid var(--el-border-color-light);
        border-radius: 6px;

        .task-item {
          padding: 12px 16px;
          border-bottom: 1px solid var(--el-border-color-lighter);

          &:last-child {
            border-bottom: none;
          }

          .task-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;

            .task-name {
              font-weight: 500;
              color: var(--el-text-color-primary);
            }
          }

          .task-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: var(--el-text-color-placeholder);

            .task-id {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            }
          }
        }
      }
    }

    .operation-section {
      .operation-list {
        width: 100%;

        .operation-item {
          width: 100%;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .el-radio {
            width: 100%;
            margin-right: 0;

            .el-radio__label {
              width: 100%;
              padding-left: 8px;
            }
          }

          .operation-content {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid var(--el-border-color-light);
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover {
              border-color: var(--el-color-primary);
              background: var(--el-fill-color-extra-light);
            }

            .operation-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 12px;
              font-size: 18px;

              &.cancel {
                background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
                color: #8b5a2b;
              }

              &.delete {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;
              }

              &.retry {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
              }

              &.download {
                background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                color: white;
              }
            }

            .operation-info {
              .operation-name {
                font-size: 16px;
                font-weight: 500;
                color: var(--el-text-color-primary);
                margin-bottom: 4px;
              }

              .operation-desc {
                font-size: 13px;
                color: var(--el-text-color-regular);
                line-height: 1.4;
              }
            }
          }
        }
      }
    }

    .confirmation-section {
      .confirmation-details {
        .warning-text {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-top: 8px;
          color: var(--el-color-danger);
          font-size: 13px;
          font-weight: 500;
        }
      }
    }

    .statistics-section {
      .stat-item {
        text-align: center;
        padding: 12px;
        background: var(--el-fill-color-light);
        border-radius: 6px;

        .stat-value {
          font-size: 20px;
          font-weight: 600;
          color: var(--el-color-primary);
          line-height: 1;
        }

        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
          margin-top: 4px;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
