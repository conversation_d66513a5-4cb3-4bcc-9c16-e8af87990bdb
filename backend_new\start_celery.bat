@echo off
REM Celery服务启动脚本 (Windows)
echo 启动Celery服务...

REM 创建日志目录
if not exist logs mkdir logs

REM 启动Celery Worker (后台运行)
echo 启动Celery Worker...
start "Celery Worker" cmd /c "python celery_worker.py"

REM 等待2秒
timeout /t 2 /nobreak >nul

REM 启动Celery Beat (后台运行)
echo 启动Celery Beat...
start "Celery Beat" cmd /c "python celery_beat.py"

REM 等待2秒
timeout /t 2 /nobreak >nul

REM 启动Celery Flower监控 (可选)
echo 启动Celery Flower监控...
start "Celery Flower" cmd /c "python celery_monitor.py"

echo.
echo Celery服务启动完成！
echo.
echo 服务状态:
echo - Worker: 处理任务队列
echo - Beat: 定时任务调度
echo - Flower: 监控界面 (http://localhost:5555)
echo.
echo 按任意键退出...
pause >nul
