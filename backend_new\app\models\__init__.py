"""
数据模型模块
导入所有数据模型以确保它们被SQLAlchemy注册
"""
from .base import BaseModel
from .user import User, Role
from .environment import Environment
from .model_config import ModelConfig

# 混沌测试相关模型
from .chaos_task import ChaosTask
from .chaos_batch_task import ChaosBatchTask, ChaosBatchTaskItem
from .chaos_execution import ChaosExecution
from .chaos_scenario import ChaosScenario

__all__ = [
    "BaseModel",
    "User",
    "Role",
    "Environment",
    "ModelConfig",
    "ChaosTask",
    "ChaosBatchTask",
    "ChaosBatchTaskItem",
    "ChaosExecution",
    "ChaosScenario",
]
