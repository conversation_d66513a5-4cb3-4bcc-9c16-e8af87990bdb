"""
角色业务服务
"""
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.crud.role import RoleCRUD
from app.schemas.user import RoleResponse


class RoleService:
    """角色业务服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.role_crud = RoleCRUD(db)

    async def get_role_list(self) -> List[RoleResponse]:
        """获取角色列表"""
        roles = await self.role_crud.get_all_active_roles()
        
        # 转换为响应格式
        role_list = []
        for role in roles:
            role_item = RoleResponse(
                id=role.id,
                roleCode=role.code,
                roleName=role.name,
                description=role.description or "",
                status="1" if role.is_active else "2",
                createTime=role.created_at.strftime("%Y-%m-%d %H:%M:%S") if role.created_at else "",
                updateTime=role.updated_at.strftime("%Y-%m-%d %H:%M:%S") if role.updated_at else ""
            )
            role_list.append(role_item)
        
        return role_list
