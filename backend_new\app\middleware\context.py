"""
上下文中间件
自动设置当前用户上下文
"""
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.context import set_current_user_id, clear_current_user_id
from app.core.security import get_current_user_from_token
from app.db.session import get_db
import logging

logger = logging.getLogger(__name__)


class ContextMiddleware(BaseHTTPMiddleware):
    """上下文中间件，自动设置当前用户ID"""

    async def dispatch(self, request: Request, call_next):
        # 清除之前的上下文
        clear_current_user_id()

        try:
            # 尝试从请求中获取当前用户
            authorization = request.headers.get("Authorization")

            if authorization and authorization.startswith("Bearer "):
                token = authorization.split(" ")[1]
                try:
                    # 获取数据库会话
                    async for db in get_db():
                        user = await get_current_user_from_token(token, db)
                        if user:
                            set_current_user_id(str(user.id))
                            logger.debug(f"设置当前用户上下文: {user.id}")
                        break
                except Exception as e:
                    logger.debug(f"获取当前用户失败: {str(e)}")
                    # 不抛出异常，让后续的认证中间件处理
                    pass

            # 处理请求
            response = await call_next(request)
            return response

        finally:
            # 清除上下文
            clear_current_user_id()
