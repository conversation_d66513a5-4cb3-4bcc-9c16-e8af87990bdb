# 应用配置
APP_NAME=DpTestPlatform Backend
APP_VERSION=1.0.0
DEBUG=true
API_V1_STR=/api/v1

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=dptest_platform

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:5173

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
#LLOWED_FILE_TYPES=.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
