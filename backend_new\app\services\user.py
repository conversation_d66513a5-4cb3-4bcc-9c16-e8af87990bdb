"""
用户业务服务
"""
from typing import Dict, Any, Optional
from datetime import timedelta
from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.user import UserCRUD
from app.schemas.user import (
    UserCreate, UserUpdate, UserResponse, UserPageResponse, UserQuery,
    LoginRequest, LoginResponse, RegisterRequest
)
from app.core.config import settings
from app.core.security import create_access_token, create_refresh_token
from app.core.exceptions import (
    raise_authentication_error,
    raise_validation_error,
    raise_conflict_error,
    raise_not_found_error
)
from app.db.redis import RedisClient
from app.services.base import BaseService


class UserService(BaseService):
    """用户业务服务类"""

    def __init__(self, db: AsyncSession, redis_client: RedisClient = None):
        super().__init__(db, redis_client)
        self.user_crud = UserCRUD(db)
        self.redis_client = redis_client

    async def login(self, login_data: LoginRequest, request: Request = None) -> LoginResponse:
        """用户登录"""
        # 用户认证
        user = await self.user_crud.authenticate_user(
            username=login_data.username,
            password=login_data.password
        )

        if not user:
            raise_authentication_error("用户名或密码错误")

        if not user.is_active:
            raise_validation_error("用户账号已被禁用")

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        token = create_access_token(
            subject=user.id,
            expires_delta=access_token_expires
        )

        # 创建刷新令牌
        refreshToken = create_refresh_token(subject=user.id)

        # 更新最后登录时间
        await self.user_crud.update_last_login(user.id)

        # 缓存用户信息到Redis
        if self.redis_client:
            user_cache_key = f"user:{user.id}"
            user_data = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "nickname": user.nickname,
                "is_superuser": user.is_superuser,
                "roles": [role.code for role in user.roles] if user.roles else []
            }
            await self.redis_client.set(
                user_cache_key,
                user_data,
                expire=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            )

        # 构建用户响应
        user_response = self._convert_to_response(user)

        return LoginResponse(
            token=token,
            refreshToken=refreshToken,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user_response
        )

    async def register(self, register_data: RegisterRequest) -> UserResponse:
        """用户注册"""
        # 检查用户名是否已存在
        existing_user = await self.user_crud.get_by_username(register_data.username)
        if existing_user:
            raise_conflict_error(f"用户名 '{register_data.username}' 已存在")

        # 检查邮箱是否已存在
        if register_data.email:
            existing_email = await self.user_crud.get_by_email(register_data.email)
            if existing_email:
                raise_conflict_error(f"邮箱 '{register_data.email}' 已被使用")

        # 创建用户
        user_create = UserCreate(
            username=register_data.username,
            email=register_data.email,
            password=register_data.password,
            nickname=register_data.nickname or register_data.username
        )

        user = await self.user_crud.create_user(user_create)
        return self._convert_to_response(user)

    async def get_user_by_id(self, user_id: int) -> UserResponse:
        """根据ID获取用户"""
        # 先尝试从Redis缓存获取
        if self.redis_client:
            user_cache_key = f"user:{user_id}"
            cached_user = await self.redis_client.get(user_cache_key)
            if cached_user:
                return UserResponse(**cached_user)

        # 从数据库获取
        user = await self.user_crud.get(self.db, user_id)
        if not user:
            raise_not_found_error("用户不存在")

        return self._convert_to_response(user)

    async def list_users(self, query: UserQuery) -> UserPageResponse:
        """查询用户列表"""
        users, total = await self.user_crud.list(
            keyword=query.keyword,
            status=query.status,
            is_active=query.is_active,
            offset=query.offset,
            limit=query.size
        )

        # 转换为响应格式
        user_responses = [self._convert_to_response(user) for user in users]

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return UserPageResponse(
            items=user_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """创建用户"""
        # 检查用户名是否重复
        existing_user = await self.user_crud.get_by_username(user_data.username)
        if existing_user:
            raise_conflict_error(f"用户名 '{user_data.username}' 已存在")

        # 检查邮箱是否重复
        if user_data.email:
            existing_email = await self.user_crud.get_by_email(user_data.email)
            if existing_email:
                raise_conflict_error(f"邮箱 '{user_data.email}' 已被使用")

        user = await self.user_crud.create_user(user_data)
        return self._convert_to_response(user)

    async def update_user(self, user_id: int, user_data: UserUpdate) -> UserResponse:
        """更新用户"""
        user = await self.user_crud.update_user(user_id, user_data)
        if not user:
            raise_not_found_error("用户不存在")

        # 清除Redis缓存
        if self.redis_client:
            user_cache_key = f"user:{user_id}"
            await self.redis_client.delete(user_cache_key)

        return self._convert_to_response(user)

    async def delete_user(self, user_id: int) -> bool:
        """删除用户"""
        user = await self.user_crud.remove(self.db, id=user_id)
        if not user:
            raise_not_found_error("用户不存在")

        # 清除Redis缓存
        if self.redis_client:
            user_cache_key = f"user:{user_id}"
            await self.redis_client.delete(user_cache_key)

        return True

    def _convert_to_response(self, user) -> UserResponse:
        """将User对象转换为UserResponse"""
        # 安全获取角色信息，避免延迟加载问题
        try:
            roles = [role.code for role in user.roles] if hasattr(user, 'roles') and user.roles else []
        except Exception:
            roles = []

        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            nickname=user.nickname,
            avatar=user.avatar,
            description=user.description,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            status=user.status,
            last_login_at=user.last_login_at,
            roles=roles,
            created_at=user.created_at,
            updated_at=user.updated_at,
            created_by=user.created_by,
            updated_by=user.updated_by,
            created_by_name=None,  # 将在_resolve_user_names中设置
            updated_by_name=None,  # 将在_resolve_user_names中设置
        )
