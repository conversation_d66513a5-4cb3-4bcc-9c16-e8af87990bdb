"""
认证API路由
包含登录、注册、token刷新等功能
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_redis_client, get_current_user
from app.db.redis import RedisClient
from app.schemas.user import LoginRequest, LoginResponse, RegisterRequest, UserResponse
from app.schemas.base import response_builder
from app.services.user import UserService

router = APIRouter()


@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(
    login_data: LoginRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client)
):
    """
    用户登录接口
    
    Args:
        login_data: 登录数据
        request: HTTP请求对象
        db: 数据库会话
        redis_client: Redis客户端
        
    Returns:
        登录响应数据
    """
    user_service = UserService(db, redis_client)
    result = await user_service.login(login_data, request)
    return result


@router.post("/register", response_model=UserResponse, summary="用户注册")
async def register(
    register_data: RegisterRequest,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client)
):
    """
    用户注册接口
    
    Args:
        register_data: 注册数据
        db: 数据库会话
        redis_client: Redis客户端
        
    Returns:
        用户信息
    """
    user_service = UserService(db, redis_client)
    result = await user_service.register(register_data)
    return result


@router.post("/logout", summary="用户登出")
async def logout(
    redis_client: RedisClient = Depends(get_redis_client),
    current_user = Depends(get_current_user)
):
    """
    用户登出接口
    清除Redis中的用户缓存

    Args:
        redis_client: Redis客户端
        current_user: 当前用户

    Returns:
        成功响应
    """
    # 清除用户缓存
    if redis_client:
        user_cache_key = f"user:{current_user.id}"
        await redis_client.delete(user_cache_key)

    return {"message": "登出成功"}


@router.get("/user/info", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    current_user = Depends(get_current_user)
):
    """
    获取当前登录用户的详细信息
    用于前端获取用户信息和权限

    Args:
        db: 数据库会话
        redis_client: Redis客户端
        current_user: 当前用户

    Returns:
        用户详细信息
    """
    user_service = UserService(db, redis_client)
    result = await user_service.get_user_by_id(current_user.id)
    return result
