#!/usr/bin/env python3
"""
快速测试API端点
"""
import asyncio
import aiohttp
import json


async def test_api():
    """测试API端点"""
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # 测试根路径
        print("🔍 测试根路径...")
        try:
            async with session.get(f"{base_url}/") as response:
                print(f"根路径状态: {response.status}")
        except Exception as e:
            print(f"根路径错误: {e}")
        
        # 测试API文档
        print("\n🔍 测试API文档...")
        try:
            async with session.get(f"{base_url}/docs") as response:
                print(f"API文档状态: {response.status}")
        except Exception as e:
            print(f"API文档错误: {e}")
        
        # 测试健康检查
        print("\n🔍 测试健康检查...")
        try:
            async with session.get(f"{base_url}/health") as response:
                print(f"健康检查状态: {response.status}")
                if response.status == 200:
                    result = await response.json()
                    print(f"健康检查结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"健康检查错误: {e}")
        
        # 测试环境管理路由
        print("\n🔍 测试环境管理路由...")
        try:
            async with session.get(f"{base_url}/api/v1/env/supported-types") as response:
                print(f"支持类型状态: {response.status}")
                if response.status == 200:
                    result = await response.json()
                    print(f"支持类型结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                else:
                    error_text = await response.text()
                    print(f"支持类型错误: {error_text}")
        except Exception as e:
            print(f"支持类型异常: {e}")
        
        # 测试连接测试端点
        print("\n🔍 测试连接测试端点...")
        test_data = {
            "type": "http",
            "config": {
                "host": "httpbin.org",
                "port": 80,
                "protocol": "http",
                "path": "/get"
            },
            "timeout": 10
        }
        
        try:
            async with session.post(
                f"{base_url}/api/v1/env/test",
                json=test_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                print(f"连接测试状态: {response.status}")
                if response.status == 200:
                    result = await response.json()
                    print(f"连接测试结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                else:
                    error_text = await response.text()
                    print(f"连接测试错误: {error_text}")
        except Exception as e:
            print(f"连接测试异常: {e}")


if __name__ == "__main__":
    asyncio.run(test_api())
