"""
混沌测试批次任务数据模型
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, String, Integer, Text, JSON, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class ChaosBatchTask(BaseModel):
    """
    混沌测试批次任务模型
    管理多个故障注入任务的批次执行
    """
    __tablename__ = "chaos_batch_tasks"

    # 基本信息
    name = Column(String(100), nullable=False, index=True, comment="批次任务名称")
    description = Column(Text, nullable=True, comment="批次任务描述")
    
    # 关联信息
    env_ids = Column(JSON, nullable=False, comment="目标环境ID列表")
    
    # 执行控制
    execution_type = Column(String(20), default="immediate", comment="执行类型：immediate/scheduled")
    scheduled_time = Column(DateTime, nullable=True, comment="定时执行时间")
    
    # 批次执行配置
    batch_execution_mode = Column(String(20), default="sequential", comment="批次执行模式：sequential/parallel")
    batch_interval = Column(Integer, default=0, comment="批次间隔时间(秒)")
    
    # 任务状态
    task_status = Column(String(20), default="enabled", comment="任务状态：enabled/disabled/archived")
    last_execution_time = Column(DateTime, nullable=True, comment="最后执行时间")
    execution_count = Column(Integer, default=0, comment="执行次数")
    
    # 监控配置
    monitor_config = Column(JSON, nullable=True, comment="监控配置")
    
    # 关联关系
    batch_items = relationship("ChaosBatchTaskItem", back_populates="batch_task", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ChaosBatchTask(id={self.id}, name='{self.name}', mode='{self.batch_execution_mode}')>"
    
    @property
    def is_enabled(self) -> bool:
        """是否启用"""
        return self.task_status == "enabled"
    
    def can_execute(self) -> bool:
        """是否可以执行"""
        if not self.is_enabled:
            return False
        
        if self.execution_type == "scheduled":
            return self.scheduled_time and self.scheduled_time <= datetime.now()
        
        return True


class ChaosBatchTaskItem(BaseModel):
    """
    批次任务子项模型
    定义批次任务中的单个故障注入配置
    """
    __tablename__ = "chaos_batch_task_items"

    # 关联信息
    batch_task_id = Column(Integer, ForeignKey("chaos_batch_tasks.id"), nullable=False, comment="批次任务ID")
    
    # 基本信息
    name = Column(String(100), nullable=False, comment="子任务名称")
    description = Column(Text, nullable=True, comment="子任务描述")
    
    # 执行顺序
    execution_order = Column(Integer, default=0, comment="执行顺序")
    
    # 故障配置
    fault_type = Column(String(50), nullable=False, comment="故障类型：cpu/memory/network/disk/process/k8s")
    fault_params = Column(JSON, nullable=False, comment="故障参数配置")
    
    # 故障控制
    auto_destroy = Column(Boolean, default=True, comment="是否自动销毁故障")
    max_duration = Column(Integer, nullable=True, comment="最大执行时长(秒)")
    
    # 状态控制
    is_enabled = Column(Boolean, default=True, comment="是否启用")
    
    # 关联关系
    batch_task = relationship("ChaosBatchTask", back_populates="batch_items")
    
    def __repr__(self):
        return f"<ChaosBatchTaskItem(id={self.id}, name='{self.name}', fault_type='{self.fault_type}')>"
