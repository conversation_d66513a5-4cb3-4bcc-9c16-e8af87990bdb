"""
路由管理器
统一管理所有API路由
"""
from fastapi import APIRouter, Depends
from app.core.config import settings
from app.api.deps import get_current_user, get_redis_client
from app.db.session import get_async_db

# 创建主路由器
api_router = APIRouter()

# 在这里包含所有子路由
from app.api.v1.auth import router as auth_router
from app.api.v1.users import router as users_router
from app.api.v1.roles import router as roles_router
from app.api.v1.environments import router as environments_router
from app.api.v1.model_configs import router as model_configs_router
from app.api.v1.chaos.tasks import router as chaos_tasks_router
from app.api.v1.chaos.executions import router as chaos_executions_router
from app.api.v1.health import router as health_router
from app.api.v1.upload import router as upload_router

api_router.include_router(auth_router,prefix="/auth",tags=["认证"])

api_router.include_router(users_router,prefix="/users",tags=["用户管理"])

api_router.include_router(roles_router,prefix="/roles",tags=["角色管理"])

api_router.include_router(environments_router,prefix="/env",tags=["环境管理"])
 
api_router.include_router(model_configs_router,prefix="/model",tags=["模型配置管理"])

api_router.include_router(chaos_tasks_router,prefix="/chaos/tasks",tags=["混沌测试任务"])

api_router.include_router(chaos_executions_router,prefix="/chaos/executions",tags=["混沌测试执行记录"])

api_router.include_router(health_router,prefix="/health",tags=["健康检查"])

api_router.include_router(upload_router,prefix="/upload",tags=["文件上传"])

# 添加用户信息接口到根路径，适配前端
@api_router.get("/user/info", response_model=None, tags=["用户信息"])
async def get_user_info(
    db = Depends(get_async_db),
    redis_client = Depends(get_redis_client),
    current_user = Depends(get_current_user)
):
    """获取当前用户信息 - 适配前端"""
    from app.services.user import UserService
    user_service = UserService(db, redis_client)
    result = await user_service.get_user_by_id(current_user.id)
    return result


@api_router.get("/", tags=["根路径"])
async def root():
    """根路径接口"""
    return {
        "message": f"欢迎使用 {settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "redoc": "/redoc"
    }
