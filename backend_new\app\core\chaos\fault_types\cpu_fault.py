"""
CPU故障处理器
实现CPU相关的故障注入和销毁
"""
import logging
from typing import Dict, Any
from .base_fault import BaseFaultHandler, FaultResult

logger = logging.getLogger(__name__)


class CPUFaultHandler(BaseFaultHandler):
    """CPU故障处理器"""
    
    async def inject(
        self, 
        ssh_client, 
        fault_params: Dict[str, Any],
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """
        注入CPU故障
        
        支持的参数:
        - cpu-percent: CPU使用率百分比 (0-100)
        - cpu-count: 影响的CPU核心数
        - timeout: 持续时间(秒)
        """
        # 验证参数
        is_valid, error_msg = self.validate_params(fault_params)
        if not is_valid:
            return FaultResult(
                success=False,
                fault_id="",
                message=f"参数验证失败: {error_msg}"
            )
        
        # 构建命令参数
        cmd_params = {}
        
        # CPU使用率
        if "cpu_percent" in fault_params:
            cmd_params["cpu-percent"] = fault_params["cpu_percent"]
        
        # CPU核心数
        if "cpu_count" in fault_params:
            cmd_params["cpu-count"] = fault_params["cpu_count"]
        
        # 持续时间
        if "timeout" in fault_params:
            cmd_params["timeout"] = fault_params["timeout"]
        
        # 构建ChaosBlade命令
        command = self._build_chaosblade_command(
            action="create",
            target="cpu",
            params=cmd_params,
            chaosblade_path=chaosblade_path
        )
        
        # 执行命令
        return await self._execute_chaosblade_command(ssh_client, command)
    
    async def destroy(
        self, 
        ssh_client, 
        fault_id: str,
        chaosblade_path: str = "/opt/chaosblade/blade"
    ) -> FaultResult:
        """销毁CPU故障"""
        command = self._build_destroy_command(fault_id, chaosblade_path)
        return await self._execute_chaosblade_command(ssh_client, command)
    
    def validate_params(self, fault_params: Dict[str, Any]) -> tuple[bool, str]:
        """验证CPU故障参数"""
        required_params = ["cpu_percent"]
        
        # 检查必需参数
        for param in required_params:
            if param not in fault_params:
                return False, f"缺少必需参数: {param}"
        
        # 验证CPU使用率
        cpu_percent = fault_params.get("cpu_percent")
        if not isinstance(cpu_percent, (int, float)) or not (0 <= cpu_percent <= 100):
            return False, "cpu_percent必须是0-100之间的数字"
        
        # 验证CPU核心数
        cpu_count = fault_params.get("cpu_count")
        if cpu_count is not None:
            if not isinstance(cpu_count, int) or cpu_count <= 0:
                return False, "cpu_count必须是正整数"
        
        # 验证超时时间
        timeout = fault_params.get("timeout")
        if timeout is not None:
            if not isinstance(timeout, int) or timeout <= 0:
                return False, "timeout必须是正整数"
        
        return True, ""
    
    def get_param_schema(self) -> Dict[str, Any]:
        """获取CPU故障参数结构定义"""
        return {
            "type": "object",
            "properties": {
                "cpu_percent": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 100,
                    "description": "CPU使用率百分比",
                    "required": True
                },
                "cpu_count": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "影响的CPU核心数，默认为所有核心",
                    "required": False
                },
                "timeout": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "持续时间(秒)，不指定则持续到手动销毁",
                    "required": False
                }
            },
            "required": ["cpu_percent"],
            "additionalProperties": False
        }
