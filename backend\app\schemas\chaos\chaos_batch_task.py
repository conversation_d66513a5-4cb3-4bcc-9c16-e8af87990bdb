"""
混沌测试批次任务数据传输对象
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

from app.schemas.base import BaseResponseSchema, BaseQuery, BasePageResponse


class ChaosBatchTaskItemBase(BaseModel):
    """批次任务子项基础模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="子任务名称")
    description: Optional[str] = Field(None, max_length=1000, description="子任务描述")
    fault_type: Optional[str] = Field(None, description="故障类型")
    fault_params: Optional[Dict[str, Any]] = Field(None, description="故障参数配置")
    task_order: Optional[int] = Field(None, ge=1, description="任务执行顺序")
    auto_destroy: Optional[bool] = Field(None, description="是否自动销毁故障")
    max_duration: Optional[int] = Field(None, ge=1, description="最大执行时长(秒)")


class ChaosBatchTaskItemCreate(ChaosBatchTaskItemBase):
    """创建批次任务子项请求模型"""
    id: Optional[int] = Field(None, description="子项ID（编辑时提供）")
    name: str = Field(..., min_length=1, max_length=100, description="子任务名称")
    fault_type: str = Field(..., description="故障类型")
    fault_params: Dict[str, Any] = Field(..., description="故障参数配置")
    task_order: int = Field(..., ge=1, description="任务执行顺序")
    auto_destroy: bool = Field(default=True, description="是否自动销毁故障")


class ChaosBatchTaskItemUpdate(ChaosBatchTaskItemBase):
    """更新批次任务子项请求模型"""
    pass


class ChaosBatchTaskItemResponse(ChaosBatchTaskItemBase, BaseResponseSchema):
    """批次任务子项响应模型"""
    id: int = Field(..., description="子项ID")
    batch_task_id: int = Field(..., description="批次任务ID")
    name: str = Field(..., description="子任务名称")
    fault_type: str = Field(..., description="故障类型")
    fault_params: Dict[str, Any] = Field(..., description="故障参数配置")
    task_order: int = Field(..., description="任务执行顺序")
    auto_destroy: bool = Field(..., description="是否自动销毁故障")

    class Config:
        from_attributes = True


class ChaosBatchTaskBase(BaseModel):
    """批次任务基础模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="批次任务名称")
    description: Optional[str] = Field(None, max_length=1000, description="批次任务描述")
    env_id: Optional[int] = Field(None, description="目标环境ID")
    batch_execution_mode: Optional[str] = Field(None, description="批次执行模式：sequential(间隔执行)/order(连续执行)")
    wait_time: Optional[int] = Field(None, ge=0, le=3600, description="间隔执行的等待时间(秒)")
    execution_type: Optional[str] = Field(None, description="执行类型")
    scheduled_time: Optional[datetime] = Field(None, description="定时执行时间")
    periodic_config: Optional[Dict[str, Any]] = Field(None, description="周期性执行配置")
    cron_expression: Optional[str] = Field(None, description="Cron表达式")
    auto_destroy: Optional[bool] = Field(None, description="是否自动销毁故障")
    max_duration: Optional[int] = Field(None, description="最大执行时长(秒)")
    task_status: Optional[str] = Field(None, description="任务状态")

    @validator('batch_execution_mode')
    def validate_batch_execution_mode(cls, v):
        """验证批次执行模式"""
        if v is not None:
            allowed_modes = ['sequential', 'order']
            if v not in allowed_modes:
                raise ValueError(f'批次执行模式必须为: {", ".join(allowed_modes)}')
        return v

    @validator('execution_type')
    def validate_execution_type(cls, v):
        """验证执行类型"""
        if v is not None:
            allowed_types = ['immediate', 'scheduled', 'periodic', 'cron']
            if v not in allowed_types:
                raise ValueError(f'执行类型必须为: {", ".join(allowed_types)}')
        return v

    @validator('task_status')
    def validate_task_status(cls, v):
        """验证任务状态"""
        if v is not None:
            allowed_statuses = ['enabled', 'disabled']
            if v not in allowed_statuses:
                raise ValueError(f'任务状态必须为: {", ".join(allowed_statuses)}')
        return v


class ChaosBatchTaskCreate(ChaosBatchTaskBase):
    """创建批次任务请求模型"""
    name: str = Field(..., min_length=1, max_length=100, description="批次任务名称")
    env_id: int = Field(..., description="目标环境ID")
    batch_execution_mode: str = Field(default="sequential", description="批次执行模式：sequential(间隔执行)/order(连续执行)")
    wait_time: int = Field(default=30, ge=0, le=3600, description="间隔执行的等待时间(秒)")
    execution_type: str = Field(default="immediate", description="执行类型")
    auto_destroy: bool = Field(default=True, description="是否自动销毁故障")
    task_status: str = Field(default="enabled", description="任务状态")
    
    # 子任务列表
    task_items: List[ChaosBatchTaskItemCreate] = Field(..., min_items=1, description="子任务列表")

    @validator('task_items')
    def validate_task_items_order(cls, v):
        """验证子任务顺序的唯一性"""
        if v:
            orders = [item.task_order for item in v]
            if len(orders) != len(set(orders)):
                raise ValueError('子任务执行顺序不能重复')
            
            # 检查顺序是否连续（从1开始）
            sorted_orders = sorted(orders)
            expected_orders = list(range(1, len(orders) + 1))
            if sorted_orders != expected_orders:
                raise ValueError('子任务执行顺序必须从1开始且连续')
        return v


class ChaosBatchTaskUpdate(ChaosBatchTaskBase):
    """更新批次任务请求模型"""
    # 子任务列表（可选，用于更新子任务）
    task_items: Optional[List[ChaosBatchTaskItemCreate]] = Field(None, description="子任务列表")

    @validator('task_items')
    def validate_task_items_order(cls, v):
        """验证子任务顺序的唯一性"""
        if v:
            orders = [item.task_order for item in v]
            if len(orders) != len(set(orders)):
                raise ValueError('子任务执行顺序不能重复')
            
            # 检查顺序是否连续（从1开始）
            sorted_orders = sorted(orders)
            expected_orders = list(range(1, len(orders) + 1))
            if sorted_orders != expected_orders:
                raise ValueError('子任务执行顺序必须从1开始且连续')
        return v


class ChaosBatchTaskResponse(ChaosBatchTaskBase, BaseResponseSchema):
    """批次任务详情响应模型"""
    id: int = Field(..., description="批次任务ID")
    name: str = Field(..., description="批次任务名称")
    env_id: int = Field(..., description="目标环境ID")
    batch_execution_mode: str = Field(..., description="批次执行模式：sequential(间隔执行)/order(连续执行)")
    wait_time: Optional[int] = Field(None, description="间隔执行的等待时间(秒)")
    execution_type: str = Field(..., description="执行类型")
    status: str = Field(..., description="运行状态")
    task_status: str = Field(..., description="任务状态")
    auto_destroy: bool = Field(..., description="是否自动销毁故障")

    # 扩展字段
    execution_result: Optional[Dict[str, Any]] = Field(None, description="执行结果汇总")
    environment_name: Optional[str] = Field(None, description="环境名称")
    task_count: int = Field(0, description="子任务数量")
    
    # 子任务列表
    task_items: List[ChaosBatchTaskItemResponse] = Field(default=[], description="子任务列表")
    
    # 状态属性
    is_running: bool = Field(description="是否正在运行")
    is_completed: bool = Field(description="是否已完成")
    can_execute: bool = Field(description="是否可以执行")
    can_stop: bool = Field(description="是否可以停止")
    can_edit: bool = Field(description="是否可以编辑")
    can_delete: bool = Field(description="是否可以删除")

    class Config:
        from_attributes = True


class ChaosBatchTaskQuery(BaseQuery):
    """批次任务查询参数"""
    keyword: Optional[str] = Field(None, description="关键词搜索（任务名称、描述）")
    env_id: Optional[int] = Field(None, description="环境ID筛选")
    batch_execution_mode: Optional[str] = Field(None, description="批次执行模式筛选：sequential(间隔执行)/order(连续执行)")
    status: Optional[str] = Field(None, description="运行状态筛选")
    execution_type: Optional[str] = Field(None, description="执行类型筛选")
    task_status: Optional[str] = Field(None, description="任务状态筛选")
    created_by: Optional[str] = Field(None, description="创建者筛选")


class ChaosBatchTaskPageResponse(BasePageResponse):
    """批次任务列表分页响应模型"""
    items: List[ChaosBatchTaskResponse] = Field(..., description="批次任务列表")


class ChaosBatchTaskExecuteRequest(BaseModel):
    """批次任务执行请求模型"""
    force: bool = Field(default=False, description="是否强制执行（忽略状态检查）")
    override_params: Optional[Dict[str, Any]] = Field(None, description="覆盖参数")
    monitor_config: Optional[Dict[str, Any]] = Field(None, description="监控配置")


class ChaosBatchTaskStatusRequest(BaseModel):
    """批次任务状态变更请求模型"""
    task_status: str = Field(..., description="任务状态：enabled/disabled")

    @validator('task_status')
    def validate_task_status(cls, v):
        """验证任务状态"""
        allowed_statuses = ['enabled', 'disabled']
        if v not in allowed_statuses:
            raise ValueError(f'任务状态必须为: {", ".join(allowed_statuses)}')
        return v


class ChaosBatchTaskStatistics(BaseModel):
    """批次任务统计信息"""
    total_count: int = Field(..., description="总任务数")
    status_stats: Dict[str, int] = Field(..., description="状态统计")
    execution_mode_stats: Dict[str, int] = Field(..., description="执行模式统计")
    env_stats: Dict[str, int] = Field(..., description="环境统计")
