"""
模型配置Pydantic模式
"""
from typing import Optional, Dict, Any, List, Literal
from pydantic import BaseModel, Field, field_validator
from app.schemas.base import BaseSchema, BaseResponseSchema, PaginationSchema, PaginationResponseSchema


class ModelConfigBase(BaseSchema):
    """模型配置基础模型（包含公共字段）"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="模型名称（唯一标识）")
    platform: Optional[str] = Field(None, min_length=1, max_length=50, description="所属平台")
    description: Optional[str] = Field(None, max_length=1000, description="模型描述")
    api_url: Optional[str] = Field(None, min_length=1, max_length=500, description="API地址")
    timeout_seconds: Optional[int] = Field(None, ge=1, le=300, description="请求超时时间（秒）")
    model_name: Optional[str] = Field(None, min_length=1, max_length=100, description="实际模型名称")
    max_tokens: Optional[int] = Field(None, ge=1, le=32768, description="最大令牌数")
    prompt: Optional[str] = Field(None, max_length=5000, description="系统提示词")

    @field_validator('platform')
    @classmethod
    def validate_platform(cls, v):
        """验证平台类型"""
        if v is not None:
            allowed_platforms = [
                'local',       # 本地模型
                'doubao',      # 豆包 - 字节跳动
                'deepseek',    # DeepSeek
                'qwen',        # 通义千问 - 阿里巴巴
                'ernie',       # 文心一言 - 百度
                'chatglm',     # 智谱AI
                'openai',      # OpenAI
                'claude'       # Anthropic Claude
            ]
            if v.lower() not in allowed_platforms:
                raise ValueError(f'平台类型必须是以下之一: {", ".join(allowed_platforms)}')
            return v.lower()
        return v

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v):
        """验证API地址格式"""
        if v is not None and not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError('API地址必须以http://或https://开头')
        return v


class ModelConfigCreate(ModelConfigBase):
    """创建模型配置请求模型（必填字段）"""
    name: str = Field(..., min_length=1, max_length=100, description="模型名称（唯一标识）")
    platform: str = Field(..., min_length=1, max_length=50, description="所属平台")
    api_url: str = Field(..., min_length=1, max_length=500, description="API地址")
    api_key: Optional[str] = Field(None, min_length=1, description="API Key（明文，创建时会自动加密）")
    model_name: str = Field(..., min_length=1, max_length=100, description="实际模型名称")


class ModelConfigUpdate(ModelConfigBase):
    """更新模型配置请求模型（可选字段）"""
    api_key: Optional[str] = Field(None, description="API Key（明文，更新时会自动加密）")


class ModelConfigQuery(PaginationSchema):
    """模型配置查询参数模型"""
    keyword: Optional[str] = Field(None, description="搜索关键词")
    platform: Optional[str] = Field(None, description="平台类型筛选")
    status: Optional[str] = Field(None, description="状态筛选")
    health_status: Optional[str] = Field(None, description="健康状态筛选")


class ModelConfigResponse(ModelConfigBase, BaseResponseSchema):
    """模型配置详情响应模型"""
    id: int = Field(..., description="模型配置ID")
    name: str = Field(..., description="模型名称")
    platform: str = Field(..., description="所属平台")
    api_url: str = Field(..., description="API地址")
    model_name: str = Field(..., description="实际模型名称")
    status: Literal['enabled', 'disabled'] = Field(default='enabled', description="状态")
    health_status: Literal['unknown', 'healthy', 'unhealthy'] = Field(default='unknown', description="健康状态")
    last_health_check: Optional[str] = Field(None, description="最后健康检查时间")
    api_key: str = Field(..., description="API Key（已脱敏显示）")

    model_config = {"from_attributes": True}


class ModelConfigPageResponse(BaseModel):
    """模型配置列表分页响应模型"""
    items: List[ModelConfigResponse] = Field(..., description="当前页模型列表")
    total: int = Field(..., description="总条数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页条数")
    pages: int = Field(..., description="总页数")


class ModelHealthCheckRequest(BaseSchema):
    """模型健康检查请求模式"""
    model_id: Optional[int] = Field(None, description="单个模型ID")
    model_ids: Optional[List[int]] = Field(None, description="模型ID列表，为空时检查所有启用的模型")
    timeout_seconds: Optional[int] = Field(10, ge=1, le=60, description="检查超时时间（秒）")


class ModelHealthCheckResponse(BaseSchema):
    """模型健康检查响应模式"""
    model_id: int = Field(description="模型ID")
    model_name: str = Field(description="模型名称")
    is_healthy: bool = Field(description="是否健康")
    response_time_ms: Optional[int] = Field(None, description="响应时间（毫秒）")
    error_message: Optional[str] = Field(None, description="错误信息")
    check_time: str = Field(description="检查时间")


class ModelCallRequest(BaseSchema):
    """模型调用请求模式"""
    model_name: str = Field(..., description="模型名称")
    prompt: str = Field(..., min_length=1, description="提示词")
    parameters: Optional[Dict[str, Any]] = Field(None, description="调用参数")
    stream: Optional[bool] = Field(False, description="是否流式返回")


class ModelCallResponse(BaseSchema):
    """模型调用响应模式"""
    model_name: str = Field(description="模型名称")
    response: Any = Field(description="模型响应（原始格式）")
    response_time_ms: int = Field(description="响应时间（毫秒）")
    success: bool = Field(description="是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")


class ModelStatsResponse(BaseSchema):
    """模型统计响应模式"""
    total_count: int = Field(description="模型总数")
    platform_stats: Dict[str, int] = Field(description="平台统计")
    status_stats: Dict[str, int] = Field(description="状态统计")
    health_stats: Dict[str, int] = Field(description="健康状态统计")
    recent_models: List[ModelConfigResponse] = Field(description="最近模型")


class SupportedPlatformResponse(BaseSchema):
    """支持的平台响应模式"""
    platform: str = Field(description="平台代码")
    display_name: str = Field(description="显示名称")
    description: str = Field(description="平台描述")
    default_timeout: int = Field(description="默认超时时间")
    required_fields: List[str] = Field(description="必填字段")
    optional_fields: List[str] = Field(description="可选字段")
    icon: Optional[str] = Field(None, description="图标")
    category: Optional[str] = Field(None, description="分类")
