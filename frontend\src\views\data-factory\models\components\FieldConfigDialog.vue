<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="700px"
    :before-close="handleClose"
    class="field-config-dialog"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="field-form"
      >
        <!-- 基础信息 -->
        <div class="form-section">
          <h4 class="section-title">基础信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="字段名称" prop="name" required>
                <el-input
                  v-model="form.name"
                  placeholder="请输入字段名称"
                  clearable
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据类型" prop="type" required>
                <el-select
                  v-model="form.type"
                  placeholder="选择数据类型"
                  style="width: 100%"
                  @change="handleTypeChange"
                >
                  <el-option
                    v-for="type in dataTypes"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                  >
                    <div class="type-option">
                      <span class="type-name">{{ type.label }}</span>
                      <span class="type-desc">{{ type.description }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="生成器" prop="generator" required>
                <el-select
                  v-model="form.generator"
                  placeholder="选择生成器"
                  style="width: 100%"
                  @change="handleGeneratorChange"
                >
                  <el-option
                    v-for="generator in availableGenerators"
                    :key="generator.value"
                    :label="generator.label"
                    :value="generator.value"
                  >
                    <div class="generator-option">
                      <span class="generator-name">{{ generator.label }}</span>
                      <span class="generator-desc">{{ generator.description }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否必填" prop="required">
                <el-radio-group v-model="form.required">
                  <el-radio :label="true">必填</el-radio>
                  <el-radio :label="false">可选</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="字段描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="2"
              placeholder="请输入字段描述（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 生成器配置 -->
        <div v-if="form.generator" class="form-section">
          <h4 class="section-title">生成器配置</h4>
          
          <!-- UUID生成器 -->
          <div v-if="form.generator === 'uuid'" class="generator-config">
            <el-alert
              title="UUID生成器无需额外配置"
              type="info"
              :closable="false"
              show-icon
            />
          </div>

          <!-- 姓名生成器 -->
          <div v-else-if="form.generator === 'name'" class="generator-config">
            <el-form-item label="语言地区">
              <el-select v-model="form.options.locale" placeholder="选择语言地区">
                <el-option label="中文" value="zh_CN" />
                <el-option label="英文" value="en_US" />
                <el-option label="日文" value="ja_JP" />
              </el-select>
            </el-form-item>
          </div>

          <!-- 手机号生成器 -->
          <div v-else-if="form.generator === 'phone'" class="generator-config">
            <el-form-item label="语言地区">
              <el-select v-model="form.options.locale" placeholder="选择语言地区">
                <el-option label="中国" value="zh_CN" />
                <el-option label="美国" value="en_US" />
              </el-select>
            </el-form-item>
          </div>

          <!-- 邮箱生成器 -->
          <div v-else-if="form.generator === 'email'" class="generator-config">
            <el-form-item label="邮箱域名">
              <el-input
                v-model="form.options.domain"
                placeholder="可选，如：example.com"
                clearable
              />
            </el-form-item>
          </div>

          <!-- 数值范围生成器 -->
          <div v-else-if="form.generator === 'range'" class="generator-config">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="最小值" required>
                  <el-input-number
                    v-model="form.options.min"
                    :min="0"
                    :max="form.options.max - 1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大值" required>
                  <el-input-number
                    v-model="form.options.max"
                    :min="form.options.min + 1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 序列生成器 -->
          <div v-else-if="form.generator === 'sequence'" class="generator-config">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="起始值">
                  <el-input-number
                    v-model="form.options.start"
                    :min="0"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="步长">
                  <el-input-number
                    v-model="form.options.step"
                    :min="1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="前缀">
                  <el-input
                    v-model="form.options.prefix"
                    placeholder="可选前缀"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 日期生成器 -->
          <div v-else-if="form.generator === 'date'" class="generator-config">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开始日期">
                  <el-date-picker
                    v-model="form.options.start"
                    type="date"
                    placeholder="选择开始日期"
                    style="width: 100%"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束日期">
                  <el-date-picker
                    v-model="form.options.end"
                    type="date"
                    placeholder="选择结束日期"
                    style="width: 100%"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 预览区域 -->
        <div v-if="form.generator" class="form-section">
          <h4 class="section-title">数据预览</h4>
          <div class="preview-area">
            <div class="preview-header">
              <span class="preview-title">生成样例</span>
              <el-button size="small" @click="handleRefreshPreview">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            <div class="preview-samples">
              <el-tag
                v-for="(sample, index) in previewSamples"
                :key="index"
                class="sample-tag"
                type="info"
              >
                {{ sample }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          {{ mode === 'create' ? '添加字段' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  field: Api.DataFactory.FieldConfig | null
  mode: 'create' | 'edit'
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', field: Api.DataFactory.FieldConfig): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const previewSamples = ref<string[]>([])

// 表单数据
const form = reactive<Api.DataFactory.FieldConfig>({
  name: '',
  type: 'string',
  generator: 'uuid',
  description: '',
  required: true,
  options: {}
})

// 数据类型选项
const dataTypes = [
  { value: 'string', label: '字符串', description: '文本数据' },
  { value: 'integer', label: '整数', description: '整数数值' },
  { value: 'decimal', label: '小数', description: '浮点数值' },
  { value: 'boolean', label: '布尔值', description: '真/假值' },
  { value: 'date', label: '日期', description: '日期格式' },
  { value: 'datetime', label: '日期时间', description: '日期时间格式' }
]

// 生成器选项
const generators = {
  string: [
    { value: 'uuid', label: 'UUID', description: '唯一标识符' },
    { value: 'name', label: '姓名', description: '随机姓名' },
    { value: 'phone', label: '手机号', description: '手机号码' },
    { value: 'email', label: '邮箱', description: '邮箱地址' }
  ],
  integer: [
    { value: 'range', label: '数值范围', description: '指定范围内的整数' },
    { value: 'sequence', label: '序列', description: '递增序列' }
  ],
  decimal: [
    { value: 'range', label: '数值范围', description: '指定范围内的小数' }
  ],
  boolean: [
    { value: 'random', label: '随机布尔', description: '随机真假值' }
  ],
  date: [
    { value: 'date', label: '日期生成器', description: '随机日期' }
  ],
  datetime: [
    { value: 'date', label: '日期时间生成器', description: '随机日期时间' }
  ]
}

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { min: 1, max: 50, message: '名称长度应在 1 到 50 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
      message: '字段名称只能包含字母、数字和下划线，且不能以数字开头',
      trigger: 'blur'
    }
  ],
  type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  generator: [
    { required: true, message: '请选择生成器', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '添加字段' : '编辑字段'
})

const availableGenerators = computed(() => {
  return generators[form.type as keyof typeof generators] || []
})

// 监听器
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      initForm()
    }
  },
  { immediate: true }
)

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

watch(
  () => form.generator,
  () => {
    generatePreviewSamples()
  }
)

// 方法
const initForm = () => {
  if (props.field) {
    Object.assign(form, {
      ...props.field,
      options: { ...props.field.options }
    })
  } else {
    Object.assign(form, {
      name: '',
      type: 'string',
      generator: 'uuid',
      description: '',
      required: true,
      options: {}
    })
  }

  nextTick(() => {
    formRef.value?.clearValidate()
    generatePreviewSamples()
  })
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleTypeChange = () => {
  // 重置生成器选择
  const availableGens = generators[form.type as keyof typeof generators] || []
  if (availableGens.length > 0) {
    form.generator = availableGens[0].value
  }
  form.options = {}
  generatePreviewSamples()
}

const handleGeneratorChange = () => {
  // 重置生成器选项
  form.options = {}
  
  // 设置默认选项
  switch (form.generator) {
    case 'name':
    case 'phone':
      form.options.locale = 'zh_CN'
      break
    case 'range':
      form.options.min = 1
      form.options.max = 100
      break
    case 'sequence':
      form.options.start = 1
      form.options.step = 1
      break
  }
  
  generatePreviewSamples()
}

const handleRefreshPreview = () => {
  generatePreviewSamples()
}

const generatePreviewSamples = () => {
  const samples: string[] = []
  
  for (let i = 0; i < 5; i++) {
    let sample = ''
    
    switch (form.generator) {
      case 'uuid':
        sample = `uuid-${Math.random().toString(36).substr(2, 8)}`
        break
      case 'name':
        const names = ['张三', '李四', '王五', '赵六', '钱七']
        sample = names[Math.floor(Math.random() * names.length)]
        break
      case 'phone':
        sample = `138${Math.random().toString().substr(2, 8)}`
        break
      case 'email':
        const domain = form.options.domain || 'example.com'
        sample = `user${i + 1}@${domain}`
        break
      case 'range':
        const min = form.options.min || 1
        const max = form.options.max || 100
        sample = String(Math.floor(Math.random() * (max - min + 1)) + min)
        break
      case 'sequence':
        const start = form.options.start || 1
        const step = form.options.step || 1
        sample = String(start + i * step)
        break
      case 'date':
        const date = new Date()
        date.setDate(date.getDate() - Math.floor(Math.random() * 365))
        sample = date.toISOString().split('T')[0]
        break
      case 'random':
        sample = Math.random() > 0.5 ? 'true' : 'false'
        break
      default:
        sample = `${form.generator}_${i + 1}`
    }
    
    samples.push(sample)
  }
  
  previewSamples.value = samples
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    
    // 清理空的选项
    const cleanOptions = Object.fromEntries(
      Object.entries(form.options).filter(([_, value]) => value !== '' && value !== null && value !== undefined)
    )
    
    const fieldData: Api.DataFactory.FieldConfig = {
      ...form,
      options: Object.keys(cleanOptions).length > 0 ? cleanOptions : undefined
    }
    
    emit('confirm', fieldData)
    
  } catch (error) {
    ElMessage.error('请检查表单输入')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.field-config-dialog {
  .dialog-content {
    .field-form {
      .form-section {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          border-bottom: 1px solid var(--el-border-color-lighter);
          padding-bottom: 8px;
        }

        .type-option,
        .generator-option {
          display: flex;
          flex-direction: column;

          .type-name,
          .generator-name {
            font-weight: 500;
          }

          .type-desc,
          .generator-desc {
            font-size: 12px;
            color: var(--el-text-color-placeholder);
          }
        }

        .generator-config {
          background: var(--el-fill-color-extra-light);
          padding: 16px;
          border-radius: 6px;
        }

        .preview-area {
          background: var(--el-fill-color-light);
          padding: 16px;
          border-radius: 6px;

          .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .preview-title {
              font-weight: 500;
              color: var(--el-text-color-primary);
            }
          }

          .preview-samples {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .sample-tag {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 13px;
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
