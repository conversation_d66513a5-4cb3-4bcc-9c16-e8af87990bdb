"""
时区处理工具模块
统一处理应用中的时区相关操作
"""
from datetime import datetime, timezone, timedelta
from typing import Optional, Union
from app.core.config import settings


class TimezoneHelper:
    """时区处理助手类"""
    
    def __init__(self):
        # 从配置获取时区，默认为上海时区
        self.app_timezone = self._get_timezone_from_string(settings.TIMEZONE)
    
    def _get_timezone_from_string(self, tz_string: str) -> timezone:
        """从字符串获取时区对象"""
        if tz_string == "Asia/Shanghai":
            return timezone(timedelta(hours=8))
        elif tz_string == "UTC":
            return timezone.utc
        elif tz_string.startswith("UTC"):
            # 处理 UTC+8, UTC-5 等格式
            try:
                offset_str = tz_string[3:]
                if offset_str.startswith('+'):
                    hours = int(offset_str[1:])
                elif offset_str.startswith('-'):
                    hours = -int(offset_str[1:])
                else:
                    hours = int(offset_str)
                return timezone(timedelta(hours=hours))
            except (ValueError, IndexError):
                # 解析失败，使用默认时区
                return timezone(timedelta(hours=8))
        else:
            # 默认使用上海时区
            return timezone(timedelta(hours=8))
    
    def now(self) -> datetime:
        """获取当前应用时区的时间"""
        return datetime.now(self.app_timezone)
    
    def utc_now(self) -> datetime:
        """获取当前UTC时间"""
        return datetime.now(timezone.utc)
    
    def to_app_timezone(self, dt: datetime) -> datetime:
        """将时间转换为应用时区"""
        if dt.tzinfo is None:
            # 如果没有时区信息，假设为UTC时间
            dt = dt.replace(tzinfo=timezone.utc)
        return dt.astimezone(self.app_timezone)
    
    def to_utc(self, dt: datetime) -> datetime:
        """将时间转换为UTC时区"""
        if dt.tzinfo is None:
            # 如果没有时区信息，假设为应用时区
            dt = dt.replace(tzinfo=self.app_timezone)
        return dt.astimezone(timezone.utc)
    
    def format_datetime(self, dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """格式化时间为字符串"""
        if dt is None:
            return ""
        
        # 转换为应用时区
        app_dt = self.to_app_timezone(dt)
        return app_dt.strftime(format_str)
    
    def parse_datetime(self, dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
        """解析时间字符串为datetime对象"""
        dt = datetime.strptime(dt_str, format_str)
        # 添加应用时区信息
        return dt.replace(tzinfo=self.app_timezone)
    
    def get_timezone_offset(self) -> str:
        """获取时区偏移字符串，如 '+08:00'"""
        offset = self.app_timezone.utcoffset(None)
        if offset is None:
            return "+00:00"
        
        total_seconds = int(offset.total_seconds())
        hours, remainder = divmod(abs(total_seconds), 3600)
        minutes = remainder // 60
        sign = '+' if total_seconds >= 0 else '-'
        return f"{sign}{hours:02d}:{minutes:02d}"


# 全局时区助手实例
timezone_helper = TimezoneHelper()


# 便捷函数
def now() -> datetime:
    """获取当前应用时区的时间"""
    return timezone_helper.now()


def utc_now() -> datetime:
    """获取当前UTC时间"""
    return timezone_helper.utc_now()


def to_app_timezone(dt: datetime) -> datetime:
    """将时间转换为应用时区"""
    return timezone_helper.to_app_timezone(dt)


def to_utc(dt: datetime) -> datetime:
    """将时间转换为UTC时区"""
    return timezone_helper.to_utc(dt)


def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化时间为字符串"""
    return timezone_helper.format_datetime(dt, format_str)


def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """解析时间字符串为datetime对象"""
    return timezone_helper.parse_datetime(dt_str, format_str)
