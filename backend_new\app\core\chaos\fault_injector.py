"""
故障注入核心模块
统一的故障注入接口，支持单次和批次注入
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from app.utils.clients.ssh_client import SSHClient
from app.core.chaos.fault_types import get_fault_handler

logger = logging.getLogger(__name__)


class FaultStatus(Enum):
    """故障状态枚举"""
    PENDING = "pending"
    INJECTING = "injecting"
    INJECTED = "injected"
    DESTROYING = "destroying"
    DESTROYED = "destroyed"
    FAILED = "failed"


@dataclass
class FaultConfig:
    """故障配置"""
    fault_type: str  # cpu/memory/network/disk/process/k8s
    fault_params: Dict[str, Any]  # 故障参数
    host_info: Dict[str, Any]  # 主机信息
    max_duration: Optional[int] = None  # 最大持续时间(秒)
    auto_destroy: bool = True  # 是否自动销毁
    
    # 执行标识
    execution_id: Optional[int] = None
    task_id: Optional[int] = None
    batch_task_id: Optional[int] = None


@dataclass
class InjectionResult:
    """单次注入结果"""
    success: bool
    fault_id: str  # ChaosBlade UID或其他标识
    message: str
    output: Optional[str] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    command: Optional[str] = None
    
    # 故障信息
    fault_config: Optional[FaultConfig] = None
    host_info: Optional[Dict[str, Any]] = None


@dataclass
class BatchInjectionResult:
    """批次注入结果"""
    total_count: int
    success_count: int
    failed_count: int
    results: List[InjectionResult]
    batch_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.success_count / self.total_count if self.total_count > 0 else 0.0
    
    @property
    def is_all_success(self) -> bool:
        """是否全部成功"""
        return self.success_count == self.total_count
    
    @property
    def is_partial_success(self) -> bool:
        """是否部分成功"""
        return 0 < self.success_count < self.total_count


class FaultInjector:
    """故障注入器"""
    
    def __init__(self):
        self.active_faults: Dict[str, FaultConfig] = {}  # 活跃故障记录
        self.chaosblade_path = "/opt/chaosblade/blade"  # ChaosBlade路径
    
    async def inject_single(self, fault_config: FaultConfig) -> InjectionResult:
        """
        单次故障注入
        
        Args:
            fault_config: 故障配置
            
        Returns:
            InjectionResult: 注入结果
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"开始执行故障注入: {fault_config.fault_type}")
            
            # 获取故障处理器
            fault_handler = get_fault_handler(fault_config.fault_type)
            if not fault_handler:
                return InjectionResult(
                    success=False,
                    fault_id="",
                    message=f"不支持的故障类型: {fault_config.fault_type}",
                    start_time=start_time,
                    fault_config=fault_config
                )
            
            # 创建SSH连接
            ssh_client = SSHClient(fault_config.host_info)
            connect_result = await ssh_client.connect()
            
            if not connect_result.success:
                return InjectionResult(
                    success=False,
                    fault_id="",
                    message=f"SSH连接失败: {connect_result.message}",
                    start_time=start_time,
                    fault_config=fault_config
                )
            
            try:
                # 执行故障注入
                injection_result = await fault_handler.inject(
                    ssh_client=ssh_client,
                    fault_params=fault_config.fault_params,
                    chaosblade_path=self.chaosblade_path
                )
                
                if injection_result.success:
                    # 记录活跃故障
                    self.active_faults[injection_result.fault_id] = fault_config
                    
                    # 设置自动销毁
                    if fault_config.auto_destroy and fault_config.max_duration:
                        asyncio.create_task(
                            self._schedule_auto_destroy(
                                injection_result.fault_id,
                                fault_config.max_duration
                            )
                        )
                
                injection_result.start_time = start_time
                injection_result.fault_config = fault_config
                injection_result.host_info = fault_config.host_info
                
                return injection_result
                
            finally:
                await ssh_client.disconnect()
                
        except Exception as e:
            logger.error(f"故障注入异常: {str(e)}")
            return InjectionResult(
                success=False,
                fault_id="",
                message=f"故障注入异常: {str(e)}",
                error=str(e),
                start_time=start_time,
                fault_config=fault_config
            )
    
    async def inject_batch(
        self, 
        fault_configs: List[FaultConfig],
        execution_mode: str = "parallel"  # parallel/sequential
    ) -> BatchInjectionResult:
        """
        批次故障注入
        
        Args:
            fault_configs: 故障配置列表
            execution_mode: 执行模式 parallel(并行)/sequential(顺序)
            
        Returns:
            BatchInjectionResult: 批次注入结果
        """
        batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        
        logger.info(f"开始批次故障注入: {batch_id}, 模式: {execution_mode}, 任务数: {len(fault_configs)}")
        
        if execution_mode == "parallel":
            # 并行执行
            tasks = [self.inject_single(config) for config in fault_configs]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append(InjectionResult(
                        success=False,
                        fault_id="",
                        message=f"执行异常: {str(result)}",
                        error=str(result),
                        fault_config=fault_configs[i]
                    ))
                else:
                    processed_results.append(result)
                    
        else:
            # 顺序执行
            processed_results = []
            for config in fault_configs:
                result = await self.inject_single(config)
                processed_results.append(result)
                
                # 如果失败且配置了停止策略，可以在这里处理
                if not result.success:
                    logger.warning(f"故障注入失败，继续执行下一个: {result.message}")
        
        # 统计结果
        success_count = sum(1 for r in processed_results if r.success)
        failed_count = len(processed_results) - success_count
        
        batch_result = BatchInjectionResult(
            total_count=len(fault_configs),
            success_count=success_count,
            failed_count=failed_count,
            results=processed_results,
            batch_id=batch_id,
            start_time=start_time,
            end_time=datetime.now()
        )
        
        logger.info(f"批次故障注入完成: {batch_id}, 成功: {success_count}, 失败: {failed_count}")
        return batch_result
    
    async def destroy_fault(self, fault_id: str) -> InjectionResult:
        """
        销毁故障
        
        Args:
            fault_id: 故障ID (ChaosBlade UID)
            
        Returns:
            InjectionResult: 销毁结果
        """
        start_time = datetime.now()
        
        try:
            # 获取故障配置
            fault_config = self.active_faults.get(fault_id)
            if not fault_config:
                return InjectionResult(
                    success=False,
                    fault_id=fault_id,
                    message=f"未找到活跃故障: {fault_id}",
                    start_time=start_time
                )
            
            # 获取故障处理器
            fault_handler = get_fault_handler(fault_config.fault_type)
            if not fault_handler:
                return InjectionResult(
                    success=False,
                    fault_id=fault_id,
                    message=f"不支持的故障类型: {fault_config.fault_type}",
                    start_time=start_time
                )
            
            # 创建SSH连接
            ssh_client = SSHClient(fault_config.host_info)
            connect_result = await ssh_client.connect()
            
            if not connect_result.success:
                return InjectionResult(
                    success=False,
                    fault_id=fault_id,
                    message=f"SSH连接失败: {connect_result.message}",
                    start_time=start_time
                )
            
            try:
                # 执行故障销毁
                destroy_result = await fault_handler.destroy(
                    ssh_client=ssh_client,
                    fault_id=fault_id,
                    chaosblade_path=self.chaosblade_path
                )
                
                if destroy_result.success:
                    # 移除活跃故障记录
                    self.active_faults.pop(fault_id, None)
                
                destroy_result.start_time = start_time
                return destroy_result
                
            finally:
                await ssh_client.disconnect()
                
        except Exception as e:
            logger.error(f"故障销毁异常: {str(e)}")
            return InjectionResult(
                success=False,
                fault_id=fault_id,
                message=f"故障销毁异常: {str(e)}",
                error=str(e),
                start_time=start_time
            )
    
    async def _schedule_auto_destroy(self, fault_id: str, delay_seconds: int):
        """
        调度自动销毁
        
        Args:
            fault_id: 故障ID
            delay_seconds: 延迟时间(秒)
        """
        try:
            logger.info(f"调度自动销毁故障: {fault_id}, 延迟: {delay_seconds}秒")
            await asyncio.sleep(delay_seconds)
            
            # 检查故障是否仍然活跃
            if fault_id in self.active_faults:
                logger.info(f"执行自动销毁故障: {fault_id}")
                result = await self.destroy_fault(fault_id)
                
                if result.success:
                    logger.info(f"自动销毁故障成功: {fault_id}")
                else:
                    logger.error(f"自动销毁故障失败: {fault_id}, {result.message}")
            else:
                logger.info(f"故障已被手动销毁: {fault_id}")
                
        except Exception as e:
            logger.error(f"自动销毁故障异常: {fault_id}, {str(e)}")
    
    def get_active_faults(self) -> Dict[str, FaultConfig]:
        """获取活跃故障列表"""
        return self.active_faults.copy()
    
    def is_fault_active(self, fault_id: str) -> bool:
        """检查故障是否活跃"""
        return fault_id in self.active_faults


# 全局故障注入器实例
fault_injector = FaultInjector()
