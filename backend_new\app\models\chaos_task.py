"""
混沌测试任务数据模型
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, Integer, Text, JSON, DateTime, Boolean
from app.models.base import BaseModel


class ChaosTask(BaseModel):
    """
    混沌测试任务模型
    管理故障注入任务的完整生命周期
    """
    __tablename__ = "chaos_tasks"

    # 基本信息
    name = Column(String(100), nullable=False, index=True, comment="任务名称")
    description = Column(Text, nullable=True, comment="任务描述")
    
    # 关联信息
    env_ids = Column(JSON, nullable=False, comment="目标环境ID列表")
    
    # 故障配置
    fault_type = Column(String(50), nullable=False, comment="故障类型：cpu/memory/network/disk/process/k8s")
    fault_params = Column(JSON, nullable=False, comment="故障参数配置")
    
    # 执行控制
    execution_type = Column(String(20), default="immediate", comment="执行类型：immediate/scheduled/periodic/cron")
    scheduled_time = Column(DateTime, nullable=True, comment="定时执行时间")
    periodic_config = Column(JSON, nullable=True, comment="周期性执行配置")
    cron_expression = Column(String(100), nullable=True, comment="Cron表达式")
    
    # 故障控制
    auto_destroy = Column(Boolean, default=True, comment="是否自动销毁故障")
    max_duration = Column(Integer, nullable=True, comment="最大执行时长(秒)")
    
    # 任务状态
    task_status = Column(String(20), default="enabled", comment="任务状态：enabled/disabled/archived")
    last_execution_time = Column(DateTime, nullable=True, comment="最后执行时间")
    execution_count = Column(Integer, default=0, comment="执行次数")
    
    # 监控配置
    monitor_config = Column(JSON, nullable=True, comment="监控配置")
    
    def __repr__(self):
        return f"<ChaosTask(id={self.id}, name='{self.name}', fault_type='{self.fault_type}')>"
    
    @property
    def is_scheduled(self) -> bool:
        """是否为定时任务"""
        return self.execution_type in ["scheduled", "periodic", "cron"]
    
    @property
    def is_enabled(self) -> bool:
        """是否启用"""
        return self.task_status == "enabled"
    
    def can_execute(self) -> bool:
        """是否可以执行"""
        if not self.is_enabled:
            return False
        
        if self.execution_type == "scheduled":
            return self.scheduled_time and self.scheduled_time <= datetime.now()
        
        return True
