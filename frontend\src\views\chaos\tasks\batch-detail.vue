<template>
  <div class="batch-task-detail" v-loading="loading">
    <div v-if="task">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <el-button @click="handleBack" :icon="ArrowLeft">返回</el-button>
          <div class="header-info">
            <h2>{{ task.name }}</h2>
            <div class="header-meta">
              <el-tag type="warning">批次任务</el-tag>
              <el-tag :type="getStatusTagType(task.status)">
                {{ getStatusLabel(task.status) }}
              </el-tag>
              <span class="meta-item">创建时间：{{ formatDateTime(task.created_at) }}</span>
              <span class="meta-item">创建者：{{ task.created_by }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <el-button
            v-if="task.can_execute"
            type="primary"
            @click="handleExecuteTask"
            :loading="executing"
          >
            执行批次任务
          </el-button>
          <el-button
            v-if="task.can_stop"
            type="danger"
            @click="handleStopTask"
          >
            停止任务
          </el-button>
          <el-dropdown @command="handleDropdownCommand">
            <el-button>
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">编辑任务</el-dropdown-item>
                <el-dropdown-item command="copy">复制任务</el-dropdown-item>
                <el-dropdown-item command="executions" divided>查看执行记录</el-dropdown-item>
                <el-dropdown-item
                  v-if="task.status !== 'pending'"
                  command="reset"
                >
                  重置任务
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="task.task_status === 'disabled'"
                  command="enable"
                >
                  启用任务
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="task.task_status === 'enabled'"
                  command="disable"
                >
                  禁用任务
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 任务详情卡片 -->
      <div class="detail-content">
        <el-row :gutter="24">
          <el-col :span="16">
            <!-- 基本信息 -->
            <el-card class="detail-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <span>基本信息</span>
                </div>
              </template>
              <div class="info-grid">
                <div class="info-item">
                  <label>任务名称：</label>
                  <span>{{ task.name }}</span>
                </div>
                <div class="info-item">
                  <label>任务描述：</label>
                  <span>{{ task.description || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>目标环境：</label>
                  <span>{{ task.environment_name || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>执行模式：</label>
                  <span>{{ getBatchExecutionModeLabel(task.batch_execution_mode) }}</span>
                </div>
                <div class="info-item">
                  <label>子任务数量：</label>
                  <span>{{ task.task_count }}个</span>
                </div>
                <div class="info-item">
                  <label>执行类型：</label>
                  <span>{{ getExecutionTypeLabel(task.execution_type) }}</span>
                </div>
                <div class="info-item" v-if="task.scheduled_time">
                  <label>计划执行时间：</label>
                  <span>{{ formatDateTime(task.scheduled_time) }}</span>
                </div>
                <div class="info-item" v-if="task.cron_expression">
                  <label>Cron表达式：</label>
                  <span>{{ task.cron_expression }}</span>
                </div>
              </div>
            </el-card>

            <!-- 子任务列表 -->
            <el-card class="detail-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <span>子任务列表</span>
                </div>
              </template>
              <div class="task-items">
                <div 
                  v-for="(item, index) in task.task_items" 
                  :key="item.id"
                  class="task-item"
                >
                  <div class="task-item-header">
                    <span class="task-order">{{ index + 1 }}</span>
                    <span class="task-name">{{ item.name }}</span>
                    <el-tag size="small">{{ getFaultTypeLabel(item.fault_type) }}</el-tag>
                  </div>
                  <div class="task-item-content">
                    <div class="task-description">{{ item.description || '无描述' }}</div>
                    <div class="task-params">
                      <el-collapse>
                        <el-collapse-item title="查看参数" :name="item.id">
                          <pre class="params-json">{{ JSON.stringify(item.fault_params, null, 2) }}</pre>
                        </el-collapse-item>
                      </el-collapse>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <!-- 执行状态 -->
            <el-card class="detail-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <span>执行状态</span>
                </div>
              </template>
              <div class="status-info">
                <div class="status-item">
                  <label>任务状态：</label>
                  <el-tag :type="getStatusTagType(task.status)">
                    {{ getStatusLabel(task.status) }}
                  </el-tag>
                </div>
                <div class="status-item">
                  <label>执行状态：</label>
                  <el-tag :type="getTaskStatusTagType(task.task_status)">
                    {{ getTaskStatusLabel(task.task_status) }}
                  </el-tag>
                </div>
                <div class="status-item" v-if="task.execution_result">
                  <label>执行结果：</label>
                  <div class="execution-result">
                    <pre>{{ JSON.stringify(task.execution_result, null, 2) }}</pre>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 操作记录 -->
            <el-card class="detail-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <span>操作记录</span>
                </div>
              </template>
              <div class="operation-logs">
                <div class="log-item">
                  <div class="log-time">{{ formatDateTime(task.created_at) }}</div>
                  <div class="log-content">任务创建</div>
                  <div class="log-user">{{ task.created_by }}</div>
                </div>
                <div class="log-item" v-if="task.updated_at !== task.created_at">
                  <div class="log-time">{{ formatDateTime(task.updated_at) }}</div>
                  <div class="log-content">任务更新</div>
                  <div class="log-user">{{ task.updated_by }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <div v-else-if="!loading" class="empty-state">
      <el-empty description="任务不存在或已被删除" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import ChaosService from '@/api/chaosApi'
import type { ChaosBatchTaskResponse } from '@/types/api/chaos'

defineOptions({ name: 'ChaosBatchTaskDetail' })

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const executing = ref(false)
const task = ref<ChaosBatchTaskResponse | null>(null)

// 获取任务ID
const taskId = computed(() => parseInt(route.params.id as string))

// 生命周期
onMounted(async () => {
  await loadTaskDetail()
})

// 方法
const loadTaskDetail = async () => {
  loading.value = true
  try {
    task.value = await ChaosService.getBatchTaskDetail(taskId.value)
  } catch (error) {
    ElMessage.error('加载任务详情失败')
  } finally {
    loading.value = false
  }
}

const handleBack = () => {
  router.push('/chaos/tasks')
}

const handleExecuteTask = async () => {
  if (!task.value) return
  
  try {
    await ElMessageBox.confirm(
      '确定要执行此批次任务吗？',
      '确认执行',
      { type: 'warning' }
    )
    
    executing.value = true
    await ChaosService.executeBatchTask(task.value.id)
    ElMessage.success('批次任务执行成功')
    await loadTaskDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('执行任务失败')
    }
  } finally {
    executing.value = false
  }
}

const handleStopTask = async () => {
  if (!task.value) return
  
  try {
    await ElMessageBox.confirm(
      '确定要停止此批次任务吗？',
      '确认停止',
      { type: 'warning' }
    )
    
    await ChaosService.stopBatchTask(task.value.id)
    ElMessage.success('任务停止成功')
    await loadTaskDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('停止任务失败')
    }
  }
}

const handleDropdownCommand = (command: string) => {
  if (!task.value) return

  switch (command) {
    case 'edit':
      router.push(`/chaos/batch-tasks/${task.value.id}/edit`)
      break
    case 'copy':
      router.push(`/chaos/tasks/create-batch?copy=${task.value.id}`)
      break
    case 'executions':
      router.push(`/chaos/executions?task_id=${task.value.id}&task_type=batch`)
      break
    case 'reset':
      handleResetTask()
      break
    case 'enable':
      handleEnableTask()
      break
    case 'disable':
      handleDisableTask()
      break
    case 'delete':
      handleDeleteTask()
      break
  }
}

const handleResetTask = async () => {
  if (!task.value) return

  try {
    await ElMessageBox.confirm(
      `确定要重置任务 "${task.value.name}" 吗？重置后任务状态将变为待执行，可以重新执行。`,
      '确认重置',
      { type: 'warning' }
    )

    await ChaosService.resetBatchTask(task.value.id)
    ElMessage.success('任务已重置')
    await loadTaskDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置任务失败')
    }
  }
}

const handleEnableTask = async () => {
  if (!task.value) return

  try {
    await ChaosService.enableBatchTask(task.value.id)
    ElMessage.success('任务已启用')
    await loadTaskDetail()
  } catch (error) {
    ElMessage.error('启用任务失败')
  }
}

const handleDisableTask = async () => {
  if (!task.value) return

  try {
    await ElMessageBox.confirm(
      `确定要禁用任务 "${task.value.name}" 吗？禁用后循环任务将停止调度。`,
      '确认禁用',
      { type: 'warning' }
    )

    await ChaosService.disableBatchTask(task.value.id)
    ElMessage.success('任务已禁用')
    await loadTaskDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('禁用任务失败')
    }
  }
}

const handleDeleteTask = async () => {
  if (!task.value) return

  try {
    await ElMessageBox.confirm(
      '确定要删除此批次任务吗？删除后无法恢复。',
      '确认删除',
      { type: 'warning' }
    )

    await ChaosService.deleteBatchTask(task.value.id)
    ElMessage.success('任务删除成功')
    router.push('/chaos/tasks')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

// 工具方法
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    'draft': 'info',
    'ready': 'success',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'cancelled': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'ready': '就绪',
    'running': '运行中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const getTaskStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'info',
    'running': 'warning',
    'success': 'success',
    'failed': 'danger',
    'cancelled': 'info'
  }
  return statusMap[status] || 'info'
}

const getTaskStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '等待中',
    'running': '执行中',
    'success': '成功',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const getBatchExecutionModeLabel = (mode: string) => {
  const modeMap: Record<string, string> = {
    'sequential': '间隔执行',
    'order': '连续执行'
  }
  return modeMap[mode] || mode
}

const getExecutionTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'immediate': '立即执行',
    'scheduled': '定时执行',
    'periodic': '周期执行',
    'cron': 'Cron执行'
  }
  return typeMap[type] || type
}

const getFaultTypeLabel = (faultType: string) => {
  // 这里可以根据实际的故障类型映射返回中文标签
  return faultType
}

const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>


.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.header-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.meta-item {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  gap: 12px;
}

.detail-content {
  margin-top: 24px;
}

.detail-card {
  margin-bottom: 24px;
}

.card-header {
  font-weight: 600;
  font-size: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: var(--el-text-color-secondary);
  min-width: 120px;
}

.task-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-item {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  background: var(--el-bg-color-page);
}

.task-item-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.task-order {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--el-color-primary);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
}

.task-name {
  font-weight: 500;
  flex: 1;
}

.task-description {
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.params-json {
  background: var(--el-bg-color-page);
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

.status-info,
.operation-logs {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-item label {
  font-weight: 500;
  color: var(--el-text-color-secondary);
  min-width: 80px;
}

.execution-result {
  margin-top: 8px;
}

.execution-result pre {
  background: var(--el-bg-color-page);
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

.log-item {
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-primary);
}

.log-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.log-content {
  font-weight: 500;
  margin-bottom: 4px;
}

.log-user {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}
</style>
