"""
调度作业模型
"""
from datetime import datetime
from typing import Optional, Dict, Any

from sqlalchemy import Column, String, JSON, TIMESTAMP, LargeBinary, Index, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import declarative_base

Base = declarative_base()


class ScheduleJob(Base):
    """调度作业模型"""
    __tablename__ = "schedule_jobs"
    __table_args__ = (
        Index('idx_schedule_jobs_next_run_time', 'next_run_time'),
        Index('idx_schedule_jobs_trigger', 'trigger_type'),
        {'comment': '调度作业表'}
    )

    # 作业标识
    id = Column(String(191), primary_key=True, comment="作业ID")
    name = Column(String(255), nullable=False, comment="作业名称")
    
    # 执行信息
    func = Column(String(500), nullable=False, comment="执行函数路径")
    args = Column(JSON, nullable=True, comment="函数参数")
    kwargs = Column(JSON, nullable=True, comment="函数关键字参数")
    
    # 触发器信息
    trigger_type = Column(String(50), nullable=False, comment="触发器类型：date/interval/cron")
    trigger_args = Column(JSON, nullable=True, comment="触发器参数")
    next_run_time = Column(TIMESTAMP, nullable=True, comment="下次运行时间")
    
    # 作业状态
    job_state = Column(LargeBinary, nullable=True, comment="作业状态数据")

    # 时间戳
    created_at = Column(TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP'), comment="创建时间")
    updated_at = Column(TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment="更新时间")
    
    def __repr__(self):
        return f"<ScheduleJob(id='{self.id}', name='{self.name}', trigger='{self.trigger_type}')>"
    
    @property
    def is_active(self) -> bool:
        """作业是否激活"""
        return self.next_run_time is not None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'func': self.func,
            'args': self.args,
            'kwargs': self.kwargs,
            'trigger': self.trigger_type,
            'trigger_args': self.trigger_args,
            'next_run_time': self.next_run_time.isoformat() if self.next_run_time else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
