<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务详情"
    width="80%"
    :before-close="handleClose"
    class="task-detail-dialog"
  >
    <div v-if="task" class="dialog-content">
      <!-- 任务基本信息 -->
      <div class="task-info-section">
        <div class="section-header">
          <h3>基本信息</h3>
          <div class="task-status">
            <TaskStatusTag :status="task.status" />
          </div>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务名称">{{ task.name }}</el-descriptions-item>
          <el-descriptions-item label="任务ID">{{ task.id }}</el-descriptions-item>
          <el-descriptions-item label="数据模型ID">{{ task.model_id }}</el-descriptions-item>
          <el-descriptions-item label="生成数据量">{{ formatNumber(task.record_count) }} 条</el-descriptions-item>
          <el-descriptions-item label="导出格式">
            <el-tag size="small" :type="getFormatType(task.export_format)">
              {{ task.export_format.toUpperCase() }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行进度">
            <TaskProgressBar :progress="task.progress" :status="task.status" />
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(task.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ task.created_by || '-' }}</el-descriptions-item>
          <el-descriptions-item v-if="task.started_at" label="开始时间">
            {{ formatDateTime(task.started_at) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="task.completed_at" label="完成时间">
            {{ formatDateTime(task.completed_at) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="task.execution_time" label="执行耗时">
            {{ task.execution_time }} 秒
          </el-descriptions-item>
          <el-descriptions-item v-if="task.result_file_size" label="结果文件大小">
            {{ formatFileSize(task.result_file_size) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="task.description" label="任务描述" :span="2">
            {{ task.description }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 执行状态 -->
      <div v-if="task.status === 'running'" class="execution-status-section">
        <div class="section-header">
          <h3>执行状态</h3>
          <el-button size="small" @click="handleRefreshStatus">
            <el-icon><Refresh /></el-icon>
            刷新状态
          </el-button>
        </div>
        
        <div class="status-cards">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="status-card">
                <div class="card-content">
                  <div class="card-icon progress">
                    <el-icon><TrendCharts /></el-icon>
                  </div>
                  <div class="card-info">
                    <div class="card-value">{{ task.progress }}%</div>
                    <div class="card-label">执行进度</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="status-card">
                <div class="card-content">
                  <div class="card-icon memory">
                    <el-icon><Monitor /></el-icon>
                  </div>
                  <div class="card-info">
                    <div class="card-value">{{ task.memory_usage || 0 }}MB</div>
                    <div class="card-label">内存使用</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="status-card">
                <div class="card-content">
                  <div class="card-icon cpu">
                    <el-icon><Cpu /></el-icon>
                  </div>
                  <div class="card-info">
                    <div class="card-value">{{ task.cpu_usage || 0 }}%</div>
                    <div class="card-label">CPU使用率</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="task.status === 'failed' && task.error_message" class="error-section">
        <div class="section-header">
          <h3>错误信息</h3>
        </div>
        
        <el-alert
          :title="task.error_message"
          type="error"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="error-details">
              <p>{{ task.error_message }}</p>
              <div class="error-actions">
                <el-button size="small" @click="handleViewLog">查看详细日志</el-button>
                <el-button size="small" type="primary" @click="handleRetry">重试任务</el-button>
              </div>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 导出配置 -->
      <div v-if="task.export_config" class="export-config-section">
        <div class="section-header">
          <h3>导出配置</h3>
        </div>
        
        <div class="config-content">
          <pre class="config-json">{{ formatExportConfig(task.export_config) }}</pre>
        </div>
      </div>

      <!-- 结果文件信息 -->
      <div v-if="task.status === 'completed' && task.result_file_path" class="result-section">
        <div class="section-header">
          <h3>结果文件</h3>
          <el-button type="primary" size="small" @click="handleDownload">
            <el-icon><Download /></el-icon>
            下载文件
          </el-button>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="文件路径">{{ task.result_file_path }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(task.result_file_size || 0) }}</el-descriptions-item>
          <el-descriptions-item label="文件格式">
            <el-tag size="small" :type="getFormatType(task.export_format)">
              {{ task.export_format.toUpperCase() }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="生成时间">{{ formatDateTime(task.completed_at || '') }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 操作历史 -->
      <div class="history-section">
        <div class="section-header">
          <h3>操作历史</h3>
        </div>
        
        <el-timeline>
          <el-timeline-item
            :timestamp="formatDateTime(task.created_at)"
            type="primary"
          >
            任务创建
          </el-timeline-item>
          <el-timeline-item
            v-if="task.started_at"
            :timestamp="formatDateTime(task.started_at)"
            type="success"
          >
            开始执行
          </el-timeline-item>
          <el-timeline-item
            v-if="task.completed_at"
            :timestamp="formatDateTime(task.completed_at)"
            :type="task.status === 'completed' ? 'success' : 'danger'"
          >
            {{ task.status === 'completed' ? '执行完成' : '执行失败' }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="task?.status === 'completed'" type="primary" @click="handleDownload">
          下载结果
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, TrendCharts, Monitor, Cpu, Download } from '@element-plus/icons-vue'
import { formatDateTime, formatNumber } from '@/utils'
import TaskStatusTag from './TaskStatusTag.vue'
import TaskProgressBar from './TaskProgressBar.vue'

interface Props {
  visible: boolean
  task: Api.DataFactory.TaskInfo | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
  },
  { immediate: true }
)

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleRefreshStatus = () => {
  // 刷新任务状态
  ElMessage.info('状态刷新功能待实现')
}

const handleViewLog = () => {
  // 查看详细日志
  ElMessage.info('查看日志功能待实现')
}

const handleRetry = () => {
  // 重试任务
  ElMessage.info('重试功能待实现')
}

const handleDownload = () => {
  // 下载文件
  ElMessage.info('下载功能待实现')
}

const getFormatType = (format: string) => {
  const types: Record<string, string> = {
    json: 'primary',
    csv: 'success',
    excel: 'warning'
  }
  return types[format] || 'info'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatExportConfig = (config: any) => {
  return JSON.stringify(config, null, 2)
}
</script>

<style scoped lang="scss">
.task-detail-dialog {
  .dialog-content {
    .task-info-section,
    .execution-status-section,
    .error-section,
    .export-config-section,
    .result-section,
    .history-section {
      margin-bottom: 30px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .task-status {
          display: flex;
          align-items: center;
        }
      }

      .status-cards {
        .status-card {
          .card-content {
            display: flex;
            align-items: center;

            .card-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 12px;
              font-size: 20px;

              &.progress {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
              }

              &.memory {
                background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                color: white;
              }

              &.cpu {
                background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                color: white;
              }
            }

            .card-info {
              .card-value {
                font-size: 18px;
                font-weight: 600;
                color: var(--el-text-color-primary);
                line-height: 1;
              }

              .card-label {
                font-size: 12px;
                color: var(--el-text-color-regular);
                margin-top: 4px;
              }
            }
          }
        }
      }

      .error-details {
        .error-actions {
          margin-top: 12px;
          display: flex;
          gap: 8px;
        }
      }

      .config-content {
        .config-json {
          background: var(--el-fill-color-light);
          padding: 16px;
          border-radius: 6px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 13px;
          line-height: 1.5;
          color: var(--el-text-color-primary);
          margin: 0;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
