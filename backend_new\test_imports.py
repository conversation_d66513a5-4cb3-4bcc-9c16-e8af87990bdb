#!/usr/bin/env python3
"""
测试混沌测试模块导入
验证所有组件是否能正确导入
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_core_imports():
    """测试核心模块导入"""
    print("测试核心模块导入...")
    
    try:
        from app.core.celery_app import celery_app
        print("✅ Celery应用导入成功")
    except Exception as e:
        print(f"❌ Celery应用导入失败: {e}")
    
    try:
        from app.core.chaos.fault_injector import fault_injector, FaultConfig
        print("✅ 故障注入器导入成功")
    except Exception as e:
        print(f"❌ 故障注入器导入失败: {e}")
    
    try:
        from app.core.chaos.fault_types import get_fault_handler, get_supported_fault_types
        print("✅ 故障类型处理器导入成功")
    except Exception as e:
        print(f"❌ 故障类型处理器导入失败: {e}")

def test_model_imports():
    """测试数据模型导入"""
    print("\n测试数据模型导入...")
    
    try:
        from app.models.chaos_task import ChaosTask
        print("✅ 混沌任务模型导入成功")
    except Exception as e:
        print(f"❌ 混沌任务模型导入失败: {e}")
    
    try:
        from app.models.chaos_batch_task import ChaosBatchTask, ChaosBatchTaskItem
        print("✅ 批次任务模型导入成功")
    except Exception as e:
        print(f"❌ 批次任务模型导入失败: {e}")
    
    try:
        from app.models.chaos_execution import ChaosExecution
        print("✅ 执行记录模型导入成功")
    except Exception as e:
        print(f"❌ 执行记录模型导入失败: {e}")

def test_service_imports():
    """测试服务层导入"""
    print("\n测试服务层导入...")
    
    try:
        from app.services.chaos.task_service import ChaosTaskService
        print("✅ 任务服务导入成功")
    except Exception as e:
        print(f"❌ 任务服务导入失败: {e}")
    
    try:
        from app.services.chaos.execution_service import ChaosExecutionService
        print("✅ 执行服务导入成功")
    except Exception as e:
        print(f"❌ 执行服务导入失败: {e}")
    
    try:
        from app.services.chaos.batch_task_service import ChaosBatchTaskService
        print("✅ 批次任务服务导入成功")
    except Exception as e:
        print(f"❌ 批次任务服务导入失败: {e}")

def test_schema_imports():
    """测试Schema导入"""
    print("\n测试Schema导入...")
    
    try:
        from app.schemas.chaos.task_schemas import ChaosTaskCreate, ChaosTaskResponse
        print("✅ 任务Schema导入成功")
    except Exception as e:
        print(f"❌ 任务Schema导入失败: {e}")
    
    try:
        from app.schemas.chaos.execution_schemas import ChaosExecutionResponse
        print("✅ 执行Schema导入成功")
    except Exception as e:
        print(f"❌ 执行Schema导入失败: {e}")
    
    try:
        from app.schemas.chaos.batch_task_schemas import ChaosBatchTaskCreate
        print("✅ 批次任务Schema导入成功")
    except Exception as e:
        print(f"❌ 批次任务Schema导入失败: {e}")

def test_api_imports():
    """测试API导入"""
    print("\n测试API导入...")
    
    try:
        from app.api.v1.chaos.tasks import router as tasks_router
        print("✅ 任务API导入成功")
    except Exception as e:
        print(f"❌ 任务API导入失败: {e}")
    
    try:
        from app.api.v1.chaos.executions import router as executions_router
        print("✅ 执行API导入成功")
    except Exception as e:
        print(f"❌ 执行API导入失败: {e}")

def test_task_imports():
    """测试Celery任务导入"""
    print("\n测试Celery任务导入...")
    
    try:
        from app.tasks.chaos.execution_tasks import execute_single_chaos_task
        print("✅ 执行任务导入成功")
    except Exception as e:
        print(f"❌ 执行任务导入失败: {e}")
    
    try:
        from app.tasks.chaos.schedule_tasks import schedule_chaos_task
        print("✅ 调度任务导入成功")
    except Exception as e:
        print(f"❌ 调度任务导入失败: {e}")
    
    try:
        from app.tasks.chaos.cleanup_tasks import cleanup_expired_faults
        print("✅ 清理任务导入成功")
    except Exception as e:
        print(f"❌ 清理任务导入失败: {e}")

def test_fault_types():
    """测试故障类型"""
    print("\n测试故障类型...")
    
    try:
        from app.core.chaos.fault_types import get_supported_fault_types
        fault_types = get_supported_fault_types()
        print(f"✅ 支持的故障类型: {fault_types}")
    except Exception as e:
        print(f"❌ 获取故障类型失败: {e}")

def main():
    """主函数"""
    print("🚀 开始测试混沌测试模块导入...")
    print("=" * 50)
    
    test_core_imports()
    test_model_imports()
    test_service_imports()
    test_schema_imports()
    test_api_imports()
    test_task_imports()
    test_fault_types()
    
    print("\n" + "=" * 50)
    print("✨ 导入测试完成！")

if __name__ == '__main__':
    main()
