# 混沌测试模块迁移指南

## 📋 迁移概述

本指南详细说明了混沌测试模块从原APScheduler架构迁移到新Celery+Redis分布式架构的完整过程。

## 🏗️ 新架构特性

### 核心优势
- ✅ **分布式支持** - 多节点集群部署
- ✅ **高可用性** - 任务持久化和自动重试
- ✅ **水平扩展** - 支持动态添加Worker节点
- ✅ **可视化监控** - Flower监控界面
- ✅ **模块化设计** - 故障注入组件独立封装

### 技术栈
- **任务队列**: Celery 5.5.3
- **消息代理**: Redis
- **数据库**: MySQL
- **监控**: Flower
- **故障注入**: ChaosBlade

## 🚀 部署步骤

### 1. 环境准备

#### 安装依赖
```bash
# 安装Celery
pip install celery==5.5.3

# 安装Flower监控
pip install flower

# 安装Redis客户端
pip install redis
```

#### 启动Redis服务
```bash
# Windows (使用Redis for Windows)
redis-server

# Linux/macOS
sudo systemctl start redis
# 或
redis-server /etc/redis/redis.conf
```

### 2. 数据库迁移

#### 执行数据库迁移
```bash
# 进入项目目录
cd backend_new

# 执行迁移
alembic upgrade head
```

#### 验证表创建
```sql
-- 检查新创建的表
SHOW TABLES LIKE 'chaos_%';

-- 应该看到以下表：
-- chaos_tasks
-- chaos_batch_tasks  
-- chaos_batch_task_items
-- chaos_executions
-- chaos_scenarios
```

### 3. 启动Celery服务

#### Windows环境
```bash
# 启动所有服务
start_celery.bat
```

#### Linux/macOS环境
```bash
# 给脚本执行权限
chmod +x start_celery.sh stop_celery.sh

# 启动所有服务
./start_celery.sh
```

#### 手动启动（开发调试）
```bash
# 启动Worker
python celery_worker.py

# 启动Beat调度器
python celery_beat.py

# 启动Flower监控
python celery_monitor.py
```

### 4. 验证部署

#### 检查服务状态
```bash
# 检查Celery Worker状态
celery -A app.core.celery_app inspect active

# 检查队列状态
celery -A app.core.celery_app inspect active_queues
```

#### 访问监控界面
- Flower监控: http://localhost:5555
- 查看队列状态、任务执行情况、Worker节点信息

## 🧪 功能测试

### 1. API接口测试

#### 创建混沌测试任务
```bash
curl -X POST "http://localhost:8000/api/chaos/tasks" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "CPU压力测试",
    "description": "测试CPU故障注入",
    "env_ids": [1],
    "fault_type": "cpu",
    "fault_params": {
      "cpu_percent": 80,
      "timeout": 60
    },
    "execution_type": "immediate",
    "auto_destroy": true,
    "max_duration": 120
  }'
```

#### 查询任务列表
```bash
curl -X GET "http://localhost:8000/api/chaos/tasks?page=1&size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 执行任务
```bash
curl -X POST "http://localhost:8000/api/chaos/tasks/1/execute" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "force_execute": false
  }'
```

### 2. 故障类型测试

#### CPU故障
```json
{
  "fault_type": "cpu",
  "fault_params": {
    "cpu_percent": 80,
    "cpu_count": 2,
    "timeout": 60
  }
}
```

#### 内存故障
```json
{
  "fault_type": "memory", 
  "fault_params": {
    "mem_percent": 70,
    "timeout": 60
  }
}
```

#### 网络故障
```json
{
  "fault_type": "network",
  "fault_params": {
    "fault_action": "delay",
    "interface": "eth0",
    "time": 100,
    "timeout": 60
  }
}
```

### 3. 调度功能测试

#### 定时任务
```json
{
  "execution_type": "scheduled",
  "scheduled_time": "2025-08-05T10:00:00"
}
```

#### 周期任务
```json
{
  "execution_type": "periodic",
  "periodic_config": {
    "interval_type": "minutes",
    "interval_value": 30
  }
}
```

#### Cron任务
```json
{
  "execution_type": "cron",
  "cron_expression": "0 9 * * 1-5"
}
```

## 📊 监控和维护

### 1. 日志查看
```bash
# Worker日志
tail -f logs/celery_worker.log

# Beat调度器日志  
tail -f logs/celery_beat.log

# Flower监控日志
tail -f logs/celery_flower_startup.log
```

### 2. 性能监控
- **Flower界面**: 实时查看任务执行状态
- **Redis监控**: 查看队列长度和内存使用
- **数据库监控**: 查看执行记录和任务统计

### 3. 故障排查

#### 常见问题
1. **Redis连接失败**
   - 检查Redis服务状态
   - 验证连接配置

2. **任务执行失败**
   - 查看Worker日志
   - 检查目标主机连接
   - 验证ChaosBlade安装

3. **调度不生效**
   - 检查Beat调度器状态
   - 验证时间配置

## 🔧 配置说明

### Celery配置
- **并发数**: 默认4个Worker进程
- **任务超时**: 300秒硬超时，240秒软超时
- **重试策略**: 最多重试3次，间隔60秒

### 队列配置
- `chaos_execution`: 故障注入执行
- `chaos_schedule`: 任务调度
- `chaos_cleanup`: 清理维护
- `default`: 默认队列

### 自动清理
- **过期故障**: 每5分钟清理
- **运行状态检查**: 每1分钟
- **历史记录清理**: 每1小时

## 📈 扩展部署

### 多节点部署
```bash
# 节点1: 主节点 (Worker + Beat + Flower)
./start_celery.sh

# 节点2: Worker节点
python celery_worker.py --hostname=worker2@%h

# 节点3: Worker节点  
python celery_worker.py --hostname=worker3@%h
```

### 队列专用节点
```bash
# 专用执行节点
celery worker -A app.core.celery_app --queues=chaos_execution --hostname=executor@%h

# 专用调度节点
celery worker -A app.core.celery_app --queues=chaos_schedule --hostname=scheduler@%h
```

## 🎯 迁移完成检查清单

- [ ] Redis服务正常运行
- [ ] 数据库迁移成功执行
- [ ] Celery Worker启动正常
- [ ] Celery Beat调度器运行
- [ ] Flower监控界面可访问
- [ ] API接口响应正常
- [ ] 任务创建和执行成功
- [ ] 故障注入功能正常
- [ ] 自动清理机制工作
- [ ] 监控和日志正常

## 📞 技术支持

如遇到问题，请检查：
1. 服务日志文件
2. Flower监控界面
3. Redis连接状态
4. 数据库表结构

迁移完成后，新的混沌测试模块将提供更强大、更可靠的故障注入能力！
