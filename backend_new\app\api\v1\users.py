"""
用户管理API路由
"""
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_redis_client, get_current_superuser, CommonQueryParams
from app.db.redis import RedisClient
from app.schemas.user import (
    UserCreate, UserUpdate, UserResponse, UserPageResponse, UserQuery
)
from app.schemas.base import response_builder
from app.services.user import UserService

router = APIRouter()


@router.get("", response_model=UserPageResponse, summary="查询用户列表")
async def list_users(
    query: UserQuery = Depends(),
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user = Depends(get_current_superuser)
):
    """查询用户列表，支持关键词搜索、状态筛选和分页"""
    user_service = UserService(db, redis_client)
    result = await user_service.list_users(query)
    return result


@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user = Depends(get_current_superuser)
):
    """获取指定ID的用户详情"""
    user_service = UserService(db, redis_client)
    result = await user_service.get_user_by_id(user_id)
    return result


@router.post("", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user = Depends(get_current_superuser)
):
    """创建新用户，需要管理员权限"""
    user_service = UserService(db, redis_client)
    result = await user_service.create_user(user_in)
    return result


@router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    user_id: int,
    user_in: UserUpdate,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    _current_user = Depends(get_current_superuser)
):
    """更新用户信息，支持修改基本信息和密码"""
    user_service = UserService(db, redis_client)
    result = await user_service.update_user(user_id, user_in)
    return result


@router.delete("/{user_id}", status_code=204, summary="删除用户")
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    redis_client: RedisClient = Depends(get_redis_client),
    current_user = Depends(get_current_superuser)
):
    """删除用户，不允许删除超级管理员和自己"""
    if user_id == current_user.id:
        from app.core.exceptions import raise_validation_error
        raise_validation_error("不能删除自己的账户")
    
    user_service = UserService(db, redis_client)
    await user_service.delete_user(user_id)
    return None
