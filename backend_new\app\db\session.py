"""
数据库会话管理模块
配置SQLAlchemy数据库连接和会话
"""
from typing import Generator, AsyncGenerator  # 导入生成器类型
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    settings.database_url,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False,                   # 禁用SQL语句日志（关键：不再打印SQL）
    echo_pool=False,              # 禁用连接池操作日志
)

# 创建异步数据库引擎
async_engine = create_async_engine(
    settings.database_url.replace("mysql+pymysql://", "mysql+asyncmy://"),
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False,                   # 禁用SQL语句日志（关键：不再打印SQL）
    echo_pool=False,              # 禁用连接池操作日志
)

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False
)

# 创建基础模型类
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:  # 修正同步生成器类型注解
    """
    获取数据库会话（同步）
    用于依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:  # 修正异步生成器类型注解
    """
    获取数据库会话（异步）
    用于依赖注入
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Async database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db():
    """
    初始化数据库
    创建所有表
    """
    try:
        async with async_engine.begin() as conn:
            # 导入所有模型以确保它们被注册
            from app.models import User, Role, Environment, ModelConfig  # noqa

            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        # 数据库初始化失败不阻止应用启动（开发阶段）
        logger.warning("Database initialization failed, continuing without database")


async def close_db():
    """
    关闭数据库连接
    """
    try:
        await async_engine.dispose()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Failed to close database connections: {e}")
        raise