"""
通用模型调用客户端
使用OpenAI库进行统一封装，支持多种AI平台
"""
import time
import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from openai import AsyncOpenAI
import logging

from app.models.model_config import ModelConfig

logger = logging.getLogger(__name__)


class ModelClient:
    """通用模型调用客户端"""
    
    def __init__(self, model_config: ModelConfig, skip_network_test: bool = False):
        self.model_config = model_config
        self.timeout = model_config.timeout_seconds or 30
        self._client = None
        
        # 根据平台配置不同的客户端
        self._setup_client()
        
        if not skip_network_test:
            # 可以在这里添加网络连接测试
            pass
    
    def _setup_client(self):
        """根据平台设置客户端"""
        try:
            # 使用OpenAI库的通用接口
            self._client = AsyncOpenAI(
                api_key=self.model_config.api_key or "dummy-key",
                base_url=self.model_config.api_url,
                timeout=self.timeout
            )
            logger.info(f"初始化模型客户端: {self.model_config.name} ({self.model_config.platform})")
        except Exception as e:
            logger.error(f"初始化模型客户端失败: {str(e)}")
            raise
    
    def _format_messages(self, prompt: str) -> list:
        """格式化消息"""
        messages = []
        
        # 添加系统提示词
        if self.model_config.prompt:
            messages.append({
                "role": "system",
                "content": self.model_config.prompt
            })
        
        # 添加用户消息
        messages.append({
            "role": "user",
            "content": prompt
        })
        
        return messages
    
    async def call_model(self, prompt: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """调用模型（非流式）"""
        start_time = time.time()
        
        logger.info(f"开始调用模型: {self.model_config.name}")
        logger.debug(f"参数: prompt长度={len(prompt)}, parameters={parameters}")
        
        try:
            messages = self._format_messages(prompt)
            
            # 合并参数
            call_params = {
                'model': self.model_config.model_name,
                'messages': messages,
                'max_tokens': parameters.get('max_tokens', self.model_config.max_tokens) if parameters else self.model_config.max_tokens,
                'timeout': self.timeout
            }
            
            # 添加其他参数
            if parameters:
                for key, value in parameters.items():
                    if key not in ['max_tokens'] and value is not None:
                        call_params[key] = value
            
            logger.info(f"调用参数: model={call_params['model']}, max_tokens={call_params['max_tokens']}")
            
            # 调用模型
            response = await self._client.chat.completions.create(**call_params)
            
            response_time = int((time.time() - start_time) * 1000)
            
            # 提取响应内容
            content = ""
            if response.choices and len(response.choices) > 0:
                content = response.choices[0].message.content or ""
            
            # 提取使用信息
            usage = None
            if hasattr(response, 'usage') and response.usage:
                usage = {
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                }
            
            logger.info(f"模型调用成功: {self.model_config.name}, 响应时间: {response_time}ms")
            
            return {
                'success': True,
                'response': content,
                'response_time_ms': response_time,
                'error_message': None,
                'usage': usage
            }
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            
            logger.error(f"模型 {self.model_config.name} 调用失败: {error_msg}")
            
            return {
                'success': False,
                'response': None,
                'response_time_ms': response_time,
                'error_message': error_msg,
                'usage': None
            }
    
    async def call_model_stream(self, prompt: str, parameters: Optional[Dict[str, Any]] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """调用模型（流式）"""
        start_time = time.time()
        
        logger.info(f"开始流式调用模型: {self.model_config.name}")
        logger.debug(f"参数: prompt长度={len(prompt)}, parameters={parameters}")
        
        try:
            messages = self._format_messages(prompt)
            
            # 合并参数
            call_params = {
                'model': self.model_config.model_name,
                'messages': messages,
                'max_tokens': parameters.get('max_tokens', self.model_config.max_tokens) if parameters else self.model_config.max_tokens,
                'stream': True,
                'timeout': self.timeout
            }
            
            # 添加其他参数
            if parameters:
                for key, value in parameters.items():
                    if key not in ['max_tokens'] and value is not None:
                        call_params[key] = value
            
            logger.info(f"流式调用参数: model={call_params['model']}, max_tokens={call_params['max_tokens']}")
            
            # 创建流式请求
            stream = await self._client.chat.completions.create(**call_params)
            
            # 处理流式响应
            chunk_count = 0
            logger.info("开始处理流式响应")
            
            async for chunk in stream:
                chunk_count += 1
                if chunk_count <= 3:
                    logger.debug(f"收到原始chunk #{chunk_count}: {chunk}")
                
                # 处理内容块
                if chunk.choices and chunk.choices[0].delta.content:
                    content_chunk = {
                        'type': 'content',
                        'content': chunk.choices[0].delta.content,
                        'finish_reason': chunk.choices[0].finish_reason
                    }
                    if chunk_count <= 3:
                        logger.debug(f"生成内容chunk: {content_chunk}")
                    yield content_chunk
                
                # 处理结束
                if chunk.choices and chunk.choices[0].finish_reason:
                    response_time = int((time.time() - start_time) * 1000)
                    done_chunk = {
                        'type': 'done',
                        'response_time_ms': response_time,
                        'finish_reason': chunk.choices[0].finish_reason
                    }
                    logger.info(f"流式调用完成: {done_chunk}")
                    yield done_chunk
                    break
            
            logger.info(f"流式响应处理完成，总共处理了 {chunk_count} 个chunks")
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            error_type = type(e).__name__
            
            logger.error(f"模型 {self.model_config.name} 流式调用失败:")
            logger.error(f"  错误类型: {error_type}")
            logger.error(f"  错误信息: {error_msg}")
            logger.error(f"  API URL: {self.model_config.api_url}")
            logger.error(f"  模型名称: {self.model_config.model_name}")
            
            error_chunk = {
                'type': 'error',
                'error_message': error_msg,
                'response_time_ms': response_time
            }
            logger.debug(f"返回错误chunk: {error_chunk}")
            yield error_chunk
    
    async def close(self):
        """关闭客户端连接"""
        if self._client:
            await self._client.close()
            self._client = None
            logger.debug(f"关闭模型客户端: {self.model_config.name}")


class ModelClientFactory:
    """模型客户端工厂"""
    
    @staticmethod
    def create_client(model_config: ModelConfig, skip_network_test: bool = False) -> ModelClient:
        """创建模型客户端"""
        return ModelClient(model_config, skip_network_test)
    
    @staticmethod
    def get_supported_platforms() -> list:
        """获取支持的平台列表"""
        return [
            'local',       # 本地模型
            'doubao',      # 豆包 - 字节跳动
            'deepseek',    # DeepSeek
            'qwen',        # 通义千问 - 阿里巴巴
            'ernie',       # 文心一言 - 百度
            'chatglm',     # 智谱AI
            'openai',      # OpenAI
            'claude'       # Anthropic Claude
        ]
