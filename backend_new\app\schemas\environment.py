"""
环境管理Pydantic模式
"""
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_validator
from app.schemas.base import BaseSchema, BaseResponseSchema, PaginationSchema, PaginationResponseSchema


class EnvironmentBase(BaseSchema):
    """环境基础模型（包含公共字段）"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="环境名称")
    type: Optional[str] = Field(None, min_length=1, max_length=50, description="环境类型")
    description: Optional[str] = Field(None, max_length=1000, description="环境描述")
    host: Optional[str] = Field(None, max_length=255, description="主机地址")
    port: Optional[int] = Field(None, ge=1, le=65535, description="端口号")
    config: Optional[Dict[str, Any]] = Field(None, description="环境配置")
    tags: Optional[str] = Field(None, max_length=500, description="环境标签，逗号分隔")

    @field_validator('type')
    @classmethod
    def validate_type(cls, v):
        """验证环境类型"""
        if v is None:
            return v
        allowed_types = [
            'database', 'redis', 'ssh', 'k8s', 'api', 'kafka',
            'mongodb', 'elasticsearch', 'rabbitmq', 'minio', 'opensearch'
        ]
        if v.lower() not in allowed_types:
            raise ValueError(f'环境类型必须是以下之一: {", ".join(allowed_types)}')
        return v.lower()

    @field_validator('config')
    @classmethod
    def validate_config(cls, v, info):
        """根据环境类型验证配置"""
        if not v:
            return v
        
        # 获取其他字段的值
        data = info.data if hasattr(info, 'data') else {}
        env_type = data.get('type')
        if not env_type:
            return v
            
        # 数据库类型必须的配置项
        if env_type == 'database':
            required_fields = ['db_type', 'database_name']
            for field in required_fields:
                if field not in v:
                    raise ValueError(f'数据库类型环境必须包含配置项: {field}')
        
        # Redis类型必须的配置项
        elif env_type == 'redis':
            if 'password' not in v:
                v['password'] = ''  # Redis密码可以为空
        
        # SSH类型必须的配置项
        elif env_type == 'ssh':
            required_fields = ['username']
            for field in required_fields:
                if field not in v:
                    raise ValueError(f'SSH类型环境必须包含配置项: {field}')
        
        return v


class EnvironmentCreate(EnvironmentBase):
    """创建环境请求模型（必填字段）"""
    name: str = Field(..., min_length=1, max_length=100, description="环境名称")
    type: str = Field(..., min_length=1, max_length=50, description="环境类型")


class EnvironmentUpdate(EnvironmentBase):
    """更新环境请求模型（可选字段）"""
    pass


class EnvironmentQuery(PaginationSchema):
    """环境查询参数模型"""
    keyword: Optional[str] = Field(None, description="搜索关键词")
    env_type: Optional[str] = Field(None, description="环境类型筛选")
    status: Optional[str] = Field(None, description="状态筛选")
    tags: Optional[str] = Field(None, description="标签筛选")


class EnvironmentResponse(EnvironmentBase, BaseResponseSchema):
    """环境详情响应模型"""
    id: int = Field(..., description="环境ID")
    name: str = Field(..., description="环境名称")
    type: str = Field(..., description="环境类型")
    status: str = Field(default="unknown", description="连接状态")
    last_test_time: Optional[str] = Field(None, description="最后测试时间")
    tag_list: List[str] = Field(default_factory=list, description="标签列表")

    model_config = {"from_attributes": True}

    def __init__(self, **data):
        super().__init__(**data)
        # 自动生成tag_list
        if hasattr(self, 'tags') and self.tags:
            self.tag_list = [tag.strip() for tag in self.tags.split(",") if tag.strip()]
        else:
            self.tag_list = []


class EnvironmentPageResponse(BaseModel):
    """环境列表分页响应模型"""
    items: List[EnvironmentResponse] = Field(..., description="当前页环境列表")
    total: int = Field(..., description="总条数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页条数")
    pages: int = Field(..., description="总页数")


class ConnectionTestRequest(BaseSchema):
    """连接测试请求模式"""
    type: Optional[str] = Field(None, description="环境类型")
    config: Optional[Dict[str, Any]] = Field(None, description="连接配置")
    timeout: Optional[int] = Field(10, ge=1, le=60, description="超时时间(秒)")


class ConnectionTestResponse(BaseSchema):
    """连接测试响应模式"""
    success: bool = Field(description="连接是否成功")
    message: str = Field(description="测试结果消息")
    duration: float = Field(description="连接耗时(秒)")
    details: Optional[Dict[str, Any]] = Field(None, description="连接详情")


class EnvironmentStatsResponse(BaseSchema):
    """环境统计响应模式"""
    total_count: int = Field(description="环境总数")
    type_stats: Dict[str, int] = Field(description="类型统计")
    status_stats: Dict[str, int] = Field(description="状态统计")
    recent_environments: List[EnvironmentResponse] = Field(description="最近环境")


class SupportedTypeResponse(BaseSchema):
    """支持的环境类型响应模式"""
    type: str = Field(description="类型代码")
    display_name: str = Field(description="显示名称")
    description: str = Field(description="类型描述")
    default_port: Optional[int] = Field(None, description="默认端口")
    required_fields: List[str] = Field(description="必填字段")
    optional_fields: List[str] = Field(description="可选字段")
    icon: Optional[str] = Field(None, description="图标")
    category: Optional[str] = Field(None, description="分类")
