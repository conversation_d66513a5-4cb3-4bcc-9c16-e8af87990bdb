<template>
  <div class="task-progress-bar">
    <div class="progress-container">
      <el-progress
        :percentage="displayProgress"
        :status="progressStatus"
        :stroke-width="8"
        :show-text="false"
        class="progress-bar"
      />
      <div class="progress-text">
        <span class="progress-value">{{ displayProgress }}%</span>
        <span v-if="status === 'running'" class="progress-animation">
          <el-icon class="loading-icon">
            <Loading />
          </el-icon>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Loading } from '@element-plus/icons-vue'

interface Props {
  progress: number
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
}

const props = defineProps<Props>()

// 显示进度
const displayProgress = computed(() => {
  // 确保进度在0-100之间
  return Math.max(0, Math.min(100, props.progress || 0))
})

// 进度条状态
const progressStatus = computed(() => {
  switch (props.status) {
    case 'completed':
      return 'success'
    case 'failed':
      return 'exception'
    case 'cancelled':
      return 'warning'
    case 'running':
      return undefined // 默认蓝色
    case 'pending':
      return undefined
    default:
      return undefined
  }
})
</script>

<style scoped lang="scss">
.task-progress-bar {
  .progress-container {
    position: relative;
    width: 100%;

    .progress-bar {
      width: 100%;
    }

    .progress-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      background: var(--el-bg-color);
      padding: 0 4px;
      border-radius: 2px;

      .progress-value {
        line-height: 1;
      }

      .progress-animation {
        display: flex;
        align-items: center;

        .loading-icon {
          font-size: 12px;
          color: var(--el-color-primary);
          animation: rotate 1s linear infinite;
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
