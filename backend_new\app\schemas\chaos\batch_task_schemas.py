"""
混沌测试批次任务Schema定义
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from app.schemas.base import BaseResponseSchema, BaseSchema


class ChaosBatchTaskItemBase(BaseSchema):
    """批次任务子项基础Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="子任务名称")
    description: Optional[str] = Field(None, description="子任务描述")
    fault_type: str = Field(..., description="故障类型")
    fault_params: Dict[str, Any] = Field(..., description="故障参数配置")
    auto_destroy: bool = Field(default=True, description="是否自动销毁故障")
    max_duration: Optional[int] = Field(None, ge=1, description="最大执行时长(秒)")
    is_enabled: bool = Field(default=True, description="是否启用")

    @validator('fault_type')
    def validate_fault_type(cls, v):
        valid_types = ['cpu', 'memory', 'network', 'disk', 'process', 'k8s']
        if v not in valid_types:
            raise ValueError(f'故障类型必须是 {valid_types} 中的一个')
        return v


class ChaosBatchTaskItemCreate(ChaosBatchTaskItemBase):
    """创建批次任务子项Schema"""
    pass


class ChaosBatchTaskItemResponse(ChaosBatchTaskItemBase, BaseResponseSchema):
    """批次任务子项响应Schema"""
    execution_order: int = Field(..., description="执行顺序")

    class Config:
        from_attributes = True


class ChaosBatchTaskBase(BaseSchema):
    """混沌测试批次任务基础Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="批次任务名称")
    description: Optional[str] = Field(None, description="批次任务描述")
    env_ids: List[int] = Field(..., min_items=1, description="目标环境ID列表")
    execution_type: str = Field(default="immediate", description="执行类型")
    scheduled_time: Optional[datetime] = Field(None, description="定时执行时间")
    batch_execution_mode: str = Field(default="sequential", description="批次执行模式")
    batch_interval: int = Field(default=0, ge=0, description="批次间隔时间(秒)")
    monitor_config: Optional[Dict[str, Any]] = Field(None, description="监控配置")

    @validator('execution_type')
    def validate_execution_type(cls, v):
        valid_types = ['immediate', 'scheduled']
        if v not in valid_types:
            raise ValueError(f'执行类型必须是 {valid_types} 中的一个')
        return v

    @validator('batch_execution_mode')
    def validate_batch_execution_mode(cls, v):
        valid_modes = ['sequential', 'parallel']
        if v not in valid_modes:
            raise ValueError(f'批次执行模式必须是 {valid_modes} 中的一个')
        return v

    @validator('scheduled_time')
    def validate_scheduled_time(cls, v, values):
        if values.get('execution_type') == 'scheduled' and not v:
            raise ValueError('定时执行类型必须指定执行时间')
        return v


class ChaosBatchTaskCreate(ChaosBatchTaskBase):
    """创建混沌测试批次任务Schema"""
    batch_items: List[ChaosBatchTaskItemCreate] = Field(..., min_items=1, description="批次任务子项列表")


class ChaosBatchTaskUpdate(BaseSchema):
    """更新混沌测试批次任务Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="批次任务名称")
    description: Optional[str] = Field(None, description="批次任务描述")
    env_ids: Optional[List[int]] = Field(None, min_items=1, description="目标环境ID列表")
    execution_type: Optional[str] = Field(None, description="执行类型")
    scheduled_time: Optional[datetime] = Field(None, description="定时执行时间")
    batch_execution_mode: Optional[str] = Field(None, description="批次执行模式")
    batch_interval: Optional[int] = Field(None, ge=0, description="批次间隔时间(秒)")
    task_status: Optional[str] = Field(None, description="任务状态")
    monitor_config: Optional[Dict[str, Any]] = Field(None, description="监控配置")


class ChaosBatchTaskResponse(ChaosBatchTaskBase, BaseResponseSchema):
    """混沌测试批次任务响应Schema"""
    task_status: str = Field(..., description="任务状态")
    last_execution_time: Optional[datetime] = Field(None, description="最后执行时间")
    execution_count: int = Field(default=0, description="执行次数")
    batch_items: List[ChaosBatchTaskItemResponse] = Field(default=[], description="批次任务子项列表")

    class Config:
        from_attributes = True


class ChaosBatchTaskQuery(BaseSchema):
    """混沌测试批次任务查询Schema"""
    keyword: Optional[str] = Field(None, description="关键词搜索")
    execution_type: Optional[str] = Field(None, description="执行类型筛选")
    batch_execution_mode: Optional[str] = Field(None, description="批次执行模式筛选")
    task_status: Optional[str] = Field(None, description="任务状态筛选")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")

    @property
    def offset(self) -> int:
        return (self.page - 1) * self.size


class ChaosBatchTaskPageResponse(BaseSchema):
    """混沌测试批次任务分页响应Schema"""
    items: List[ChaosBatchTaskResponse] = Field(..., description="批次任务列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    pages: int = Field(..., description="总页数")


class ChaosBatchTaskExecuteRequest(BaseSchema):
    """混沌测试批次任务执行请求Schema"""
    env_ids: Optional[List[int]] = Field(None, description="指定执行的环境ID列表，不指定则使用任务配置")
    force_execute: bool = Field(default=False, description="是否强制执行（忽略状态检查）")
    execution_config: Optional[Dict[str, Any]] = Field(None, description="执行配置覆盖")


class ChaosBatchTaskExecuteResponse(BaseSchema):
    """混沌测试批次任务执行响应Schema"""
    batch_task_id: int = Field(..., description="批次任务ID")
    celery_task_id: str = Field(..., description="Celery任务ID")
    execution_type: str = Field(..., description="执行类型")
    status: str = Field(..., description="提交状态")
    message: str = Field(..., description="提交结果消息")
    scheduled_time: Optional[datetime] = Field(None, description="调度时间")


class ChaosBatchTaskStatsResponse(BaseSchema):
    """混沌测试批次任务统计响应Schema"""
    total_batch_tasks: int = Field(..., description="总批次任务数")
    enabled_batch_tasks: int = Field(..., description="启用批次任务数")
    disabled_batch_tasks: int = Field(..., description="禁用批次任务数")
    running_batch_executions: int = Field(..., description="运行中批次执行数")
    today_batch_executions: int = Field(..., description="今日批次执行数")
    execution_type_stats: Dict[str, int] = Field(..., description="执行类型统计")
    batch_execution_mode_stats: Dict[str, int] = Field(..., description="批次执行模式统计")
