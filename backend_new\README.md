# DpTestPlatform Backend

基于 FastAPI + SQLAlchemy + Pydantic V2 的现代化后端API服务

## 技术栈

- **Web框架**: FastAPI 0.104.1
- **ORM**: SQLAlchemy 2.0 (异步)
- **数据验证**: Pydantic V2
- **数据库**: MySQL
- **缓存**: Redis
- **认证**: JWT <PERSON>
- **数据库迁移**: Alembic
- **日志**: Loguru

## 项目结构

```
backend_new/
├── app/                     # 应用核心代码
│   ├── main.py             # 应用主入口
│   ├── api/                # API路由层
│   │   ├── deps.py         # 依赖注入定义
│   │   ├── router.py       # 路由管理器
│   │   └── v1/             # API版本1
│   ├── core/               # 核心功能模块
│   │   ├── config.py       # 配置管理
│   │   ├── security.py     # 安全工具
│   │   └── exceptions.py   # 异常定义
│   ├── db/                 # 数据库模块
│   │   ├── session.py      # 数据库会话管理
│   │   └── redis.py        # Redis缓存
│   ├── models/             # 数据模型层
│   │   └── base.py         # 基础模型类
│   ├── crud/               # 数据访问层
│   │   └── base.py         # 基础CRUD类
│   ├── schemas/            # 数据传输对象
│   │   └── base.py         # 基础Schema
│   └── utils/              # 工具函数
│       └── logger.py       # 日志配置
├── alembic/                # 数据库迁移
├── requirements.txt        # Python依赖
├── run.py                  # 启动脚本
├── .env.example           # 环境变量示例
└── README.md              # 项目说明
```

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑 .env 文件，配置数据库和Redis连接信息
```

### 3. 数据库初始化

```bash
# 生成迁移文件
alembic revision --autogenerate -m "Initial migration"

# 执行迁移
alembic upgrade head

# 初始化默认数据（创建超级管理员）
python scripts/init_db.py
```

### 4. 启动应用

```bash
# 开发模式启动
python run.py

# 或使用uvicorn直接启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 5. 访问API文档

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
- 健康检查: http://localhost:8000/health/

### 6. 默认账户

初始化数据库后，系统会创建默认的超级管理员账户：
- 用户名: `admin`
- 密码: `admin123`
- 邮箱: `<EMAIL>`

可以使用此账户登录系统并管理其他用户。

## 架构设计

### 分层架构模式

```
API层 (api/)          ←→ HTTP请求和响应，参数验证，路由定义
    ↓
业务逻辑层 (services/) ←→ 核心业务逻辑，业务规则实现
    ↓
数据访问层 (crud/)     ←→ 数据访问抽象，查询逻辑封装
    ↓
数据模型层 (models/)   ←→ 数据库实体定义，关系映射
```

### 核心特性

- **异步支持**: 全面使用异步编程，提高并发性能
- **类型安全**: 使用Pydantic V2进行数据验证和序列化
- **依赖注入**: FastAPI的依赖注入系统，便于测试和维护
- **统一异常处理**: 自定义异常类和全局异常处理器
- **日志系统**: 基于Loguru的结构化日志
- **缓存支持**: Redis缓存集成，用户会话管理
- **JWT认证**: 完整的JWT认证和授权机制
- **用户管理**: 完整的用户CRUD、角色权限管理
- **文件上传**: 头像上传、文件管理功能
- **静态文件**: 静态资源服务，支持头像显示

## API接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/logout` - 用户登出
- `GET /api/user/info` - 获取当前用户信息

### 用户管理接口
- `GET /api/users` - 用户列表（分页+搜索）
- `GET /api/users/{id}` - 用户详情
- `POST /api/users` - 创建用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

### 角色管理接口
- `GET /api/roles/list` - 获取角色列表

### 环境管理接口
- `GET /api/environments` - 环境列表（分页+搜索）
- `GET /api/environments/{id}` - 环境详情
- `POST /api/environments` - 创建环境
- `PUT /api/environments/{id}` - 更新环境
- `DELETE /api/environments/{id}` - 删除环境
- `POST /api/environments/test` - 测试环境连接
- `POST /api/environments/{id}/test` - 测试指定环境连接
- `POST /api/environments/batch-test` - 批量测试环境连接
- `GET /api/environments/stats/overview` - 环境统计信息
- `GET /api/environments/types/supported` - 支持的环境类型

### 模型配置管理接口
- `GET /api/model-configs` - 模型配置列表（分页+搜索）
- `GET /api/model-configs/{id}` - 模型配置详情
- `POST /api/model-configs` - 创建模型配置
- `PUT /api/model-configs/{id}` - 更新模型配置
- `DELETE /api/model-configs/{id}` - 删除模型配置
- `POST /api/model-configs/{id}/enable` - 启用模型
- `POST /api/model-configs/{id}/disable` - 停用模型
- `GET /api/model-configs/available/list` - 获取可用模型列表
- `POST /api/model-configs/{id}/health-check` - 检查指定模型健康状态
- `POST /api/model-configs/health-check/batch` - 批量健康检查
- `POST /api/model-configs/test` - 测试模型配置连接
- `POST /api/model-configs/call` - 调用模型
- `POST /api/model-configs/call/stream` - 调用模型（流式）
- `GET /api/model-configs/stats/overview` - 模型统计信息
- `GET /api/model-configs/platforms/supported` - 支持的平台类型

### 文件上传接口
- `POST /api/upload/avatar` - 上传头像
- `POST /api/upload/file` - 上传文件
- `DELETE /api/upload/file/{filename}` - 删除文件

### 系统接口
- `GET /health/` - 基础健康检查
- `GET /health/detailed` - 详细健康检查

## 开发指南

### 添加新的API端点

1. 在 `app/models/` 中定义数据模型
2. 在 `app/schemas/` 中定义Pydantic模型
3. 在 `app/crud/` 中实现CRUD操作
4. 在 `app/services/` 中实现业务逻辑
5. 在 `app/api/v1/` 中创建路由
6. 在 `app/api/router.py` 中注册路由

### 数据库迁移

```bash
# 生成迁移文件
alembic revision --autogenerate -m "描述信息"

# 执行迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_api.py

# 生成覆盖率报告
pytest --cov=app tests/
```

## 部署

### Docker部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "run.py"]
```

### 生产环境配置

1. 设置环境变量 `DEBUG=false`
2. 配置生产数据库连接
3. 设置强密码的 `SECRET_KEY`
4. 配置反向代理（Nginx）
5. 启用HTTPS

## 许可证

MIT License
