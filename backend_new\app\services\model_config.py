"""
模型配置业务服务
"""
import time
import asyncio
import httpx
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.model_config import ModelConfigCRUD
from app.schemas.model_config import (
    ModelConfigCreate, ModelConfigUpdate, ModelConfigResponse, ModelConfigQuery,
    ModelConfigPageResponse, ModelHealthCheckRequest, ModelHealthCheckResponse,
    ModelCallRequest, ModelCallResponse, ModelStatsResponse, SupportedPlatformResponse
)
from app.core.exceptions import (
    raise_not_found_error, raise_conflict_error, raise_business_error
)
from app.utils.model_client import ModelClient
from app.services.base import BaseService


class ModelConfigService(BaseService):
    """模型配置业务服务类"""

    def __init__(self, db: AsyncSession, redis_client = None):
        super().__init__(db, redis_client)
        self.model_crud = ModelConfigCRUD(db)

    async def create_model_config(self, model_data: ModelConfigCreate) -> ModelConfigResponse:
        """创建模型配置"""
        # 检查名称是否重复
        existing_model = await self.model_crud.get_by_name(model_data.name)
        if existing_model:
            raise_conflict_error(f"模型名称 '{model_data.name}' 已存在")

        model = await self.model_crud.create_model_config(model_data)
        response = self._convert_to_response(model)
        # 解析用户昵称
        await self._resolve_single_user_names(response)
        return response

    async def list_model_configs(self, query: ModelConfigQuery) -> ModelConfigPageResponse:
        """查询模型配置列表"""
        models, total = await self.model_crud.list(
            keyword=query.keyword,
            platform=query.platform,
            status=query.status,
            health_status=query.health_status,
            offset=query.offset,
            limit=query.size
        )

        # 转换为响应格式
        model_responses = [self._convert_to_response(model) for model in models]

        # 批量解析用户昵称
        await self._resolve_user_names(model_responses)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return ModelConfigPageResponse(
            items=model_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def get_model_config_by_id(self, model_id: int) -> ModelConfigResponse:
        """根据ID获取模型配置"""
        model = await self.model_crud.get(self.db, model_id)
        if not model:
            raise_not_found_error("模型配置不存在")

        response = self._convert_to_response(model)
        # 解析用户昵称
        await self._resolve_single_user_names(response)
        return response

    async def update_model_config(self, model_id: int, model_data: ModelConfigUpdate) -> ModelConfigResponse:
        """更新模型配置"""
        model = await self.model_crud.update_model_config(model_id, model_data)
        if not model:
            raise_not_found_error("模型配置不存在")

        response = self._convert_to_response(model)
        # 解析用户昵称
        await self._resolve_single_user_names(response)
        return response

    async def delete_model_config(self, model_id: int) -> bool:
        """删除模型配置"""
        model = await self.model_crud.remove(self.db, id=model_id)
        if not model:
            raise_not_found_error("模型配置不存在")

        return True

    async def enable_model(self, model_id: int) -> ModelConfigResponse:
        """启用模型"""
        model = await self.model_crud.enable_model(model_id)
        if not model:
            raise_not_found_error("模型配置不存在")

        return self._convert_to_response(model)

    async def disable_model(self, model_id: int) -> ModelConfigResponse:
        """停用模型"""
        model = await self.model_crud.disable_model(model_id)
        if not model:
            raise_not_found_error("模型配置不存在")

        return self._convert_to_response(model)

    async def get_available_models(self) -> List[ModelConfigResponse]:
        """获取所有可用的模型配置（启用且健康）"""
        models = await self.model_crud.get_available_models()
        return [self._convert_to_response(model) for model in models]

    async def health_check_model(self, model_id: int, timeout_seconds: Optional[int] = None) -> ModelHealthCheckResponse:
        """检查指定模型的健康状态"""
        model = await self.model_crud.get(self.db, model_id)
        if not model:
            raise_not_found_error("模型配置不存在")

        # 执行健康检查
        result = await self._simple_health_check(model, timeout_seconds or 10)

        # 更新模型健康状态
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await self.model_crud.update_health_status(
                model_id,
                'healthy' if result['is_healthy'] else 'unhealthy',
                current_time
            )
        except Exception:
            # 记录更新失败，但不影响健康检查结果返回
            pass

        return ModelHealthCheckResponse(
            model_id=model.id,
            model_name=model.name,
            is_healthy=result['is_healthy'],
            response_time_ms=result.get('response_time_ms'),
            error_message=result.get('error_message'),
            check_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

    async def batch_health_check(self, model_ids: Optional[List[int]] = None, timeout_seconds: Optional[int] = None) -> List[ModelHealthCheckResponse]:
        """批量健康检查"""
        if model_ids:
            models = await self.model_crud.get_models_by_ids(model_ids)
        else:
            models = await self.model_crud.get_enabled_models()

        # 并发健康检查（限制并发数）
        semaphore = asyncio.Semaphore(3)  # 最多3个并发健康检查
        
        async def check_single_model(model):
            async with semaphore:
                try:
                    result = await self._simple_health_check(model, timeout_seconds or 10)
                    
                    # 更新模型健康状态
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    await self.model_crud.update_health_status(
                        model.id,
                        'healthy' if result['is_healthy'] else 'unhealthy',
                        current_time
                    )
                    
                    return ModelHealthCheckResponse(
                        model_id=model.id,
                        model_name=model.name,
                        is_healthy=result['is_healthy'],
                        response_time_ms=result.get('response_time_ms'),
                        error_message=result.get('error_message'),
                        check_time=current_time
                    )
                    
                except Exception as e:
                    return ModelHealthCheckResponse(
                        model_id=model.id,
                        model_name=model.name,
                        is_healthy=False,
                        response_time_ms=None,
                        error_message=f"健康检查异常: {str(e)}",
                        check_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    )
        
        # 并发执行健康检查
        tasks = [check_single_model(model) for model in models]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                model = models[i]
                final_results.append(ModelHealthCheckResponse(
                    model_id=model.id,
                    model_name=model.name,
                    is_healthy=False,
                    response_time_ms=None,
                    error_message=f"健康检查异常: {str(result)}",
                    check_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ))
            else:
                final_results.append(result)
        
        return final_results

    async def test_model_connection(self, model_data: ModelConfigCreate) -> Dict[str, Any]:
        """测试模型配置连接（不保存到数据库）"""
        try:
            # 简化的连接测试
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(model_data.api_url)
                
            duration_ms = int((time.time() - start_time) * 1000)
            
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response_time_ms": duration_ms,
                "message": "连接测试成功" if response.status_code == 200 else f"HTTP {response.status_code}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"连接测试失败: {str(e)}"
            }

    async def get_model_stats(self) -> ModelStatsResponse:
        """获取模型统计信息"""
        try:
            # 平台统计
            platform_stats = await self.model_crud.get_platform_stats()
            
            # 状态统计
            status_stats = await self.model_crud.get_status_stats()
            
            # 健康状态统计
            health_stats = await self.model_crud.get_health_stats()
            
            # 最近模型
            recent_models = await self.model_crud.get_recent_models(limit=5)
            recent_model_responses = [self._convert_to_response(model) for model in recent_models]
            
            # 总数统计
            total_count = sum(platform_stats.values())
            
            return ModelStatsResponse(
                total_count=total_count,
                platform_stats=platform_stats,
                status_stats=status_stats,
                health_stats=health_stats,
                recent_models=recent_model_responses
            )
        except Exception as e:
            raise_business_error(f"获取模型统计信息失败: {str(e)}")

    async def call_model(self, request: ModelCallRequest) -> ModelCallResponse:
        """调用模型（非流式）"""
        start_time = time.time()

        try:
            # 根据模型名称获取配置
            model = await self.model_crud.get_by_name(request.model_name)
            if not model:
                raise_not_found_error(f"模型不存在: {request.model_name}")

            if not model.is_enabled:
                raise_business_error(f"模型已停用: {request.model_name}")

            # 创建模型客户端并调用
            client = ModelClient(model, skip_network_test=True)

            try:
                result = await client.call_model(request.prompt, request.parameters)

                return ModelCallResponse(
                    model_name=request.model_name,
                    response=result.get('response'),
                    response_time_ms=result.get('response_time_ms', 0),
                    success=result.get('success', False),
                    error_message=result.get('error_message')
                )

            finally:
                await client.close()

        except Exception as e:
            response_time_ms = int((time.time() - start_time) * 1000)
            return ModelCallResponse(
                model_name=request.model_name,
                response=None,
                response_time_ms=response_time_ms,
                success=False,
                error_message=str(e)
            )

    async def call_model_stream_response(self, request: ModelCallRequest):
        """调用模型（流式）- 返回格式化的SSE流"""
        import json

        try:
            # 根据模型名称获取配置
            model = await self.model_crud.get_by_name(request.model_name)
            if not model:
                error_chunk = {
                    'type': 'error',
                    'error_message': f"模型不存在: {request.model_name}",
                    'response_time_ms': 0
                }
                yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
                return

            if not model.is_enabled:
                error_chunk = {
                    'type': 'error',
                    'error_message': f"模型已停用: {request.model_name}",
                    'response_time_ms': 0
                }
                yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
                return

            # 创建模型客户端并进行流式调用
            client = ModelClient(model, skip_network_test=True)

            try:
                async for chunk in client.call_model_stream(request.prompt, request.parameters):
                    # 将每个数据块转换为 SSE 格式
                    sse_data = f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                    yield sse_data

            finally:
                await client.close()

        except Exception as e:
            error_chunk = {
                'type': 'error',
                'error_message': str(e),
                'response_time_ms': 0
            }
            yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"

    async def get_supported_platforms(self) -> List[SupportedPlatformResponse]:
        """获取支持的平台类型"""
        # 硬编码支持的平台（简化版本）
        supported_platforms = [
            {
                "platform": "local",
                "display_name": "本地模型",
                "description": "本地部署的AI模型",
                "default_timeout": 30,
                "required_fields": ["api_url", "model_name"],
                "optional_fields": ["api_key"],
                "icon": "server",
                "category": "本地部署"
            },
            {
                "platform": "deepseek",
                "display_name": "DeepSeek",
                "description": "DeepSeek AI模型平台",
                "default_timeout": 30,
                "required_fields": ["api_key", "model_name"],
                "optional_fields": ["max_tokens", "prompt"],
                "icon": "deepseek",
                "category": "商业API"
            },
            {
                "platform": "qwen",
                "display_name": "通义千问",
                "description": "阿里巴巴通义千问模型",
                "default_timeout": 30,
                "required_fields": ["api_key", "model_name"],
                "optional_fields": ["max_tokens", "prompt"],
                "icon": "qwen",
                "category": "商业API"
            },
            {
                "platform": "openai",
                "display_name": "OpenAI",
                "description": "OpenAI GPT模型",
                "default_timeout": 30,
                "required_fields": ["api_key", "model_name"],
                "optional_fields": ["max_tokens", "prompt"],
                "icon": "openai",
                "category": "商业API"
            }
        ]
        
        return [SupportedPlatformResponse(**platform_info) for platform_info in supported_platforms]

    async def _simple_health_check(self, model, timeout_seconds: int) -> Dict[str, Any]:
        """模型健康检查实现"""
        try:
            start_time = time.time()

            # 创建模型客户端进行健康检查（使用传入的超时时间）
            client = ModelClient(model, skip_network_test=True)
            # 注意：这里可以根据需要使用timeout_seconds参数

            try:
                # 使用简单的测试提示词进行健康检查
                test_prompt = "Hello"
                result = await client.call_model(test_prompt, {"max_tokens": 10})

                response_time_ms = int((time.time() - start_time) * 1000)

                return {
                    "is_healthy": result.get('success', False),
                    "response_time_ms": response_time_ms,
                    "error_message": result.get('error_message') if not result.get('success', False) else None
                }

            finally:
                await client.close()

        except Exception as e:
            response_time_ms = int((time.time() - start_time) * 1000)
            return {
                "is_healthy": False,
                "response_time_ms": response_time_ms,
                "error_message": str(e)
            }

    def _convert_to_response(self, model) -> ModelConfigResponse:
        """将ModelConfig对象转换为ModelConfigResponse"""
        return ModelConfigResponse(
            id=model.id,
            name=model.name,
            platform=model.platform,
            description=model.description,
            api_url=model.api_url,
            timeout_seconds=model.timeout_seconds,
            model_name=model.model_name,
            max_tokens=model.max_tokens,
            prompt=model.prompt,
            status=model.status,
            health_status=model.health_status,
            last_health_check=model.last_health_check,
            api_key=model.mask_api_key(),  # 脱敏显示
            created_at=model.created_at,
            updated_at=model.updated_at,
            created_by=model.created_by,
            updated_by=model.updated_by,
            created_by_name=None,  # 将在_resolve_user_names中设置
            updated_by_name=None,  # 将在_resolve_user_names中设置
        )
