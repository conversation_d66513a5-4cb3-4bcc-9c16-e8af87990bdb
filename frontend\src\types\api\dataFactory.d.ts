/**
 * 数据工厂相关类型定义
 */
declare namespace Api {
  namespace DataFactory {
    
    // ==================== 通用类型 ====================
    
    /** 字段配置 */
    interface FieldConfig {
      /** 字段名称 */
      name: string
      /** 字段类型 */
      type: 'string' | 'integer' | 'decimal' | 'boolean' | 'date' | 'datetime'
      /** 生成器类型 */
      generator: 'uuid' | 'name' | 'phone' | 'email' | 'range' | 'sequence' | 'date'
      /** 字段描述 */
      description?: string
      /** 是否必填 */
      required: boolean
      /** 生成器选项 */
      options?: Record<string, any>
    }

    /** 导出配置 */
    interface ExportConfig {
      /** 导出格式 */
      format: 'json' | 'csv' | 'excel'
      /** 格式特定配置 */
      options?: Record<string, any>
    }

    // ==================== 数据模型相关 ====================

    /** 数据模型基础信息 */
    interface ModelBase {
      /** 模型名称 */
      name: string
      /** 模型描述 */
      description?: string
      /** 版本号 */
      version: string
      /** 模型分类 */
      category?: string
      /** 标签列表 */
      tags?: string[]
      /** 字段配置列表 */
      fields_config: FieldConfig[]
      /** 状态 */
      status: '1' | '2'
    }

    /** 数据模型信息 */
    interface ModelInfo extends ModelBase {
      /** 模型ID */
      id: number
      /** 使用次数 */
      usage_count: number
      /** 创建时间 */
      created_at: string
      /** 更新时间 */
      updated_at: string
      /** 创建者 */
      created_by: string
      /** 更新者 */
      updated_by: string
    }

    /** 创建数据模型参数 */
    interface ModelCreateData extends ModelBase {}

    /** 更新数据模型参数 */
    interface ModelUpdateData extends Partial<ModelBase> {}

    /** 数据模型列表查询参数 */
    interface ModelListParams extends Api.Common.PaginatingSearchParams {
      /** 分类筛选 */
      category?: string
      /** 关键词搜索 */
      keyword?: string
      /** 状态筛选 */
      status?: '1' | '2'
    }

    /** 数据模型响应 */
    interface ModelResponse extends Api.Common.CommonResponse<ModelInfo> {}

    /** 数据模型列表响应 */
    interface ModelListResponse extends Api.Common.PaginationResponse<ModelInfo> {}

    /** 数据预览响应 */
    interface ModelPreviewResponse extends Api.Common.CommonResponse<{
      model_id: number
      model_name: string
      preview_data: Record<string, any>[]
      field_count: number
      record_count: number
    }> {}

    /** 分类列表响应 */
    interface CategoryListResponse extends Api.Common.CommonResponse<string[]> {}

    /** 模型统计响应 */
    interface ModelStatisticsResponse extends Api.Common.CommonResponse<{
      total_models: number
      active_models: number
      inactive_models: number
      category_distribution: Record<string, number>
      total_usage: number
    }> {}

    // ==================== 生成任务相关 ====================

    /** 生成任务基础信息 */
    interface TaskBase {
      /** 任务名称 */
      name: string
      /** 任务描述 */
      description?: string
      /** 数据模型ID */
      model_id: number
      /** 生成数据条数 */
      record_count: number
      /** 导出格式 */
      export_format: 'json' | 'csv' | 'excel'
      /** 导出配置 */
      export_config?: Record<string, any>
    }

    /** 生成任务信息 */
    interface TaskInfo extends TaskBase {
      /** 任务ID */
      id: number
      /** 任务状态 */
      status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
      /** 执行进度 */
      progress: number
      /** 开始执行时间 */
      started_at?: string
      /** 完成时间 */
      completed_at?: string
      /** 执行耗时（秒） */
      execution_time?: number
      /** 结果文件路径 */
      result_file_path?: string
      /** 结果文件大小（字节） */
      result_file_size?: number
      /** 错误信息 */
      error_message?: string
      /** 内存使用量（MB） */
      memory_usage?: number
      /** CPU使用率（%） */
      cpu_usage?: number
      /** 创建时间 */
      created_at: string
      /** 更新时间 */
      updated_at: string
      /** 创建者 */
      created_by: string
      /** 更新者 */
      updated_by: string
    }

    /** 创建生成任务参数 */
    interface TaskCreateData extends TaskBase {}

    /** 更新生成任务参数 */
    interface TaskUpdateData extends Partial<{
      name: string
      description: string
      status: string
      progress: number
      error_message: string
    }> {}

    /** 生成任务列表查询参数 */
    interface TaskListParams extends Api.Common.PaginatingSearchParams {
      /** 状态筛选 */
      status?: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
      /** 模型ID筛选 */
      model_id?: number
    }

    /** 生成任务响应 */
    interface TaskResponse extends Api.Common.CommonResponse<TaskInfo> {}

    /** 生成任务列表响应 */
    interface TaskListResponse extends Api.Common.PaginationResponse<TaskInfo> {}

    /** 任务统计响应 */
    interface TaskStatisticsResponse extends Api.Common.CommonResponse<{
      total_tasks: number
      pending_tasks: number
      running_tasks: number
      completed_tasks: number
      failed_tasks: number
      cancelled_tasks: number
      success_rate: number
      avg_execution_time?: number
    }> {}

    /** 正在运行的任务响应 */
    interface RunningTasksResponse extends Api.Common.CommonResponse<TaskInfo[]> {}

    /** 导出格式响应 */
    interface ExportFormatsResponse extends Api.Common.CommonResponse<{
      format: string
      name: string
      description: string
      extension: string
      mime_type: string
    }[]> {}

    // ==================== 生成器相关 ====================

    /** 生成器信息 */
    interface GeneratorInfo {
      /** 生成器类型 */
      type: string
      /** 生成器名称 */
      name: string
      /** 生成器描述 */
      description: string
      /** 支持的数据类型 */
      supported_types: string[]
      /** 配置选项 */
      options?: {
        name: string
        type: string
        description: string
        required: boolean
        default?: any
      }[]
    }

    // ==================== 表单相关 ====================

    /** 数据模型表单 */
    interface ModelForm extends ModelBase {}

    /** 生成任务表单 */
    interface TaskForm extends TaskBase {}

    /** 字段配置表单 */
    interface FieldForm extends FieldConfig {}
  }
}
