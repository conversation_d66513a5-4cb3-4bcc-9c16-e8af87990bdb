"""
Kafka连接客户端
支持Apache Kafka的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional, List

from .base_client import BaseClient, ConnectionResult

try:
    from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
    from aiokafka.admin import AIOKafkaAdminClient, NewTopic
    from aiokafka.errors import KafkaError, KafkaConnectionError
    KAFKA_AVAILABLE = True
except ImportError:
    KAFKA_AVAILABLE = False


class KafkaClient(BaseClient):
    """
    Kafka连接客户端
    支持Apache Kafka的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Kafka客户端
        
        Args:
            config: Kafka连接配置
                - bootstrap_servers: Kafka服务器列表 (默认: localhost:9092)
                - security_protocol: 安全协议 (默认: PLAINTEXT)
                - sasl_mechanism: SASL机制 (可选)
                - sasl_plain_username: SASL用户名 (可选)
                - sasl_plain_password: SASL密码 (可选)
                - ssl_context: SSL上下文 (可选)
        """
        super().__init__(config)
        
        if not KAFKA_AVAILABLE:
            raise ImportError("aiokafka库未安装，请运行: pip install aiokafka")
        
        # 设置默认值
        self.bootstrap_servers = config.get('bootstrap_servers', 'localhost:9092')
        if isinstance(self.bootstrap_servers, str):
            self.bootstrap_servers = [self.bootstrap_servers]
        
        self.security_protocol = config.get('security_protocol', 'PLAINTEXT')
        self.sasl_mechanism = config.get('sasl_mechanism')
        self.sasl_plain_username = config.get('sasl_plain_username')
        self.sasl_plain_password = config.get('sasl_plain_password')
        self.ssl_context = config.get('ssl_context')
        
        self.admin_client = None
        self.producer = None
        self.consumer = None

    def _get_client_config(self) -> Dict[str, Any]:
        """获取客户端配置"""
        config = {
            'bootstrap_servers': self.bootstrap_servers,
            'security_protocol': self.security_protocol,
        }
        
        if self.sasl_mechanism:
            config['sasl_mechanism'] = self.sasl_mechanism
        if self.sasl_plain_username:
            config['sasl_plain_username'] = self.sasl_plain_username
        if self.sasl_plain_password:
            config['sasl_plain_password'] = self.sasl_plain_password
        if self.ssl_context:
            config['ssl_context'] = self.ssl_context
            
        return config

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立Kafka连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 创建管理客户端
            client_config = self._get_client_config()
            self.admin_client = AIOKafkaAdminClient(**client_config)
            
            # 启动管理客户端并测试连接
            await asyncio.wait_for(self.admin_client.start(), timeout=timeout)
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到Kafka集群 {','.join(self.bootstrap_servers)}",
                duration=duration,
                details={
                    "bootstrap_servers": self.bootstrap_servers,
                    "security_protocol": self.security_protocol,
                    "sasl_mechanism": self.sasl_mechanism or "无"
                }
            )
            
        except asyncio.TimeoutError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kafka连接超时 ({timeout}秒)",
                duration=duration,
                details={"error_type": "TimeoutError"}
            )
        except KafkaConnectionError as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kafka连接失败: {str(e)}",
                duration=duration,
                details={"error_type": "KafkaConnectionError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"Kafka连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开Kafka连接"""
        try:
            if self.consumer:
                await self.consumer.stop()
                self.consumer = None
            if self.producer:
                await self.producer.stop()
                self.producer = None
            if self.admin_client:
                await self.admin_client.close()
                self.admin_client = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试Kafka连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        # 如果已连接，先断开
        if self.is_connected:
            await self.disconnect()
        
        # 尝试连接
        result = await self.connect(timeout)
        
        # 如果连接成功，获取集群信息
        if result.success:
            try:
                # 获取集群元数据
                metadata = await self.admin_client.describe_cluster()
                
                if metadata:
                    result.details.update({
                        "cluster_id": getattr(metadata, 'cluster_id', '未知'),
                        "controller_id": getattr(metadata, 'controller_id', '未知'),
                        "brokers_count": len(getattr(metadata, 'brokers', []))
                    })
                
                # 获取topic列表
                topics = await self.list_topics()
                result.details["topics_count"] = len(topics)
                result.details["sample_topics"] = topics[:5]  # 显示前5个topic
                
                result.message += f" (集群ID: {result.details.get('cluster_id', '未知')})"
                
            except Exception as e:
                result.details["metadata_test"] = f"获取集群信息失败: {str(e)}"
        
        # 测试完成后断开连接
        if result.success:
            await self.disconnect()
        
        return result

    async def list_topics(self) -> List[str]:
        """
        获取topic列表
        
        Returns:
            List[str]: topic名称列表
        """
        if not self.admin_client:
            raise RuntimeError("Kafka未连接")
        
        try:
            metadata = await self.admin_client.list_topics()
            return list(metadata)
        except Exception as e:
            raise RuntimeError(f"获取topic列表失败: {str(e)}")

    async def create_topic(self, topic_name: str, num_partitions: int = 1, replication_factor: int = 1) -> Dict[str, Any]:
        """
        创建topic
        
        Args:
            topic_name: topic名称
            num_partitions: 分区数
            replication_factor: 副本因子
            
        Returns:
            Dict: 创建结果
        """
        if not self.admin_client:
            raise RuntimeError("Kafka未连接")
        
        try:
            topic = NewTopic(
                name=topic_name,
                num_partitions=num_partitions,
                replication_factor=replication_factor
            )
            
            await self.admin_client.create_topics([topic])
            
            return {
                "success": True,
                "message": f"Topic '{topic_name}' 创建成功",
                "topic_name": topic_name,
                "num_partitions": num_partitions,
                "replication_factor": replication_factor
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "topic_name": topic_name
            }

    async def delete_topic(self, topic_name: str) -> Dict[str, Any]:
        """
        删除topic
        
        Args:
            topic_name: topic名称
            
        Returns:
            Dict: 删除结果
        """
        if not self.admin_client:
            raise RuntimeError("Kafka未连接")
        
        try:
            await self.admin_client.delete_topics([topic_name])
            
            return {
                "success": True,
                "message": f"Topic '{topic_name}' 删除成功",
                "topic_name": topic_name
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "topic_name": topic_name
            }

    async def produce_message(self, topic: str, message: str, key: str = None) -> Dict[str, Any]:
        """
        发送消息到topic
        
        Args:
            topic: topic名称
            message: 消息内容
            key: 消息key (可选)
            
        Returns:
            Dict: 发送结果
        """
        try:
            if not self.producer:
                client_config = self._get_client_config()
                self.producer = AIOKafkaProducer(**client_config)
                await self.producer.start()
            
            # 发送消息
            future = await self.producer.send(
                topic,
                message.encode('utf-8'),
                key=key.encode('utf-8') if key else None
            )
            
            record_metadata = await future
            
            return {
                "success": True,
                "message": "消息发送成功",
                "topic": record_metadata.topic,
                "partition": record_metadata.partition,
                "offset": record_metadata.offset
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "topic": topic
            }

    async def consume_messages(self, topic: str, max_messages: int = 10, timeout: int = 5) -> Dict[str, Any]:
        """
        从topic消费消息
        
        Args:
            topic: topic名称
            max_messages: 最大消息数
            timeout: 超时时间(秒)
            
        Returns:
            Dict: 消费结果
        """
        try:
            client_config = self._get_client_config()
            consumer = AIOKafkaConsumer(
                topic,
                group_id="test_consumer_group",
                auto_offset_reset='earliest',
                **client_config
            )
            
            await consumer.start()
            
            messages = []
            start_time = time.time()
            
            try:
                async for msg in consumer:
                    messages.append({
                        "topic": msg.topic,
                        "partition": msg.partition,
                        "offset": msg.offset,
                        "key": msg.key.decode('utf-8') if msg.key else None,
                        "value": msg.value.decode('utf-8') if msg.value else None,
                        "timestamp": msg.timestamp
                    })
                    
                    if len(messages) >= max_messages:
                        break
                    
                    if time.time() - start_time > timeout:
                        break
                        
            finally:
                await consumer.stop()
            
            return {
                "success": True,
                "messages": messages,
                "message_count": len(messages),
                "topic": topic
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "topic": topic
            }

    async def get_topic_info(self, topic_name: str) -> Dict[str, Any]:
        """
        获取topic信息
        
        Args:
            topic_name: topic名称
            
        Returns:
            Dict: topic信息
        """
        if not self.admin_client:
            raise RuntimeError("Kafka未连接")
        
        try:
            # 获取topic元数据
            metadata = await self.admin_client.describe_topics([topic_name])
            
            if topic_name in metadata:
                topic_metadata = metadata[topic_name]
                
                return {
                    "topic_name": topic_name,
                    "partitions": len(topic_metadata.partitions),
                    "partition_info": [
                        {
                            "partition": p.partition,
                            "leader": p.leader,
                            "replicas": p.replicas,
                            "isr": p.isr
                        }
                        for p in topic_metadata.partitions.values()
                    ]
                }
            else:
                return {"error": f"Topic '{topic_name}' 不存在"}
                
        except Exception as e:
            return {"error": str(e)}
