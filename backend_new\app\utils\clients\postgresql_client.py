"""
PostgreSQL数据库连接客户端
支持PostgreSQL数据库的连接和测试
"""
import time
import asyncio
from typing import Dict, Any, Optional, List

from .base_client import BaseClient, ConnectionResult

try:
    import asyncpg
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False


class PostgreSQLClient(BaseClient):
    """
    PostgreSQL数据库连接客户端
    支持PostgreSQL数据库的连接和测试
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化PostgreSQL客户端
        
        Args:
            config: PostgreSQL连接配置
                - host: PostgreSQL主机地址 (默认: localhost)
                - port: PostgreSQL端口 (默认: 5432)
                - user/username: 用户名 (默认: postgres)
                - password: 密码
                - database/db: 数据库名 (默认: postgres)
                - ssl: SSL模式 (默认: prefer)
        """
        super().__init__(config)
        
        if not POSTGRESQL_AVAILABLE:
            raise ImportError("asyncpg库未安装，请运行: pip install asyncpg")
        
        # 设置默认值
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 5432)
        self.user = config.get('user') or config.get('username', 'postgres')
        self.password = config.get('password', '')
        self.database = config.get('database') or config.get('db', 'postgres')
        self.ssl = config.get('ssl', 'prefer')
        
        self.pg_connection = None

    async def connect(self, timeout: int = 10) -> ConnectionResult:
        """
        建立PostgreSQL连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 连接结果
        """
        start_time = time.time()
        
        try:
            # 建立PostgreSQL连接
            self.pg_connection = await asyncio.wait_for(
                asyncpg.connect(
                    host=self.host,
                    port=self.port,
                    user=self.user,
                    password=self.password,
                    database=self.database,
                    ssl=self.ssl
                ),
                timeout=timeout
            )
            
            self.is_connected = True
            duration = time.time() - start_time
            
            return ConnectionResult(
                success=True,
                message=f"成功连接到PostgreSQL数据库 {self.host}:{self.port}",
                duration=duration,
                details={
                    "host": self.host,
                    "port": self.port,
                    "user": self.user,
                    "database": self.database,
                    "ssl": self.ssl
                }
            )
            
        except asyncio.TimeoutError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"PostgreSQL连接超时 ({timeout}秒)",
                duration=duration,
                details={"error_type": "TimeoutError"}
            )
        except asyncpg.InvalidAuthorizationSpecificationError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message="PostgreSQL认证失败 - 用户名或密码错误",
                duration=duration,
                details={"error_type": "AuthenticationError"}
            )
        except asyncpg.InvalidCatalogNameError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"数据库 '{self.database}' 不存在",
                duration=duration,
                details={"error_type": "DatabaseNotFoundError"}
            )
        except asyncpg.CannotConnectNowError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message="PostgreSQL服务器当前无法接受连接",
                duration=duration,
                details={"error_type": "ServerUnavailableError"}
            )
        except ConnectionRefusedError:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message="PostgreSQL连接被拒绝 - 请检查服务器是否运行",
                duration=duration,
                details={"error_type": "ConnectionRefusedError"}
            )
        except Exception as e:
            duration = time.time() - start_time
            return ConnectionResult(
                success=False,
                message=f"PostgreSQL连接异常: {str(e)}",
                duration=duration,
                details={"error_type": type(e).__name__}
            )

    async def disconnect(self) -> None:
        """断开PostgreSQL连接"""
        try:
            if self.pg_connection:
                await self.pg_connection.close()
                self.pg_connection = None
            self.is_connected = False
        except Exception:
            # 忽略断开连接时的异常
            pass

    async def test_connection(self, timeout: int = 10) -> ConnectionResult:
        """
        测试PostgreSQL连接
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            ConnectionResult: 测试结果
        """
        # 如果已连接，先断开
        if self.is_connected:
            await self.disconnect()
        
        # 尝试连接
        result = await self.connect(timeout)
        
        # 如果连接成功，执行一个简单的查询来验证
        if result.success:
            try:
                # 获取PostgreSQL版本
                version = await self.pg_connection.fetchval("SELECT version()")
                if version:
                    # 提取版本号
                    version_parts = version.split()
                    if len(version_parts) >= 2:
                        pg_version = version_parts[1]
                        result.details["postgresql_version"] = pg_version
                        result.message += f" (版本: {pg_version})"
                
                # 获取当前数据库信息
                current_db = await self.pg_connection.fetchval("SELECT current_database()")
                if current_db:
                    result.details["current_database"] = current_db
                
                # 获取当前用户
                current_user = await self.pg_connection.fetchval("SELECT current_user")
                if current_user:
                    result.details["current_user"] = current_user
                
            except Exception as e:
                result.details["query_test"] = f"查询测试失败: {str(e)}"
        
        # 测试完成后断开连接
        if result.success:
            await self.disconnect()
        
        return result

    async def execute_query(self, query: str, *args) -> Dict[str, Any]:
        """
        执行SQL查询
        
        Args:
            query: SQL查询语句
            *args: 查询参数
            
        Returns:
            Dict: 查询结果
        """
        if not self.pg_connection:
            raise RuntimeError("PostgreSQL未连接")
        
        try:
            # 如果是SELECT查询，获取结果
            if query.strip().upper().startswith('SELECT'):
                results = await self.pg_connection.fetch(query, *args)
                
                # 转换为字典列表
                result_list = []
                for record in results:
                    result_list.append(dict(record))
                
                return {
                    "success": True,
                    "results": result_list,
                    "row_count": len(result_list)
                }
            else:
                # 对于INSERT, UPDATE, DELETE等操作
                result = await self.pg_connection.execute(query, *args)
                
                # 提取影响的行数
                affected_rows = 0
                if result.startswith(('INSERT', 'UPDATE', 'DELETE')):
                    parts = result.split()
                    if len(parts) >= 2 and parts[-1].isdigit():
                        affected_rows = int(parts[-1])
                
                return {
                    "success": True,
                    "affected_rows": affected_rows,
                    "result": result
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    async def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库信息
        
        Returns:
            Dict: 数据库信息
        """
        if not self.pg_connection:
            raise RuntimeError("PostgreSQL未连接")
        
        try:
            info = {}
            
            # 获取版本信息
            version = await self.pg_connection.fetchval("SELECT version()")
            if version:
                info["version"] = version
            
            # 获取当前数据库
            current_db = await self.pg_connection.fetchval("SELECT current_database()")
            if current_db:
                info["current_database"] = current_db
            
            # 获取当前用户
            current_user = await self.pg_connection.fetchval("SELECT current_user")
            if current_user:
                info["current_user"] = current_user
            
            # 获取服务器编码
            encoding = await self.pg_connection.fetchval("SHOW server_encoding")
            if encoding:
                info["encoding"] = encoding
            
            # 获取时区
            timezone = await self.pg_connection.fetchval("SHOW timezone")
            if timezone:
                info["timezone"] = timezone
            
            # 获取数据库大小
            try:
                db_size = await self.pg_connection.fetchval(
                    "SELECT pg_size_pretty(pg_database_size($1))", current_db
                )
                if db_size:
                    info["database_size"] = db_size
            except:
                pass
            
            return info
            
        except Exception as e:
            return {"error": str(e)}

    async def list_tables(self, schema: str = 'public') -> List[str]:
        """
        列出指定schema中的表
        
        Args:
            schema: schema名称 (默认: public)
            
        Returns:
            List[str]: 表名列表
        """
        if not self.pg_connection:
            raise RuntimeError("PostgreSQL未连接")
        
        try:
            tables = await self.pg_connection.fetch(
                """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = $1 AND table_type = 'BASE TABLE'
                ORDER BY table_name
                """,
                schema
            )
            
            return [table['table_name'] for table in tables]
            
        except Exception as e:
            raise RuntimeError(f"获取表列表失败: {str(e)}")

    async def get_table_info(self, table_name: str, schema: str = 'public') -> Dict[str, Any]:
        """
        获取表信息
        
        Args:
            table_name: 表名
            schema: schema名称 (默认: public)
            
        Returns:
            Dict: 表信息
        """
        if not self.pg_connection:
            raise RuntimeError("PostgreSQL未连接")
        
        try:
            # 获取列信息
            columns = await self.pg_connection.fetch(
                """
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_schema = $1 AND table_name = $2
                ORDER BY ordinal_position
                """,
                schema, table_name
            )
            
            # 获取行数
            row_count = await self.pg_connection.fetchval(
                f'SELECT COUNT(*) FROM "{schema}"."{table_name}"'
            )
            
            return {
                "table_name": table_name,
                "schema": schema,
                "columns": [dict(col) for col in columns],
                "row_count": row_count
            }
            
        except Exception as e:
            return {"error": str(e)}
