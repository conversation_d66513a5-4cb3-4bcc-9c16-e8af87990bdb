#!/bin/bash
# Celery服务停止脚本 (Linux/macOS)

echo "停止Celery服务..."

# 停止Celery Worker
if [ -f "celery_worker.pid" ]; then
    WORKER_PID=$(cat celery_worker.pid)
    echo "停止Celery Worker (PID: $WORKER_PID)..."
    kill $WORKER_PID 2>/dev/null
    rm -f celery_worker.pid
else
    echo "查找并停止Celery Worker进程..."
    pkill -f "celery_worker.py"
fi

# 停止Celery Beat
if [ -f "celery_beat.pid" ]; then
    BEAT_PID=$(cat celery_beat.pid)
    echo "停止Celery Beat (PID: $BEAT_PID)..."
    kill $BEAT_PID 2>/dev/null
    rm -f celery_beat.pid
else
    echo "查找并停止Celery Beat进程..."
    pkill -f "celery_beat.py"
fi

# 停止Celery Flower
echo "停止Celery Flower..."
pkill -f "celery_monitor.py"

# 清理调度文件
if [ -f "celerybeat-schedule" ]; then
    echo "清理调度文件..."
    rm -f celerybeat-schedule
fi

echo ""
echo "Celery服务已停止！"
