"""
用户数据模型
"""
from sqlalchemy import <PERSON>olean, Column, String, Text, Table, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

# 用户角色关联表
user_roles = Table(
    'user_roles',
    BaseModel.metadata,
    Column('user_id', ForeignKey('user.id'), primary_key=True),
    Column('role_id', ForeignKey('role.id'), primary_key=True)
)


class User(BaseModel):
    """用户模型"""
    __tablename__ = "user"

    # 基础信息
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, index=True, nullable=True, comment="邮箱")
    nickname = Column(String(50), nullable=True, comment="昵称")
    
    # 认证信息
    hashed_password = Column(String(255), nullable=False, comment="密码哈希")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    
    # 扩展信息
    avatar = Column(String(500), nullable=True, comment="头像URL")
    description = Column(Text, nullable=True, comment="个人简介")
    
    # 状态信息
    status = Column(String(10), default="1", comment="用户状态: 1-在线, 2-离线, 3-异常, 4-注销")
    last_login_at = Column(String(50), nullable=True, comment="最后登录时间")
    
    # 关联关系
    roles = relationship("Role", secondary=user_roles, back_populates="users")

    def __repr__(self) -> str:
        return f"<User(id={self.id}, username={self.username})>"

    @property
    def is_authenticated(self) -> bool:
        """检查用户是否已认证"""
        return True

    @property
    def role_names(self) -> list:
        """获取用户角色名称列表"""
        return [role.code for role in self.roles]

    def has_role(self, role_name: str) -> bool:
        """检查用户是否具有指定角色"""
        return role_name in self.role_names


class Role(BaseModel):
    """角色模型"""
    __tablename__ = "role"

    # 基础信息
    name = Column(String(50), unique=True, index=True, nullable=False, comment="角色名称")
    code = Column(String(50), unique=True, index=True, nullable=False, comment="角色代码")
    description = Column(Text, nullable=True, comment="角色描述")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 关联关系
    users = relationship("User", secondary=user_roles, back_populates="roles")

    def __repr__(self) -> str:
        return f"<Role(id={self.id}, name={self.name})>"
